{% doc %}
  @prompt
    Create an elegant wedding section with image right, text left, sophisticated floral design. Use refined CSS techniques to create beautiful rose and flower shapes with gradients and shadows. Add delicate petal elements using advanced CSS styling, elegant floral borders, and subtle flower decorations. Focus on roses, cherry blossoms, and peonies only - no leaves. Use soft pink background with sophisticated floral patterns. Create premium-looking flower decorations with proper styling and elegant positioning.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .wedding-block-{{ ai_gen_id }} {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: {{ block.settings.background_color }};
    padding: 60px 40px;
    position: relative;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
  }

  @media screen and (max-width: 749px) {
    .wedding-block-{{ ai_gen_id }} {
      flex-direction: column-reverse;
      padding: 40px 20px;
    }
  }

  .wedding-content-{{ ai_gen_id }} {
    flex: 1;
    padding-right: 40px;
    position: relative;
    z-index: 2;
  }

  @media screen and (max-width: 749px) {
    .wedding-content-{{ ai_gen_id }} {
      padding-right: 0;
      padding-top: 30px;
      text-align: center;
    }
  }

  .wedding-image-container-{{ ai_gen_id }} {
    flex: 1;
    position: relative;
    z-index: 2;
  }

  .wedding-image-{{ ai_gen_id }} {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .wedding-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    aspect-ratio: 3/4;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .wedding-image-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    border-radius: 8px;
  }

  .wedding-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    color: {{ block.settings.heading_color }};
    margin-bottom: 20px;
    font-weight: 500;
    line-height: 1.2;
  }

  .wedding-subtitle-{{ ai_gen_id }} {
    font-size: {{ block.settings.subtitle_size }}px;
    color: {{ block.settings.subtitle_color }};
    margin-bottom: 30px;
    font-weight: 300;
  }

  .wedding-description-{{ ai_gen_id }} {
    font-size: {{ block.settings.text_size }}px;
    color: {{ block.settings.text_color }};
    line-height: 1.6;
    margin-bottom: 30px;
  }

  .wedding-button-{{ ai_gen_id }} {
    display: inline-block;
    padding: 12px 30px;
    background-color: {{ block.settings.button_color }};
    color: {{ block.settings.button_text_color }};
    text-decoration: none;
    border-radius: 30px;
    font-size: 16px;
    transition: all 0.3s ease;
  }

  .wedding-button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_color }};
  }

  /* Flower Decorations */
  .rose-{{ ai_gen_id }} {
    position: absolute;
    z-index: 1;
  }

  /* Rose 1 - Top Left */
  .rose-1-{{ ai_gen_id }} {
    top: -20px;
    left: -20px;
    width: 100px;
    height: 100px;
    opacity: 0.7;
  }

  /* Rose 2 - Bottom Right */
  .rose-2-{{ ai_gen_id }} {
    bottom: -20px;
    right: -20px;
    width: 120px;
    height: 120px;
    opacity: 0.6;
  }

  /* Rose 3 - Top Right */
  .rose-3-{{ ai_gen_id }} {
    top: 10%;
    right: 15%;
    width: 60px;
    height: 60px;
    opacity: 0.5;
  }

  /* Rose 4 - Bottom Left */
  .rose-4-{{ ai_gen_id }} {
    bottom: 10%;
    left: 20%;
    width: 70px;
    height: 70px;
    opacity: 0.4;
  }

  /* Cherry Blossom 1 */
  .cherry-1-{{ ai_gen_id }} {
    top: 15%;
    left: 10%;
    width: 50px;
    height: 50px;
    opacity: 0.6;
  }

  /* Cherry Blossom 2 */
  .cherry-2-{{ ai_gen_id }} {
    bottom: 20%;
    right: 25%;
    width: 40px;
    height: 40px;
    opacity: 0.5;
  }

  /* Peony 1 */
  .peony-1-{{ ai_gen_id }} {
    top: 30%;
    right: 5%;
    width: 80px;
    height: 80px;
    opacity: 0.6;
  }

  /* Peony 2 */
  .peony-2-{{ ai_gen_id }} {
    bottom: 5%;
    left: 5%;
    width: 90px;
    height: 90px;
    opacity: 0.5;
  }

  /* Rose Styles */
  .rose-inner-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .rose-petal-{{ ai_gen_id }} {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, {{ block.settings.flower_color_1 }}, {{ block.settings.flower_color_2 }});
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }

  /* Cherry Blossom Styles */
  .cherry-inner-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .cherry-petal-{{ ai_gen_id }} {
    position: absolute;
    width: 40%;
    height: 40%;
    background: radial-gradient(circle at 30% 30%, {{ block.settings.flower_color_3 }}, {{ block.settings.flower_color_1 }});
    border-radius: 50% 50% 0 50%;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
  }

  /* Peony Styles */
  .peony-inner-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .peony-petal-{{ ai_gen_id }} {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 40% 40%, {{ block.settings.flower_color_2 }}, {{ block.settings.flower_color_3 }});
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.08);
  }
{% endstyle %}

<div class="wedding-block-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <!-- Rose 1 -->
  <div class="rose-{{ ai_gen_id }} rose-1-{{ ai_gen_id }}">
    <div class="rose-inner-{{ ai_gen_id }}">
      {% for i in (1..12) %}
        {% assign size = forloop.index | times: 8 %}
        {% assign top = 50 | minus: size | divided_by: 2 %}
        {% assign left = 50 | minus: size | divided_by: 2 %}
        {% assign rotate = forloop.index | times: 30 %}
        <div class="rose-petal-{{ ai_gen_id }}" style="width: {{ size }}%; height: {{ size }}%; top: {{ top }}%; left: {{ left }}%; transform: rotate({{ rotate }}deg);"></div>
      {% endfor %}
    </div>
  </div>

  <!-- Rose 2 -->
  <div class="rose-{{ ai_gen_id }} rose-2-{{ ai_gen_id }}">
    <div class="rose-inner-{{ ai_gen_id }}">
      {% for i in (1..12) %}
        {% assign size = forloop.index | times: 8 %}
        {% assign top = 50 | minus: size | divided_by: 2 %}
        {% assign left = 50 | minus: size | divided_by: 2 %}
        {% assign rotate = forloop.index | times: 30 %}
        <div class="rose-petal-{{ ai_gen_id }}" style="width: {{ size }}%; height: {{ size }}%; top: {{ top }}%; left: {{ left }}%; transform: rotate({{ rotate }}deg);"></div>
      {% endfor %}
    </div>
  </div>

  <!-- Cherry Blossom 1 -->
  <div class="rose-{{ ai_gen_id }} cherry-1-{{ ai_gen_id }}">
    <div class="cherry-inner-{{ ai_gen_id }}">
      {% for i in (1..5) %}
        {% assign rotate = forloop.index | minus: 1 | times: 72 %}
        <div class="cherry-petal-{{ ai_gen_id }}" style="top: 30%; left: 30%; transform: rotate({{ rotate }}deg) translateX(60%);"></div>
      {% endfor %}
      <div class="cherry-petal-{{ ai_gen_id }}" style="top: 30%; left: 30%; width: 30%; height: 30%; border-radius: 50%; background: {{ block.settings.flower_color_3 }}; transform: none;"></div>
    </div>
  </div>

  <!-- Cherry Blossom 2 -->
  <div class="rose-{{ ai_gen_id }} cherry-2-{{ ai_gen_id }}">
    <div class="cherry-inner-{{ ai_gen_id }}">
      {% for i in (1..5) %}
        {% assign rotate = forloop.index | minus: 1 | times: 72 %}
        <div class="cherry-petal-{{ ai_gen_id }}" style="top: 30%; left: 30%; transform: rotate({{ rotate }}deg) translateX(60%);"></div>
      {% endfor %}
      <div class="cherry-petal-{{ ai_gen_id }}" style="top: 30%; left: 30%; width: 30%; height: 30%; border-radius: 50%; background: {{ block.settings.flower_color_3 }}; transform: none;"></div>
    </div>
  </div>

  <!-- Peony 1 -->
  <div class="rose-{{ ai_gen_id }} peony-1-{{ ai_gen_id }}">
    <div class="peony-inner-{{ ai_gen_id }}">
      {% for i in (1..20) %}
        {% assign size = forloop.index | times: 4 | plus: 10 %}
        {% assign top = 50 | minus: size | divided_by: 2 %}
        {% assign left = 50 | minus: size | divided_by: 2 %}
        {% assign rotate = forloop.index | times: 18 %}
        <div class="peony-petal-{{ ai_gen_id }}" style="width: {{ size }}%; height: {{ size }}%; top: {{ top }}%; left: {{ left }}%; transform: rotate({{ rotate }}deg);"></div>
      {% endfor %}
    </div>
  </div>

  <!-- Peony 2 -->
  <div class="rose-{{ ai_gen_id }} peony-2-{{ ai_gen_id }}">
    <div class="peony-inner-{{ ai_gen_id }}">
      {% for i in (1..20) %}
        {% assign size = forloop.index | times: 4 | plus: 10 %}
        {% assign top = 50 | minus: size | divided_by: 2 %}
        {% assign left = 50 | minus: size | divided_by: 2 %}
        {% assign rotate = forloop.index | times: 18 %}
        <div class="peony-petal-{{ ai_gen_id }}" style="width: {{ size }}%; height: {{ size }}%; top: {{ top }}%; left: {{ left }}%; transform: rotate({{ rotate }}deg);"></div>
      {% endfor %}
    </div>
  </div>

  <!-- Content Area -->
  <div class="wedding-content-{{ ai_gen_id }}">
    <h2 class="wedding-title-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
    <h3 class="wedding-subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</h3>
    <div class="wedding-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
    {% if block.settings.button_text != blank and block.settings.button_link != blank %}
      <a href="{{ block.settings.button_link }}" class="wedding-button-{{ ai_gen_id }}">{{ block.settings.button_text }}</a>
    {% endif %}
  </div>

  <!-- Image Area -->
  <div class="wedding-image-container-{{ ai_gen_id }}">
    {% if block.settings.image %}
      <img 
        src="{{ block.settings.image | image_url: width: 800 }}" 
        alt="{{ block.settings.image.alt | escape }}" 
        loading="lazy"
        class="wedding-image-{{ ai_gen_id }}"
        width="{{ block.settings.image.width }}"
        height="{{ block.settings.image.height }}"
      >
    {% else %}
      <div class="wedding-image-placeholder-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Elegant Wedding",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Celebrate Your Special Day"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Elegant Wedding Collection"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Discover our exquisite collection of wedding dresses, accessories, and more to make your special day truly unforgettable. Our elegant designs are crafted with love and attention to detail.</p>"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "Explore Collection"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#FFF5F7"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "#F5A9B8"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "button_hover_color",
      "label": "Button hover color",
      "default": "#E5899B"
    },
    {
      "type": "header",
      "content": "Flower Decorations"
    },
    {
      "type": "color",
      "id": "flower_color_1",
      "label": "Flower color 1",
      "default": "#F5A9B8"
    },
    {
      "type": "color",
      "id": "flower_color_2",
      "label": "Flower color 2",
      "default": "#FFD1DC"
    },
    {
      "type": "color",
      "id": "flower_color_3",
      "label": "Flower color 3",
      "default": "#FFECF0"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 36
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 14,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 20
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 12
    }
  ],
  "presets": [
    {
      "name": "Elegant Wedding"
    }
  ]
}
{% endschema %}