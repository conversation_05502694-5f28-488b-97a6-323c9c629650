{% doc %}
  @prompt
    Create a booking form with email notification settings that allows the merchant to specify an email address to receive booking notifications

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-booking-form-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
    padding: {{ block.settings.form_padding }}px;
    background-color: {{ block.settings.form_background }};
    border-radius: {{ block.settings.form_border_radius }}px;
  }

  .ai-booking-form__title-{{ ai_gen_id }} {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.text_color }};
    text-align: {{ block.settings.title_alignment }};
  }

  .ai-booking-form__field-{{ ai_gen_id }} {
    margin-bottom: 16px;
  }

  .ai-booking-form__label-{{ ai_gen_id }} {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: {{ block.settings.text_color }};
  }

  .ai-booking-form__input-{{ ai_gen_id }},
  .ai-booking-form__select-{{ ai_gen_id }},
  .ai-booking-form__textarea-{{ ai_gen_id }} {
    width: 100%;
    padding: 12px;
    border: 1px solid {{ block.settings.input_border_color }};
    border-radius: {{ block.settings.input_border_radius }}px;
    background-color: {{ block.settings.input_background }};
    color: {{ block.settings.text_color }};
  }

  .ai-booking-form__textarea-{{ ai_gen_id }} {
    min-height: 100px;
    resize: vertical;
  }

  .ai-booking-form__button-{{ ai_gen_id }} {
    display: block;
    width: 100%;
    padding: 14px;
    margin-top: 20px;
    background-color: {{ block.settings.button_background }};
    color: {{ block.settings.button_text_color }};
    border: none;
    border-radius: {{ block.settings.button_border_radius }}px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .ai-booking-form__button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_background }};
  }

  .ai-booking-form__success-{{ ai_gen_id }} {
    padding: 12px;
    margin-top: 20px;
    background-color: {{ block.settings.success_background }};
    color: {{ block.settings.success_text_color }};
    border-radius: 4px;
  }

  .ai-booking-form__error-{{ ai_gen_id }} {
    padding: 12px;
    margin-top: 20px;
    background-color: {{ block.settings.error_background }};
    color: {{ block.settings.error_text_color }};
    border-radius: 4px;
  }

  .ai-booking-form__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  @media screen and (max-width: 767px) {
    .ai-booking-form__grid-{{ ai_gen_id }} {
      grid-template-columns: 1fr;
    }
  }
{% endstyle %}

<div class="ai-booking-form-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.form_title != blank %}
    <h2 class="ai-booking-form__title-{{ ai_gen_id }}">{{ block.settings.form_title }}</h2>
  {% endif %}

  {% form 'contact' %}
    <input type="hidden" name="contact[booking_request]" value="true">
    <input type="hidden" name="contact[notification_email]" value="{{ block.settings.notification_email }}">
    <!-- Hidden fields for page information -->
    <input type="hidden" name="contact[source_page]" value="{% if page.handle %}{{ page.handle }}{% elsif request.page_type == 'index' %}homepage{% elsif request.page_type == 'product' %}{{ product.handle }}{% elsif request.page_type == 'collection' %}{{ collection.handle }}{% else %}{{ request.page_type }}{% endif %}">
    <input type="hidden" name="contact[page_url]" value="{{ request.origin }}{{ request.path }}">
    <input type="hidden" name="contact[page_name]" value="{% if page.title %}{{ page.title }}{% elsif request.page_type == 'index' %}{{ shop.name }} - Homepage{% elsif request.page_type == 'product' %}{{ product.title }}{% elsif request.page_type == 'collection' %}{{ collection.title }}{% else %}{{ page_title }}{% endif %}"

    <div class="ai-booking-form__grid-{{ ai_gen_id }}">
      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="BookingName-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.name_label }}</label>
        <input 
          type="text" 
          id="BookingName-{{ ai_gen_id }}" 
          name="contact[name]" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
        >
      </div>

      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="BookingEmail-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.email_label }}</label>
        <input 
          type="email" 
          id="BookingEmail-{{ ai_gen_id }}" 
          name="contact[email]" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
        >
      </div>
    </div>

    <div class="ai-booking-form__grid-{{ ai_gen_id }}">
      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="BookingPhone-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.phone_label }}</label>
        <input 
          type="tel" 
          id="BookingPhone-{{ ai_gen_id }}" 
          name="contact[phone]" 
          class="ai-booking-form__input-{{ ai_gen_id }}"
        >
      </div>

      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="BookingDate-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.date_label }}</label>
        <input 
          type="date" 
          id="BookingDate-{{ ai_gen_id }}" 
          name="contact[booking_date]" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
        >
      </div>
    </div>

    <div class="ai-booking-form__grid-{{ ai_gen_id }}">
      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="BookingTime-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.time_label }}</label>
        <input 
          type="time" 
          id="BookingTime-{{ ai_gen_id }}" 
          name="contact[booking_time]" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
        >
      </div>

      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="BookingGuests-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.guests_label }}</label>
        <select 
          id="BookingGuests-{{ ai_gen_id }}" 
          name="contact[guests]" 
          class="ai-booking-form__select-{{ ai_gen_id }}" 
          required
        >
          {% for i in (1..block.settings.max_guests) %}
            <option value="{{ i }}">{{ i }}</option>
          {% endfor %}
        </select>
      </div>
    </div>

    <div class="ai-booking-form__field-{{ ai_gen_id }}">
      <label for="BookingMessage-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.message_label }}</label>
      <textarea 
        id="BookingMessage-{{ ai_gen_id }}" 
        name="contact[body]" 
        class="ai-booking-form__textarea-{{ ai_gen_id }}"
      ></textarea>
    </div>

    <button type="submit" class="ai-booking-form__button-{{ ai_gen_id }}">
      {{ block.settings.submit_label }}
    </button>

    {% if form.posted_successfully? %}
      <div class="ai-booking-form__success-{{ ai_gen_id }}">
        {{ block.settings.success_message }}
      </div>
    {% endif %}

    {% if form.errors %}
      <div class="ai-booking-form__error-{{ ai_gen_id }}">
        {{ block.settings.error_message }}
      </div>
    {% endif %}
  {% endform %}
</div>

{% schema %}
{
  "name": "Booking Form",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Form Settings"
    },
    {
      "type": "text",
      "id": "form_title",
      "label": "Form title",
      "default": "Book an Appointment"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 36,
      "step": 1,
      "unit": "px",
      "label": "Title size",
      "default": 24
    },
    {
      "type": "select",
      "id": "title_alignment",
      "label": "Title alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "text",
      "id": "notification_email",
      "label": "Notification email",
      "info": "Email address that will receive booking notifications",
      "default": "<EMAIL>"
    },
    {
      "type": "header",
      "content": "Field Labels"
    },
    {
      "type": "text",
      "id": "name_label",
      "label": "Name field label",
      "default": "Full Name"
    },
    {
      "type": "text",
      "id": "email_label",
      "label": "Email field label",
      "default": "Email Address"
    },
    {
      "type": "text",
      "id": "phone_label",
      "label": "Phone field label",
      "default": "Phone Number"
    },
    {
      "type": "text",
      "id": "date_label",
      "label": "Date field label",
      "default": "Preferred Date"
    },
    {
      "type": "text",
      "id": "time_label",
      "label": "Time field label",
      "default": "Preferred Time"
    },
    {
      "type": "text",
      "id": "guests_label",
      "label": "Guests field label",
      "default": "Number of Guests"
    },
    {
      "type": "range",
      "id": "max_guests",
      "min": 1,
      "max": 20,
      "step": 1,
      "label": "Maximum number of guests",
      "default": 10
    },
    {
      "type": "text",
      "id": "message_label",
      "label": "Message field label",
      "default": "Special Requests or Notes"
    },
    {
      "type": "text",
      "id": "submit_label",
      "label": "Submit button label",
      "default": "Book Now"
    },
    {
      "type": "header",
      "content": "Messages"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success message",
      "default": "Thank you for your booking request! We'll contact you shortly to confirm your appointment."
    },
    {
      "type": "text",
      "id": "error_message",
      "label": "Error message",
      "default": "There was a problem submitting your booking. Please check the form and try again."
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "range",
      "id": "form_padding",
      "min": 0,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Form padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "form_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Form border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "input_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Input border radius",
      "default": 4
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Button border radius",
      "default": 4
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "form_background",
      "label": "Form background",
      "default": "#f9f9f9"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "input_background",
      "label": "Input background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "input_border_color",
      "label": "Input border color",
      "default": "#dddddd"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#4a90e2"
    },
    {
      "type": "color",
      "id": "button_hover_background",
      "label": "Button hover background",
      "default": "#3a7bc8"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "success_background",
      "label": "Success message background",
      "default": "#d4edda"
    },
    {
      "type": "color",
      "id": "success_text_color",
      "label": "Success message text color",
      "default": "#155724"
    },
    {
      "type": "color",
      "id": "error_background",
      "label": "Error message background",
      "default": "#f8d7da"
    },
    {
      "type": "color",
      "id": "error_text_color",
      "label": "Error message text color",
      "default": "#721c24"
    }
  ],
  "presets": [
    {
      "name": "Booking Form"
    }
  ]
}
{% endschema %}