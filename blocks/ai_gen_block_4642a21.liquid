{% doc %}
  @prompt
    Create a footer sitemap section with organized links to main store pages including popular tours, about us and contact page links, and contact information that can be used across all page templates

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .footer-sitemap-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px 0;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
  }

  .footer-sitemap__container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: 30px;
  }

  .footer-sitemap__column-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
  }

  .footer-sitemap__heading-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    font-weight: 600;
    margin-bottom: 20px;
    color: {{ block.settings.heading_color }};
  }

  .footer-sitemap__links-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .footer-sitemap__link-{{ ai_gen_id }} {
    text-decoration: none;
    color: {{ block.settings.link_color }};
    font-size: {{ block.settings.text_size }}px;
    transition: color 0.2s ease;
  }

  .footer-sitemap__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .footer-sitemap__contact-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .footer-sitemap__contact-item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: {{ block.settings.text_size }}px;
  }

  .footer-sitemap__contact-icon-{{ ai_gen_id }} {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .footer-sitemap__social-{{ ai_gen_id }} {
    display: flex;
    gap: 15px;
    margin-top: 20px;
  }

  .footer-sitemap__social-link-{{ ai_gen_id }} {
    color: {{ block.settings.social_icon_color }};
    transition: color 0.2s ease;
  }

  .footer-sitemap__social-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.social_icon_hover_color }};
  }

  .footer-sitemap__logo-{{ ai_gen_id }} {
    margin-bottom: 15px;
    max-width: 100%;
  }

  .footer-sitemap__logo-image-{{ ai_gen_id }} {
    max-width: {{ block.settings.logo_width }}px;
    height: auto;
  }

  .footer-sitemap__description-{{ ai_gen_id }} {
    font-size: {{ block.settings.text_size }}px;
    margin-bottom: 20px;
    line-height: 1.5;
  }

  @media screen and (max-width: 749px) {
    .footer-sitemap__container-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<div class="footer-sitemap-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="footer-sitemap__container-{{ ai_gen_id }}">
    {% if block.settings.show_company_info %}
      <div class="footer-sitemap__column-{{ ai_gen_id }}">
        {% if block.settings.logo != blank %}
          <div class="footer-sitemap__logo-{{ ai_gen_id }}">
            <img 
              src="{{ block.settings.logo | image_url: width: block.settings.logo_width }}" 
              alt="{{ block.settings.company_name }}"
              width="{{ block.settings.logo_width }}"
              height=""
              loading="lazy"
              class="footer-sitemap__logo-image-{{ ai_gen_id }}"
            >
          </div>
        {% elsif block.settings.company_name != blank %}
          <h2 class="footer-sitemap__heading-{{ ai_gen_id }}">{{ block.settings.company_name }}</h2>
        {% endif %}
        
        {% if block.settings.company_description != blank %}
          <div class="footer-sitemap__description-{{ ai_gen_id }}">{{ block.settings.company_description }}</div>
        {% endif %}
        
        {% if block.settings.show_social_links %}
          <div class="footer-sitemap__social-{{ ai_gen_id }}">
            {% if block.settings.facebook_link != blank %}
              <a href="{{ block.settings.facebook_link }}" class="footer-sitemap__social-link-{{ ai_gen_id }}" aria-label="Facebook">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
            {% endif %}
            
            {% if block.settings.instagram_link != blank %}
              <a href="{{ block.settings.instagram_link }}" class="footer-sitemap__social-link-{{ ai_gen_id }}" aria-label="Instagram">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
            {% endif %}
            
            {% if block.settings.twitter_link != blank %}
              <a href="{{ block.settings.twitter_link }}" class="footer-sitemap__social-link-{{ ai_gen_id }}" aria-label="Twitter">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                </svg>
              </a>
            {% endif %}
            
            {% if block.settings.youtube_link != blank %}
              <a href="{{ block.settings.youtube_link }}" class="footer-sitemap__social-link-{{ ai_gen_id }}" aria-label="YouTube">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                  <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
                </svg>
              </a>
            {% endif %}
          </div>
        {% endif %}
      </div>
    {% endif %}
    
    {% if block.settings.show_main_menu %}
      <div class="footer-sitemap__column-{{ ai_gen_id }}">
        <h3 class="footer-sitemap__heading-{{ ai_gen_id }}">{{ block.settings.main_menu_heading }}</h3>
        <div class="footer-sitemap__links-{{ ai_gen_id }}">
          {% if block.settings.main_menu_link_1_text != blank and block.settings.main_menu_link_1_url != blank %}
            <a href="{{ block.settings.main_menu_link_1_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.main_menu_link_1_text }}</a>
          {% endif %}
          
          {% if block.settings.main_menu_link_2_text != blank and block.settings.main_menu_link_2_url != blank %}
            <a href="{{ block.settings.main_menu_link_2_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.main_menu_link_2_text }}</a>
          {% endif %}
          
          {% if block.settings.main_menu_link_3_text != blank and block.settings.main_menu_link_3_url != blank %}
            <a href="{{ block.settings.main_menu_link_3_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.main_menu_link_3_text }}</a>
          {% endif %}
          
          {% if block.settings.main_menu_link_4_text != blank and block.settings.main_menu_link_4_url != blank %}
            <a href="{{ block.settings.main_menu_link_4_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.main_menu_link_4_text }}</a>
          {% endif %}
          
          {% if block.settings.main_menu_link_5_text != blank and block.settings.main_menu_link_5_url != blank %}
            <a href="{{ block.settings.main_menu_link_5_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.main_menu_link_5_text }}</a>
          {% endif %}
        </div>
      </div>
    {% endif %}
    
    {% if block.settings.show_tours_menu %}
      <div class="footer-sitemap__column-{{ ai_gen_id }}">
        <h3 class="footer-sitemap__heading-{{ ai_gen_id }}">{{ block.settings.tours_menu_heading }}</h3>
        <div class="footer-sitemap__links-{{ ai_gen_id }}">
          {% if block.settings.tours_menu_link_1_text != blank and block.settings.tours_menu_link_1_url != blank %}
            <a href="{{ block.settings.tours_menu_link_1_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.tours_menu_link_1_text }}</a>
          {% endif %}
          
          {% if block.settings.tours_menu_link_2_text != blank and block.settings.tours_menu_link_2_url != blank %}
            <a href="{{ block.settings.tours_menu_link_2_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.tours_menu_link_2_text }}</a>
          {% endif %}
          
          {% if block.settings.tours_menu_link_3_text != blank and block.settings.tours_menu_link_3_url != blank %}
            <a href="{{ block.settings.tours_menu_link_3_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.tours_menu_link_3_text }}</a>
          {% endif %}
          
          {% if block.settings.tours_menu_link_4_text != blank and block.settings.tours_menu_link_4_url != blank %}
            <a href="{{ block.settings.tours_menu_link_4_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.tours_menu_link_4_text }}</a>
          {% endif %}
          
          {% if block.settings.tours_menu_link_5_text != blank and block.settings.tours_menu_link_5_url != blank %}
            <a href="{{ block.settings.tours_menu_link_5_url }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.tours_menu_link_5_text }}</a>
          {% endif %}
        </div>
      </div>
    {% endif %}
    
    {% if block.settings.show_contact_info %}
      <div class="footer-sitemap__column-{{ ai_gen_id }}">
        <h3 class="footer-sitemap__heading-{{ ai_gen_id }}">{{ block.settings.contact_heading }}</h3>
        <div class="footer-sitemap__contact-{{ ai_gen_id }}">
          {% if block.settings.contact_address != blank %}
            <div class="footer-sitemap__contact-item-{{ ai_gen_id }}">
              <div class="footer-sitemap__contact-icon-{{ ai_gen_id }}">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
              </div>
              <span>{{ block.settings.contact_address }}</span>
            </div>
          {% endif %}
          
          {% if block.settings.contact_phone != blank %}
            <div class="footer-sitemap__contact-item-{{ ai_gen_id }}">
              <div class="footer-sitemap__contact-icon-{{ ai_gen_id }}">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
              </div>
              <a href="tel:{{ block.settings.contact_phone | escape }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.contact_phone }}</a>
            </div>
          {% endif %}
          
          {% if block.settings.contact_email != blank %}
            <div class="footer-sitemap__contact-item-{{ ai_gen_id }}">
              <div class="footer-sitemap__contact-icon-{{ ai_gen_id }}">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </div>
              <a href="mailto:{{ block.settings.contact_email | escape }}" class="footer-sitemap__link-{{ ai_gen_id }}">{{ block.settings.contact_email }}</a>
            </div>
          {% endif %}
          
          {% if block.settings.contact_hours != blank %}
            <div class="footer-sitemap__contact-item-{{ ai_gen_id }}">
              <div class="footer-sitemap__contact-icon-{{ ai_gen_id }}">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
              </div>
              <span>{{ block.settings.contact_hours }}</span>
            </div>
          {% endif %}
        </div>
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Footer Sitemap",
  "tag": null,
  "class": "footer-sitemap",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "options": [
        {
          "value": "2",
          "label": "2 columns"
        },
        {
          "value": "3",
          "label": "3 columns"
        },
        {
          "value": "4",
          "label": "4 columns"
        }
      ],
      "default": "4",
      "label": "Desktop columns"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "1",
      "label": "Mobile columns"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 20,
      "max": 100,
      "step": 10,
      "unit": "px",
      "label": "Vertical padding",
      "default": 60
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f3f3f3"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "social_icon_color",
      "label": "Social icon color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "social_icon_hover_color",
      "label": "Social icon hover color",
      "default": "#000f9f"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 14,
      "max": 28,
      "step": 1,
      "unit": "px",
      "label": "Heading size",
      "default": 18
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Company Information"
    },
    {
      "type": "checkbox",
      "id": "show_company_info",
      "label": "Show company information",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo"
    },
    {
      "type": "range",
      "id": "logo_width",
      "min": 50,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Logo width",
      "default": 120
    },
    {
      "type": "text",
      "id": "company_name",
      "label": "Company name",
      "default": "Your Company"
    },
    {
      "type": "richtext",
      "id": "company_description",
      "label": "Company description",
      "default": "<p>Provide a short description about your company, your mission, or your values.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_social_links",
      "label": "Show social media links",
      "default": true
    },
    {
      "type": "text",
      "id": "facebook_link",
      "label": "Facebook link"
    },
    {
      "type": "text",
      "id": "instagram_link",
      "label": "Instagram link"
    },
    {
      "type": "text",
      "id": "twitter_link",
      "label": "Twitter link"
    },
    {
      "type": "text",
      "id": "youtube_link",
      "label": "YouTube link"
    },
    {
      "type": "header",
      "content": "Main Menu Links"
    },
    {
      "type": "checkbox",
      "id": "show_main_menu",
      "label": "Show main menu",
      "default": true
    },
    {
      "type": "text",
      "id": "main_menu_heading",
      "label": "Main menu heading",
      "default": "Main Menu"
    },
    {
      "type": "text",
      "id": "main_menu_link_1_text",
      "label": "Link 1 text",
      "default": "Home"
    },
    {
      "type": "url",
      "id": "main_menu_link_1_url",
      "label": "Link 1 URL"
    },
    {
      "type": "text",
      "id": "main_menu_link_2_text",
      "label": "Link 2 text",
      "default": "Shop"
    },
    {
      "type": "url",
      "id": "main_menu_link_2_url",
      "label": "Link 2 URL"
    },
    {
      "type": "text",
      "id": "main_menu_link_3_text",
      "label": "Link 3 text",
      "default": "About Us"
    },
    {
      "type": "url",
      "id": "main_menu_link_3_url",
      "label": "Link 3 URL"
    },
    {
      "type": "text",
      "id": "main_menu_link_4_text",
      "label": "Link 4 text",
      "default": "Contact"
    },
    {
      "type": "url",
      "id": "main_menu_link_4_url",
      "label": "Link 4 URL"
    },
    {
      "type": "text",
      "id": "main_menu_link_5_text",
      "label": "Link 5 text",
      "default": "Blog"
    },
    {
      "type": "url",
      "id": "main_menu_link_5_url",
      "label": "Link 5 URL"
    },
    {
      "type": "header",
      "content": "Tours Menu Links"
    },
    {
      "type": "checkbox",
      "id": "show_tours_menu",
      "label": "Show tours menu",
      "default": true
    },
    {
      "type": "text",
      "id": "tours_menu_heading",
      "label": "Tours menu heading",
      "default": "Popular Tours"
    },
    {
      "type": "text",
      "id": "tours_menu_link_1_text",
      "label": "Link 1 text",
      "default": "City Tours"
    },
    {
      "type": "url",
      "id": "tours_menu_link_1_url",
      "label": "Link 1 URL"
    },
    {
      "type": "text",
      "id": "tours_menu_link_2_text",
      "label": "Link 2 text",
      "default": "Adventure Tours"
    },
    {
      "type": "url",
      "id": "tours_menu_link_2_url",
      "label": "Link 2 URL"
    },
    {
      "type": "text",
      "id": "tours_menu_link_3_text",
      "label": "Link 3 text",
      "default": "Cultural Tours"
    },
    {
      "type": "url",
      "id": "tours_menu_link_3_url",
      "label": "Link 3 URL"
    },
    {
      "type": "text",
      "id": "tours_menu_link_4_text",
      "label": "Link 4 text",
      "default": "Food Tours"
    },
    {
      "type": "url",
      "id": "tours_menu_link_4_url",
      "label": "Link 4 URL"
    },
    {
      "type": "text",
      "id": "tours_menu_link_5_text",
      "label": "Link 5 text",
      "default": "Special Packages"
    },
    {
      "type": "url",
      "id": "tours_menu_link_5_url",
      "label": "Link 5 URL"
    },
    {
      "type": "header",
      "content": "Contact Information"
    },
    {
      "type": "checkbox",
      "id": "show_contact_info",
      "label": "Show contact information",
      "default": true
    },
    {
      "type": "text",
      "id": "contact_heading",
      "label": "Contact heading",
      "default": "Contact Us"
    },
    {
      "type": "text",
      "id": "contact_address",
      "label": "Address",
      "default": "123 Main Street, City, Country"
    },
    {
      "type": "text",
      "id": "contact_phone",
      "label": "Phone number",
      "default": "+****************"
    },
    {
      "type": "text",
      "id": "contact_email",
      "label": "Email",
      "default": "<EMAIL>"
    },
    {
      "type": "text",
      "id": "contact_hours",
      "label": "Business hours",
      "default": "Mon-Fri: 9am-5pm"
    }
  ],
  "presets": [
    {
      "name": "Footer Sitemap"
    }
  ]
}
{% endschema %}