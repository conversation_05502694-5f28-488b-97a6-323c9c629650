{% doc %}
  @prompt
    Create a responsive section with two images that smoothly transition from one to another on scroll. The section should be adaptive for all devices, maintain proper aspect ratios without cropping or stretching, and have a maximum width to prevent it from looking ugly on wide screens. Include smooth scroll-triggered animation effects and ensure the images are properly contained within reasonable dimensions.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .scroll-image-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
    overflow: hidden;
    height: auto;
    aspect-ratio: {{ block.settings.aspect_ratio }};
  }

  .scroll-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .scroll-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.1s ease;
  }

  .scroll-image-top-{{ ai_gen_id }} {
    opacity: 1;
    z-index: 2;
  }

  .scroll-image-bottom-{{ ai_gen_id }} {
    opacity: 0;
    z-index: 1;
  }

  .scroll-image-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: {{ block.settings.placeholder_color }};
  }

  .scroll-image-placeholder-{{ ai_gen_id }} svg {
    width: 50%;
    height: 50%;
    opacity: 0.5;
  }
{% endstyle %}

<scroll-transition-{{ ai_gen_id }} 
  class="scroll-image-container-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="scroll-image-wrapper-{{ ai_gen_id }}">
    {% if block.settings.image_top %}
      <img 
        src="{{ block.settings.image_top | image_url: width: 2000 }}" 
        alt="{{ block.settings.image_top.alt | escape }}"
        loading="lazy"
        class="scroll-image-{{ ai_gen_id }} scroll-image-top-{{ ai_gen_id }}"
      >
    {% else %}
      <div class="scroll-image-placeholder-{{ ai_gen_id }} scroll-image-top-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}

    {% if block.settings.image_bottom %}
      <img 
        src="{{ block.settings.image_bottom | image_url: width: 2000 }}" 
        alt="{{ block.settings.image_bottom.alt | escape }}"
        loading="lazy"
        class="scroll-image-{{ ai_gen_id }} scroll-image-bottom-{{ ai_gen_id }}"
      >
    {% else %}
      <div class="scroll-image-placeholder-{{ ai_gen_id }} scroll-image-bottom-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>
</scroll-transition-{{ ai_gen_id }}>

<script>
  (function() {
    class ScrollTransition{{ai_gen_id}} extends HTMLElement {
      constructor() {
        super();
        this.topImage = this.querySelector('.scroll-image-top-{{ ai_gen_id }}');
        this.bottomImage = this.querySelector('.scroll-image-bottom-{{ ai_gen_id }}');
        this.transitionStart = {{ block.settings.transition_start }};
        this.transitionEnd = {{ block.settings.transition_end }};
        this.observer = null;
      }

      connectedCallback() {
        if ('IntersectionObserver' in window) {
          this.setupIntersectionObserver();
        } else {
          this.setupScrollListener();
        }
      }

      disconnectedCallback() {
        if (this.observer) {
          this.observer.disconnect();
        }
        window.removeEventListener('scroll', this.handleScroll);
      }

      setupIntersectionObserver() {
        const options = {
          root: null,
          rootMargin: '0px',
          threshold: Array.from({ length: 101 }, (_, i) => i / 100)
        };

        this.observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.updateImageOpacity(entry.intersectionRatio);
            }
          });
        }, options);

        this.observer.observe(this);
      }

      setupScrollListener() {
        this.handleScroll = () => {
          const rect = this.getBoundingClientRect();
          const windowHeight = window.innerHeight;
          
          // Calculate how far the element is through the viewport (0 to 1)
          const elementProgress = 1 - (rect.bottom / (rect.height + windowHeight));
          
          if (elementProgress >= 0 && elementProgress <= 1) {
            this.updateImageOpacity(elementProgress);
          }
        };

        window.addEventListener('scroll', this.handleScroll);
        // Initial check
        this.handleScroll();
      }

      updateImageOpacity(scrollProgress) {
        // Normalize the progress to our transition range
        const normalizedProgress = (scrollProgress - this.transitionStart) / (this.transitionEnd - this.transitionStart);
        
        // Clamp between 0 and 1
        const opacity = Math.max(0, Math.min(1, normalizedProgress));
        
        // Update the opacity of the top image (which will reveal the bottom image)
        this.topImage.style.opacity = 1 - opacity;
        this.bottomImage.style.opacity = opacity;
      }
    }

    customElements.define('scroll-transition-{{ ai_gen_id }}', ScrollTransition{{ai_gen_id}});
  })();
</script>

{% schema %}
{
  "name": "Image Transition",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "image_picker",
      "id": "image_top",
      "label": "First image"
    },
    {
      "type": "image_picker",
      "id": "image_bottom",
      "label": "Second image"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 600,
      "max": 2000,
      "step": 100,
      "unit": "px",
      "label": "Maximum width",
      "default": 1200
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Aspect ratio",
      "options": [
        {
          "value": "1/1",
          "label": "1:1 (Square)"
        },
        {
          "value": "4/3",
          "label": "4:3"
        },
        {
          "value": "3/2",
          "label": "3:2"
        },
        {
          "value": "16/9",
          "label": "16:9"
        },
        {
          "value": "2/3",
          "label": "2:3 (Portrait)"
        }
      ],
      "default": "16/9"
    },
    {
      "type": "header",
      "content": "Transition"
    },
    {
      "type": "range",
      "id": "transition_start",
      "min": 0,
      "max": 0.8,
      "step": 0.1,
      "label": "Start transition at",
      "info": "When the element is this far through the viewport (0 = top, 1 = bottom)",
      "default": 0.2
    },
    {
      "type": "range",
      "id": "transition_end",
      "min": 0.2,
      "max": 1,
      "step": 0.1,
      "label": "End transition at",
      "info": "When the element is this far through the viewport (0 = top, 1 = bottom)",
      "default": 0.8
    },
    {
      "type": "color",
      "id": "placeholder_color",
      "label": "Placeholder background color",
      "default": "#f4f4f4"
    }
  ],
  "presets": [
    {
      "name": "Image Transition"
    }
  ]
}
{% endschema %}