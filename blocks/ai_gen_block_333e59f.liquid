{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, featuring a responsive design that works on all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    gap: {{ block.settings.link_spacing }}px;
    margin-bottom: {{ block.settings.spacing_before_copyright }}px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
    white-space: nowrap;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-footer__separator-{{ ai_gen_id }} {
    color: {{ block.settings.separator_color }};
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    text-align: {{ block.settings.copyright_alignment }};
    font-size: {{ block.settings.copyright_font_size }}px;
    color: {{ block.settings.copyright_color }};
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer-{{ ai_gen_id }} {
      padding: {{ block.settings.padding_mobile }}px;
      font-size: {{ block.settings.font_size | minus: 1 }}px;
    }
    
    .ai-sitemap-footer__links-{{ ai_gen_id }} {
      gap: {{ block.settings.link_spacing_mobile }}px;
    }
    
    .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
      font-size: {{ block.settings.copyright_font_size | minus: 1 }}px;
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__links-{{ ai_gen_id }}">
      {% for link in linklists[block.settings.menu].links %}
        <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">
          {{ link.title }}
        </a>
        {% unless forloop.last %}
          <span class="ai-sitemap-footer__separator-{{ ai_gen_id }}">{{ block.settings.separator }}</span>
        {% endunless %}
      {% endfor %}
    </div>
    
    {% if block.settings.show_copyright %}
      <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
        {% if block.settings.copyright_text != blank %}
          {{ block.settings.copyright_text }}
        {% else %}
          &copy; {{ 'now' | date: "%Y" }} {{ shop.name }}
        {% endif %}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Sitemap Footer",
  "tag": null,
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "Menu",
      "default": "footer"
    },
    {
      "type": "text",
      "id": "separator",
      "label": "Link separator",
      "default": "|"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "checkbox",
      "id": "show_copyright",
      "label": "Show copyright",
      "default": true
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Custom copyright text",
      "info": "Leave blank to use shop name with current year"
    },
    {
      "type": "select",
      "id": "copyright_alignment",
      "label": "Copyright alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "separator_color",
      "label": "Separator color",
      "default": "#888888"
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright text color",
      "default": "#666666"
    },
    {
      "type": "header",
      "content": "Spacing & Typography"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "padding_mobile",
      "min": 5,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Mobile padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "link_spacing",
      "min": 5,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Link spacing",
      "default": 15
    },
    {
      "type": "range",
      "id": "link_spacing_mobile",
      "min": 4,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Mobile link spacing",
      "default": 10
    },
    {
      "type": "range",
      "id": "spacing_before_copyright",
      "min": 5,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Space before copyright",
      "default": 20
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "copyright_font_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Copyright font size",
      "default": 12
    }
  ],
  "presets": [
    {
      "name": "Sitemap Footer"
    }
  ]
}
{% endschema %}