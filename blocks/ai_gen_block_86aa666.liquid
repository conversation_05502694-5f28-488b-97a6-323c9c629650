{% doc %}
  @prompt
    Create a video section with file picker to upload video files from Shopify Files, supporting MP4 and WebM formats, with small compact display size and responsive design

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-section-{{ ai_gen_id }} {
    width: 100%;
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-video-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: {{ block.settings.video_height }}px;
    background-color: #000;
    border-radius: {{ block.settings.video_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .ai-video-element-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: {{ block.settings.video_border_radius }}px;
  }

  .ai-video-placeholder-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    color: #666;
    font-size: 16px;
    text-align: center;
    border-radius: {{ block.settings.video_border_radius }}px;
  }

  .ai-video-placeholder-{{ ai_gen_id }} svg {
    width: 60px;
    height: 60px;
    opacity: 0.5;
  }

  .ai-video-title-{{ ai_gen_id }} {
    margin: 0 0 12px 0;
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.text_color }};
    text-align: {{ block.settings.text_alignment }};
  }

  .ai-video-description-{{ ai_gen_id }} {
    margin: 12px 0 0 0;
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.text_color }};
    text-align: {{ block.settings.text_alignment }};
    line-height: 1.5;
  }

  .ai-video-controls-{{ ai_gen_id }} {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .ai-video-container-{{ ai_gen_id }}:hover .ai-video-controls-{{ ai_gen_id }} {
    opacity: 1;
  }

  .ai-video-control-btn-{{ ai_gen_id }} {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .ai-video-control-btn-{{ ai_gen_id }}:hover {
    background: rgba(0, 0, 0, 0.9);
  }

  .ai-video-control-btn-{{ ai_gen_id }} svg {
    width: 16px;
    height: 16px;
  }

  @media screen and (max-width: 749px) {
    .ai-video-section-{{ ai_gen_id }} {
      padding: {{ block.settings.padding | times: 0.7 }}px;
    }

    .ai-video-container-{{ ai_gen_id }} {
      height: {{ block.settings.video_height | times: 0.7 }}px;
    }

    .ai-video-title-{{ ai_gen_id }} {
      font-size: {{ block.settings.title_size | times: 0.9 }}px;
    }

    .ai-video-description-{{ ai_gen_id }} {
      font-size: {{ block.settings.description_size | times: 0.9 }}px;
    }

    .ai-video-controls-{{ ai_gen_id }} {
      opacity: 1;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-video-container-{{ ai_gen_id }} {
      height: {{ block.settings.video_height | times: 0.6 }}px;
    }
  }
{% endstyle %}

<video-section-{{ ai_gen_id }} class="ai-video-section-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.title != blank %}
    <h2 class="ai-video-title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
  {% endif %}

  <div class="ai-video-container-{{ ai_gen_id }}">
    {% if block.settings.video_url != blank %}
      <video
        class="ai-video-element-{{ ai_gen_id }}"
        {% if block.settings.autoplay %}autoplay{% endif %}
        {% if block.settings.loop %}loop{% endif %}
        {% if block.settings.muted %}muted{% endif %}
        {% if block.settings.show_controls %}controls{% endif %}
        preload="metadata"
        playsinline
      >
        <source src="{{ block.settings.video_url }}" type="video/mp4">
        <source src="{{ block.settings.video_url }}" type="video/webm">
        Your browser does not support the video tag.
      </video>

      {% unless block.settings.show_controls %}
        <div class="ai-video-controls-{{ ai_gen_id }}">
          <button class="ai-video-control-btn-{{ ai_gen_id }}" data-action="play-pause" aria-label="Play/Pause">
            <svg class="play-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
            <svg class="pause-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
              <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
            </svg>
          </button>
          {% if block.settings.allow_mute_toggle %}
            <button class="ai-video-control-btn-{{ ai_gen_id }}" data-action="mute-toggle" aria-label="Mute/Unmute">
              <svg class="volume-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
              </svg>
              <svg class="mute-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
              </svg>
            </button>
          {% endif %}
        </div>
      {% endunless %}
    {% else %}
      <div class="ai-video-placeholder-{{ ai_gen_id }}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
        </svg>
      </div>
    {% endif %}
  </div>

  {% if block.settings.description != blank %}
    <div class="ai-video-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
  {% endif %}
</video-section-{{ ai_gen_id }}>

<script>
  (function() {
    class VideoSection{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.video = this.querySelector('.ai-video-element-{{ ai_gen_id }}');
        this.playPauseBtn = this.querySelector('[data-action="play-pause"]');
        this.muteToggleBtn = this.querySelector('[data-action="mute-toggle"]');

        if (this.video && this.playPauseBtn) {
          this.setupEventListeners();
        }
      }

      setupEventListeners() {
        this.playPauseBtn.addEventListener('click', () => {
          if (this.video.paused) {
            this.video.play();
          } else {
            this.video.pause();
          }
        });

        if (this.muteToggleBtn) {
          this.muteToggleBtn.addEventListener('click', () => {
            this.video.muted = !this.video.muted;
            this.updateMuteIcon();
          });
        }

        this.video.addEventListener('play', () => {
          this.updatePlayIcon();
        });

        this.video.addEventListener('pause', () => {
          this.updatePlayIcon();
        });

        this.video.addEventListener('loadedmetadata', () => {
          this.updateMuteIcon();
        });
      }

      updatePlayIcon() {
        const playIcon = this.playPauseBtn.querySelector('.play-icon');
        const pauseIcon = this.playPauseBtn.querySelector('.pause-icon');

        if (this.video.paused) {
          playIcon.style.display = 'block';
          pauseIcon.style.display = 'none';
        } else {
          playIcon.style.display = 'none';
          pauseIcon.style.display = 'block';
        }
      }

      updateMuteIcon() {
        if (!this.muteToggleBtn) return;

        const volumeIcon = this.muteToggleBtn.querySelector('.volume-icon');
        const muteIcon = this.muteToggleBtn.querySelector('.mute-icon');

        if (this.video.muted) {
          volumeIcon.style.display = 'none';
          muteIcon.style.display = 'block';
        } else {
          volumeIcon.style.display = 'block';
          muteIcon.style.display = 'none';
        }
      }
    }

    customElements.define('video-section-{{ ai_gen_id }}', VideoSection{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Video Section",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Video Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "url",
      "id": "video_url",
      "label": "Video URL",
      "info": "Upload video files to Shopify Files and paste the URL here. Supports MP4 and WebM formats."
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay video",
      "default": false,
      "info": "Video will be muted if autoplay is enabled"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop video",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "muted",
      "label": "Mute video",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_controls",
      "label": "Show video controls",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "allow_mute_toggle",
      "label": "Allow mute toggle",
      "default": true,
      "info": "Only applies when video controls are hidden"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 300,
      "max": 800,
      "step": 50,
      "unit": "px",
      "label": "Maximum width",
      "default": 500
    },
    {
      "type": "range",
      "id": "video_height",
      "min": 200,
      "max": 500,
      "step": 20,
      "unit": "px",
      "label": "Video height",
      "default": 280
    },
    {
      "type": "range",
      "id": "padding",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Section padding",
      "default": 20
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Section border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "video_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Video border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 24
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 14
    }
  ],
  "presets": [
    {
      "name": "Video Section"
    }
  ]
}
{% endschema %}