{% doc %}
  @prompt
    Create a classic before/after image comparison slider with a vertical divider line and draggable handle. The slider should show two images side by side with a movable vertical line that reveals more of the "after" image as you drag the handle to the right, and more of the "before" image as you drag left. Include a circular handle with arrows or grip icon on the divider line. Make it responsive and smooth to use on both desktop and mobile devices.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-before-after-slider-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .ai-before-after-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: {{ block.settings.slider_height }}px;
    overflow: hidden;
  }

  .ai-before-after-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    user-select: none;
    pointer-events: none;
  }

  .ai-before-after-before-{{ ai_gen_id }} {
    z-index: 1;
  }

  .ai-before-after-after-{{ ai_gen_id }} {
    z-index: 2;
    clip-path: inset(0 0 0 50%);
    transition: clip-path 0.1s ease-out;
  }

  .ai-before-after-divider-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: {{ block.settings.divider_width }}px;
    height: 100%;
    background-color: {{ block.settings.divider_color }};
    transform: translateX(-50%);
    z-index: 3;
    cursor: ew-resize;
    transition: left 0.1s ease-out;
  }

  .ai-before-after-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: {{ block.settings.handle_size }}px;
    height: {{ block.settings.handle_size }}px;
    background-color: {{ block.settings.handle_color }};
    border: {{ block.settings.handle_border_width }}px solid {{ block.settings.handle_border_color }};
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: ew-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease;
  }

  .ai-before-after-handle-{{ ai_gen_id }}:hover {
    transform: translate(-50%, -50%) scale(1.1);
  }

  .ai-before-after-handle-{{ ai_gen_id }} svg {
    width: calc({{ block.settings.handle_size }}px * 0.5);
    height: calc({{ block.settings.handle_size }}px * 0.5);
    fill: {{ block.settings.handle_icon_color }};
  }

  .ai-before-after-label-{{ ai_gen_id }} {
    position: absolute;
    top: 20px;
    padding: 8px 16px;
    background-color: {{ block.settings.label_bg_color }};
    color: {{ block.settings.label_text_color }};
    font-size: {{ block.settings.label_font_size }}px;
    font-weight: 600;
    border-radius: 20px;
    z-index: 4;
    opacity: {{ block.settings.label_opacity | divided_by: 100.0 }};
  }

  .ai-before-after-label-before-{{ ai_gen_id }} {
    left: 20px;
  }

  .ai-before-after-label-after-{{ ai_gen_id }} {
    right: 20px;
  }

  .ai-before-after-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: {{ block.settings.slider_height }}px;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-before-after-placeholder-{{ ai_gen_id }} svg {
    width: 100px;
    height: 100px;
    opacity: 0.3;
  }

  @media screen and (max-width: 749px) {
    .ai-before-after-container-{{ ai_gen_id }} {
      height: {{ block.settings.slider_height | times: 0.7 }}px;
    }

    .ai-before-after-placeholder-{{ ai_gen_id }} {
      height: {{ block.settings.slider_height | times: 0.7 }}px;
    }

    .ai-before-after-handle-{{ ai_gen_id }} {
      width: {{ block.settings.handle_size | times: 1.2 }}px;
      height: {{ block.settings.handle_size | times: 1.2 }}px;
    }

    .ai-before-after-handle-{{ ai_gen_id }} svg {
      width: calc({{ block.settings.handle_size | times: 1.2 }}px * 0.5);
      height: calc({{ block.settings.handle_size | times: 1.2 }}px * 0.5);
    }

    .ai-before-after-label-{{ ai_gen_id }} {
      font-size: {{ block.settings.label_font_size | times: 0.9 }}px;
      padding: 6px 12px;
    }
  }
{% endstyle %}

<before-after-slider-{{ ai_gen_id }}
  class="ai-before-after-slider-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  {% if block.settings.before_image or block.settings.after_image %}
    <div class="ai-before-after-container-{{ ai_gen_id }}">
      {% if block.settings.before_image %}
        <img
          src="{{ block.settings.before_image | image_url: width: 2000 }}"
          alt="{{ block.settings.before_image.alt | default: 'Before image' | escape }}"
          class="ai-before-after-image-{{ ai_gen_id }} ai-before-after-before-{{ ai_gen_id }}"
          loading="lazy"
          width="{{ block.settings.before_image.width }}"
          height="{{ block.settings.before_image.height }}"
        >
      {% endif %}

      {% if block.settings.after_image %}
        <img
          src="{{ block.settings.after_image | image_url: width: 2000 }}"
          alt="{{ block.settings.after_image.alt | default: 'After image' | escape }}"
          class="ai-before-after-image-{{ ai_gen_id }} ai-before-after-after-{{ ai_gen_id }}"
          loading="lazy"
          width="{{ block.settings.after_image.width }}"
          height="{{ block.settings.after_image.height }}"
        >
      {% endif %}

      <div class="ai-before-after-divider-{{ ai_gen_id }}">
        <div class="ai-before-after-handle-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 5l-4 4 4 4V5zm8 14l4-4-4-4v8z"/>
          </svg>
        </div>
      </div>

      {% if block.settings.show_labels %}
        {% if block.settings.before_label != blank %}
          <div class="ai-before-after-label-{{ ai_gen_id }} ai-before-after-label-before-{{ ai_gen_id }}">
            {{ block.settings.before_label }}
          </div>
        {% endif %}

        {% if block.settings.after_label != blank %}
          <div class="ai-before-after-label-{{ ai_gen_id }} ai-before-after-label-after-{{ ai_gen_id }}">
            {{ block.settings.after_label }}
          </div>
        {% endif %}
      {% endif %}
    </div>
  {% else %}
    <div class="ai-before-after-placeholder-{{ ai_gen_id }}">
      {{ 'image' | placeholder_svg_tag }}
    </div>
  {% endif %}
</before-after-slider-{{ ai_gen_id }}>

<script>
  (function() {
    class BeforeAfterSlider{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.isDragging = false;
        this.startX = 0;
        this.currentPosition = 50;
      }

      connectedCallback() {
        this.container = this.querySelector('.ai-before-after-container-{{ ai_gen_id }}');
        this.divider = this.querySelector('.ai-before-after-divider-{{ ai_gen_id }}');
        this.handle = this.querySelector('.ai-before-after-handle-{{ ai_gen_id }}');
        this.afterImage = this.querySelector('.ai-before-after-after-{{ ai_gen_id }}');

        if (!this.container || !this.divider || !this.handle || !this.afterImage) {
          return;
        }

        this.setupEventListeners();
      }

      setupEventListeners() {
        this.handle.addEventListener('mousedown', this.startDrag.bind(this));
        this.handle.addEventListener('touchstart', this.startDrag.bind(this), { passive: false });document.addEventListener('mousemove', this.drag.bind(this));
        document.addEventListener('touchmove', this.drag.bind(this), { passive: false });document.addEventListener('mouseup', this.stopDrag.bind(this));
        document.addEventListener('touchend', this.stopDrag.bind(this));

        this.container.addEventListener('click', this.handleClick.bind(this));
      }

      startDrag(e) {
        this.isDragging = true;
        this.startX = this.getEventX(e);
        e.preventDefault();
      }

      drag(e) {
        if (!this.isDragging) return;

        e.preventDefault();
        const rect = this.container.getBoundingClientRect();
        const x = this.getEventX(e) - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
        
        this.updatePosition(percentage);
      }

      stopDrag() {
        this.isDragging = false;
      }

      handleClick(e) {
        if (this.isDragging) return;

        const rect = this.container.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
        
        this.updatePosition(percentage);
      }

      updatePosition(percentage) {
        this.currentPosition = percentage;
        this.divider.style.left = percentage + '%';
        this.afterImage.style.clipPath = `inset(0 ${100 - percentage}% 0 0)`;
      }

      getEventX(e) {
        return e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
      }
    }

    customElements.define('before-after-slider-{{ ai_gen_id }}', BeforeAfterSlider{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Before/After Slider",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "image_picker",
      "id": "before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image",
      "label": "After image"
    },
    {
      "type": "range",
      "id": "slider_height",
      "min": 200,
      "max": 800,
      "step": 20,
      "unit": "px",
      "label": "Slider height",
      "default": 400
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Labels"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "range",
      "id": "label_font_size",
      "min": 10,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Label font size",
      "default": 14
    },
    {
      "type": "color",
      "id": "label_bg_color",
      "label": "Label background color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "label_text_color",
      "label": "Label text color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "label_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Label opacity",
      "default": 80
    },
    {
      "type": "header",
      "content": "Divider"
    },
    {
      "type": "range",
      "id": "divider_width",
      "min": 1,
      "max": 8,
      "step": 1,
      "unit": "px",
      "label": "Divider width",
      "default": 3
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Handle"
    },
    {
      "type": "range",
      "id": "handle_size",
      "min": 30,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Handle size",
      "default": 50
    },
    {
      "type": "color",
      "id": "handle_color",
      "label": "Handle background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "handle_border_color",
      "label": "Handle border color",
      "default": "#cccccc"
    },
    {
      "type": "range",
      "id": "handle_border_width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Handle border width",
      "default": 2
    },
    {
      "type": "color",
      "id": "handle_icon_color",
      "label": "Handle icon color",
      "default": "#666666"
    }
  ],
  "presets": [
    {
      "name": "Before/After Slider"
    }
  ]
}
{% endschema %}