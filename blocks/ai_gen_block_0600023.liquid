{% doc %}
  @prompt
    Create a responsive video player component that maintains proper aspect ratio and doesn't stretch videos to full screen width. The player should have a maximum width, be centered on the page, preserve video proportions, and include standard video controls. The design should be clean and professional with proper spacing around the video.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-player-container-{{ ai_gen_id }} {
    width: 100%;
    max-width: {{ block.settings.max_width }}px;
    margin: {{ block.settings.spacing_top }}px auto {{ block.settings.spacing_bottom }}px;
    padding: 0 {{ block.settings.horizontal_padding }}px;
    box-sizing: border-box;}

  .ai-video-player-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
    overflow: hidden;
    box-shadow: 0 {{ block.settings.shadow_vertical }}px {{ block.settings.shadow_blur }}px rgba(0, 0, 0, {{ block.settings.shadow_opacity | divided_by: 100.0 }});
  }

  .ai-video-player-aspect-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: {{ block.settings.aspect_ratio }}%;
  }

  .ai-video-player-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
  }

  .ai-video-player-{{ ai_gen_id }}:focus {
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
  }

  .ai-video-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #6c757d;
  }

  .ai-video-placeholder-{{ ai_gen_id }} svg {
    width: 80px;
    height: 80px;
    opacity: 0.5;
  }

  .ai-video-title-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: {{ block.settings.title_spacing }}px;
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.title_color }};
    font-weight: 600;
    line-height: 1.3;
  }

  .ai-video-description-{{ ai_gen_id }} {
    text-align: center;
    margin-top: {{ block.settings.description_spacing }}px;
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.description_color }};
    line-height: 1.5;
  }

  @media screen and (max-width: 768px) {
    .ai-video-player-container-{{ ai_gen_id }} {
      padding: 0 {{ block.settings.mobile_horizontal_padding }}px;
      margin: {{ block.settings.mobile_spacing_top }}px auto {{ block.settings.mobile_spacing_bottom }}px;
    }

    .ai-video-title-{{ ai_gen_id }} {
      font-size: {{ block.settings.mobile_title_size }}px;
    }

    .ai-video-description-{{ ai_gen_id }} {
      font-size: {{ block.settings.mobile_description_size }}px;
    }
  }
{% endstyle %}<div class="ai-video-player-container-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.title != blank %}
    <h2 class="ai-video-title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
  {% endif %}<div class="ai-video-player-wrapper-{{ ai_gen_id }}">
    <div class="ai-video-player-aspect-{{ ai_gen_id }}">
      {% if block.settings.video_url != blank %}
        <video
          class="ai-video-player-{{ ai_gen_id }}"
          controls
          {% if block.settings.autoplay %}autoplay{% endif %}
          {% if block.settings.muted %}muted{% endif %}
          {% if block.settings.loop %}loop{% endif %}
          {% if block.settings.preload != 'none' %}preload="{{ block.settings.preload }}"{% endif %}
          {% if block.settings.poster_image %}poster="{{ block.settings.poster_image | image_url: width: 1200}}"{% endif %}
          aria-label="{% if block.settings.title != blank %}{{ block.settings.title | escape }}{% else %}Video player{% endif %}">
          <source src="{{ block.settings.video_url }}" type="video/mp4">
          <p>Your browser doesn't support HTML5 video.<a href="{{ block.settings.video_url }}">Download the video</a> instead.</p>
        </video>
      {% else %}
        <div class="ai-video-placeholder-{{ ai_gen_id }}">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5,3 19,12 5,21"></polygon>
          </svg>
        </div>
      {% endif %}
    </div>
  </div>

  {% if block.settings.description != blank %}
    <div class="ai-video-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
  {% endif %}
</div>

{% schema %}
{
  "name": "Video Player",
  "settings": [
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "url",
      "id": "video_url",
      "label": "Video URL"
    },
    {
      "type": "image_picker",
      "id": "poster_image",
      "label": "Poster image"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "muted",
      "label": "Muted",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop",
      "default": false
    },
    {
      "type": "select",
      "id": "preload",
      "label": "Preload",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "metadata",
          "label": "Metadata"
        },
        {
          "value": "auto",
          "label": "Auto"
        }
      ],
      "default": "metadata"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 400,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Maximum width",
      "default": 800
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Aspect ratio",
      "options": [
        {
          "value": "56.25",
          "label": "16:9"
        },
        {
          "value": "75",
          "label": "4:3"
        },
        {
          "value": "66.67",
          "label": "3:2"
        },
        {
          "value": "100",
          "label": "1:1"
        }
      ],
      "default": "56.25"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "spacing_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top spacing",
      "default": 40
    },
    {
      "type": "range",
      "id": "spacing_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom spacing",
      "default": 40
    },
    {
      "type": "range",
      "id": "horizontal_padding",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Horizontal padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "title_spacing",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Title spacing",
      "default": 20
    },
    {
      "type": "range",
      "id": "description_spacing",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Description spacing",
      "default": 20
    },
    {
      "type": "header",
      "content": "Mobile Spacing"
    },
    {
      "type": "range",
      "id": "mobile_spacing_top",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Mobile top spacing",
      "default": 24
    },
    {
      "type": "range",
      "id": "mobile_spacing_bottom",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Mobile bottom spacing",
      "default": 24
    },
    {
      "type": "range",
      "id": "mobile_horizontal_padding",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Mobile horizontal padding",
      "default": 16
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 28
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "range",
      "id": "mobile_title_size",
      "min": 14,
      "max": 36,
      "step": 2,
      "unit": "px",
      "label": "Mobile title size",
      "default": 24
    },
    {
      "type": "range",
      "id": "mobile_description_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Mobile description size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Video background color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#666666"
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "shadow_vertical",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Shadow vertical offset",
      "default": 4
    },
    {
      "type": "range",
      "id": "shadow_blur",
      "min": 0,
      "max": 40,
      "step": 4,
      "unit": "px",
      "label": "Shadow blur",
      "default": 12
    },
    {
      "type": "range",
      "id": "shadow_opacity",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "%",
      "label": "Shadow opacity",
      "default": 15
    }
  ],
  "presets": [
    {
      "name": "Video Player"
    }
  ],
  "tag": null
}
{% endschema %}