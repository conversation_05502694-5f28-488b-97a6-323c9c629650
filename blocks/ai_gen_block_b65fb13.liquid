{% doc %}
  @prompt
    Create an elegant wedding section with image on the right and text content on the left. The background should be soft pink with beautiful floral decorations or flower petals. Include settings for image upload, heading text, description text, button with customizable text and link. The section should be responsive with proper spacing and elegant typography suitable for wedding themes. Add options to customize the pink background shade and floral elements.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .wedding-block-{{ ai_gen_id }} {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: {{ block.settings.background_color }};
    padding: 60px 40px;
    position: relative;
    overflow: hidden;
  }

  .wedding-block__content-{{ ai_gen_id }} {
    flex: 1;
    padding-right: 40px;
    position: relative;
    z-index: 2;
  }

  .wedding-block__heading-{{ ai_gen_id }} {
    font-family: {{ block.settings.heading_font.family }}, serif;
    font-size: {{ block.settings.heading_size }}px;
    color: {{ block.settings.text_color }};
    margin-bottom: 20px;
    font-weight: 500;
    line-height: 1.2;
  }

  .wedding-block__description-{{ ai_gen_id }} {
    font-family: {{ block.settings.body_font.family }}, sans-serif;
    font-size: {{ block.settings.text_size }}px;
    color: {{ block.settings.text_color }};
    line-height: 1.6;
    margin-bottom: 30px;
  }

  .wedding-block__button-{{ ai_gen_id }} {
    display: inline-block;
    padding: 12px 28px;
    background-color: {{ block.settings.button_color }};
    color: {{ block.settings.button_text_color }};
    text-decoration: none;
    border-radius: {{ block.settings.button_radius }}px;
    font-family: {{ block.settings.body_font.family }}, sans-serif;
    font-size: 16px;
    transition: background-color 0.3s ease;
  }

  .wedding-block__button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_color }};
  }

  .wedding-block__image-container-{{ ai_gen_id }} {
    flex: 1;
    position: relative;
    z-index: 2;
    height: 500px;
    max-height: 80vh;
    border-radius: 8px;
    overflow: hidden;
  }

  .wedding-block__image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  .wedding-block__image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
  }

  .wedding-block__image-placeholder-{{ ai_gen_id }} svg {
    width: 80%;
    height: 80%;
    opacity: 0.7;
  }

  .wedding-block__floral-decoration-{{ ai_gen_id }} {
    position: absolute;
    z-index: 1;
    opacity: {{ block.settings.floral_opacity | divided_by: 100.0 }};
  }

  .wedding-block__floral-top-left-{{ ai_gen_id }} {
    top: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='{{ block.settings.floral_color | remove: '#' }}'%3E%3Cpath d='M50,0 C55,15 70,20 85,25 C70,30 55,35 50,50 C45,35 30,30 15,25 C30,20 45,15 50,0 Z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    transform: rotate(45deg);
  }

  .wedding-block__floral-bottom-right-{{ ai_gen_id }} {
    bottom: -30px;
    right: -30px;
    width: 150px;
    height: 150px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='{{ block.settings.floral_color | remove: '#' }}'%3E%3Cpath d='M50,0 C55,15 70,20 85,25 C70,30 55,35 50,50 C45,35 30,30 15,25 C30,20 45,15 50,0 Z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    transform: rotate(-15deg);
  }

  .wedding-block__petals-{{ ai_gen_id }} {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
    opacity: {{ block.settings.floral_opacity | divided_by: 100.0 }};
  }

  .wedding-block__petal-{{ ai_gen_id }} {
    position: absolute;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='{{ block.settings.floral_color | remove: '#' }}'%3E%3Cpath d='M50,0 C60,30 100,40 100,50 C100,60 60,70 50,100 C40,70 0,60 0,50 C0,40 40,30 50,0 Z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: contain;
    width: 30px;
    height: 30px;
  }

  @media screen and (max-width: 768px) {
    .wedding-block-{{ ai_gen_id }} {
      flex-direction: column-reverse;
      padding: 40px 20px;
    }

    .wedding-block__content-{{ ai_gen_id }} {
      padding-right: 0;
      padding-top: 30px;
      text-align: center;
    }

    .wedding-block__image-container-{{ ai_gen_id }} {
      width: 100%;
      height: 300px;
    }

    .wedding-block__heading-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | minus: 8 }}px;
    }

    .wedding-block__description-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | minus: 2 }}px;
    }
  }
{% endstyle %}

<div class="wedding-block-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="wedding-block__floral-decoration-{{ ai_gen_id }} wedding-block__floral-top-left-{{ ai_gen_id }}"></div>
  <div class="wedding-block__floral-decoration-{{ ai_gen_id }} wedding-block__floral-bottom-right-{{ ai_gen_id }}"></div>
  
  <div class="wedding-block__petals-{{ ai_gen_id }}" id="petals-{{ ai_gen_id }}">
    {% for i in (1..12) %}
      <div class="wedding-block__petal-{{ ai_gen_id }}" style="
        top: {{ forloop.index | times: 7 | modulo: 100 }}%;
        left: {{ forloop.index | times: 13 | modulo: 100 }}%;
        transform: rotate({{ forloop.index | times: 30 }}deg) scale({{ forloop.index | modulo: 5 | plus: 5 | divided_by: 10.0 }});
        opacity: {{ forloop.index | modulo: 7 | plus: 3 | divided_by: 10.0 }};
      "></div>
    {% endfor %}
  </div>
  
  <div class="wedding-block__content-{{ ai_gen_id }}">
    <h2 class="wedding-block__heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
    <div class="wedding-block__description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
    {% if block.settings.button_text != blank and block.settings.button_link != blank %}
      <a href="{{ block.settings.button_link }}" class="wedding-block__button-{{ ai_gen_id }}">
        {{ block.settings.button_text }}
      </a>
    {% endif %}
  </div>
  
  <div class="wedding-block__image-container-{{ ai_gen_id }}">
    {% if block.settings.image %}
      <img 
        src="{{ block.settings.image | image_url: width: 1000 }}" 
        alt="{{ block.settings.image.alt | escape }}"
        loading="lazy"
        class="wedding-block__image-{{ ai_gen_id }}"
      >
    {% else %}
      <div class="wedding-block__image-placeholder-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>
</div>

<script>
  (function() {
    const petalsContainer = document.getElementById('petals-{{ ai_gen_id }}');
    if (!petalsContainer) return;
    
    // Add subtle animation to petals
    const petals = petalsContainer.querySelectorAll('.wedding-block__petal-{{ ai_gen_id }}');
    petals.forEach((petal, index) => {
      const delay = index * 0.2;
      const duration = 15 + (index % 10);
      
      petal.style.animation = `float-{{ ai_gen_id }} ${duration}s ease-in-out ${delay}s infinite alternate`;
    });
    
    // Add keyframes for floating animation
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
      @keyframes float-{{ ai_gen_id }} {
        0% {
          transform: translate(0, 0) rotate(${Math.random() * 360}deg);
        }
        100% {
          transform: translate(${Math.random() * 20 - 10}px, ${Math.random() * 20 - 10}px) rotate(${Math.random() * 360 + 180}deg);
        }
      }
    `;
    document.head.appendChild(styleSheet);
  })();
</script>

{% schema %}
{
  "name": "Wedding Feature",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Your Perfect Wedding Day"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Celebrate your special day with elegance and style. Our wedding packages are designed to create unforgettable moments that you'll cherish forever.</p>"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "Learn More"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#FFF0F5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#4A4A4A"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "#D8A7B1"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "button_hover_color",
      "label": "Button hover color",
      "default": "#C48490"
    },
    {
      "type": "range",
      "id": "button_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Button corner radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Floral Elements"
    },
    {
      "type": "color",
      "id": "floral_color",
      "label": "Floral color",
      "default": "#F8C8DC"
    },
    {
      "type": "range",
      "id": "floral_opacity",
      "min": 10,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Floral opacity",
      "default": 50
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "font_picker",
      "id": "heading_font",
      "label": "Heading font",
      "default": "serif"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 36
    },
    {
      "type": "font_picker",
      "id": "body_font",
      "label": "Body font",
      "default": "sans-serif"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    }
  ],
  "presets": [
    {
      "name": "Wedding Feature"
    }
  ]
}
{% endschema %}