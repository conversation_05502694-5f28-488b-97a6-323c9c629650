{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px 0;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 20px;
  }

  .ai-sitemap-footer__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: 20px;
  }

  .ai-sitemap-footer__section-{{ ai_gen_id }} {
    margin-bottom: 15px;
  }

  .ai-sitemap-footer__title-{{ ai_gen_id }} {
    font-weight: 600;
    margin-bottom: 10px;
    color: {{ block.settings.heading_color }};
    font-size: {{ block.settings.heading_size }}px;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer__link-item-{{ ai_gen_id }} {
    margin-bottom: 5px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    text-decoration: none;
    color: {{ block.settings.text_color }};
    transition: color 0.2s ease;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid {{ block.settings.divider_color }};
    text-align: center;
    font-size: {{ block.settings.copyright_size }}px;
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__grid-{{ ai_gen_id }}">
      {% if block.settings.menu_1 != blank %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.menu_1_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_1].links %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_2 != blank %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.menu_2_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_2].links %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_3 != blank %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.menu_3_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_3].links %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_4 != blank %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.menu_4_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_4].links %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    {% if block.settings.show_copyright %}
      <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
        {{ block.settings.copyright_text }} &copy; {% if block.settings.show_current_year %}{{ 'now' | date: '%Y' }}{% endif %}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "label": "Columns on desktop",
      "default": 4
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Heading size",
      "default": 16
    },
    {
      "type": "range",
      "id": "copyright_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Copyright font size",
      "default": 12
    },
    {
      "type": "header",
      "content": "Menu 1"
    },
    {
      "type": "text",
      "id": "menu_1_title",
      "label": "Title",
      "default": "Shop"
    },
    {
      "type": "link_list",
      "id": "menu_1",
      "label": "Menu"
    },
    {
      "type": "header",
      "content": "Menu 2"
    },
    {
      "type": "text",
      "id": "menu_2_title",
      "label": "Title",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "menu_2",
      "label": "Menu"
    },
    {
      "type": "header",
      "content": "Menu 3"
    },
    {
      "type": "text",
      "id": "menu_3_title",
      "label": "Title",
      "default": "Customer Service"
    },
    {
      "type": "link_list",
      "id": "menu_3",
      "label": "Menu"
    },
    {
      "type": "header",
      "content": "Menu 4"
    },
    {
      "type": "text",
      "id": "menu_4_title",
      "label": "Title",
      "default": "Company"
    },
    {
      "type": "link_list",
      "id": "menu_4",
      "label": "Menu"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "checkbox",
      "id": "show_copyright",
      "label": "Show copyright",
      "default": true
    },
    {
      "type": "text",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "Your Store Name"
    },
    {
      "type": "checkbox",
      "id": "show_current_year",
      "label": "Show current year",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}