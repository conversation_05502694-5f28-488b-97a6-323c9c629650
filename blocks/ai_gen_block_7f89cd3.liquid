{% doc %}
  @prompt
    Create a compact section with two images side by side horizontally. Use CSS flexbox or grid to arrange images in a row. Set max-width to 800px for the container, each image should take equal width (50% each). Include two image picker settings in the schema. Add responsive design so images stack vertically on mobile devices. Use object-fit cover to maintain proportions and add small gap between images.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .dual-image-container-{{ ai_gen_id }} {
    display: flex;
    flex-direction: row;
    gap: {{ block.settings.gap }}px;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
  }

  .dual-image-wrapper-{{ ai_gen_id }} {
    flex: 1;
    width: 50%;
    position: relative;
    aspect-ratio: {{ block.settings.aspect_ratio }};
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .dual-image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .dual-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .dual-image-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
  }

  @media screen and (max-width: 749px) {
    .dual-image-container-{{ ai_gen_id }} {
      flex-direction: column;
    }

    .dual-image-wrapper-{{ ai_gen_id }} {
      width: 100%;
    }
  }
{% endstyle %}

<div class="dual-image-container-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="dual-image-wrapper-{{ ai_gen_id }}">
    {% if block.settings.image_1 %}
      <img 
        src="{{ block.settings.image_1 | image_url: width: 800 }}"
        alt="{{ block.settings.image_1.alt | escape }}"
        loading="lazy"
        class="dual-image-{{ ai_gen_id }}"
        width="{{ block.settings.image_1.width }}"
        height="{{ block.settings.image_1.height }}"
      >
    {% else %}
      <div class="dual-image-placeholder-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>
  
  <div class="dual-image-wrapper-{{ ai_gen_id }}">
    {% if block.settings.image_2 %}
      <img 
        src="{{ block.settings.image_2 | image_url: width: 800 }}"
        alt="{{ block.settings.image_2.alt | escape }}"
        loading="lazy"
        class="dual-image-{{ ai_gen_id }}"
        width="{{ block.settings.image_2.width }}"
        height="{{ block.settings.image_2.height }}"
      >
    {% else %}
      <div class="dual-image-placeholder-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Dual Images",
  "settings": [
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "First image"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Second image"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 0,
      "max": 40,
      "step": 4,
      "unit": "px",
      "label": "Gap between images",
      "default": 16
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 0
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Aspect ratio",
      "options": [
        {
          "value": "1/1",
          "label": "1:1 (Square)"
        },
        {
          "value": "3/2",
          "label": "3:2"
        },
        {
          "value": "4/3",
          "label": "4:3"
        },
        {
          "value": "16/9",
          "label": "16:9"
        }
      ],
      "default": "1/1"
    }
  ],
  "presets": [
    {
      "name": "Dual Images"
    }
  ],
  "tag": null
}
{% endschema %}