{% doc %}
  @prompt
    Create a compact single image section with smaller dimensions. Set max-width to 600px, add padding for mobile responsiveness, and include height settings to make the section more compact. Use object-fit cover to maintain image proportions in a smaller container. Add settings for adjustable width and height in the schema.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .compact-image-container-{{ ai_gen_id }} {
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
    padding: {{ block.settings.padding_vertical }}px {{ block.settings.padding_horizontal }}px;}

  .compact-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: {{ block.settings.image_height }}px;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
    {% if block.settings.add_shadow %}
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    {% endif %}
  }

  .compact-image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: {{ block.settings.image_fit }};
    display: block;
  }

  .compact-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .compact-image-placeholder-{{ ai_gen_id }} svg {
    width: 80%;
    height: 80%;
    max-height: 200px;
  }

  @media screen and (max-width: 749px) {
    .compact-image-wrapper-{{ ai_gen_id }} {
      height: {{ block.settings.image_height_mobile }}px;
    }
  }
{% endstyle %}

<div class="compact-image-container-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="compact-image-wrapper-{{ ai_gen_id }}">
    {% if block.settings.image %}
      <img 
        src="{{ block.settings.image | image_url: width: block.settings.max_width }}"
        alt="{{ block.settings.image.alt | escape }}"
        width="{{ block.settings.image.width }}"
        height="{{ block.settings.image.height }}"
        loading="lazy"
        class="compact-image-{{ ai_gen_id }}">
    {% else %}
      <div class="compact-image-placeholder-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Compact Image",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "select",
      "id": "image_fit",
      "label": "Image fit",
      "options": [
        {
          "value": "cover",
          "label": "Cover (fills space)"
        },
        {
          "value": "contain",
          "label": "Contain (shows full image)"
        }
      ],
      "default": "cover"
    },
    {
      "type": "header",
      "content": "Dimensions"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 200,
      "max": 800,
      "step": 50,
      "unit": "px",
      "label": "Maximum width",
      "default": 600
    },
    {
      "type": "range",
      "id": "image_height",
      "min": 100,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Image height",
      "default": 300
    },
    {
      "type": "range",
      "id": "image_height_mobile",
      "min": 100,
      "max": 400,
      "step": 10,
      "unit": "px",
      "label": "Image height on mobile",
      "default": 200
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Horizontal padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_vertical",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Vertical padding",
      "default": 20
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "add_shadow",
      "label": "Add shadow",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Compact Image"
    }
  ]
}
{% endschema %}