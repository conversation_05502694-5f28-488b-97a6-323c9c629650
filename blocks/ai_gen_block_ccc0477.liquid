{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    padding: {{ block.settings.padding_top }}px {{ block.settings.padding_horizontal }}px {{ block.settings.padding_bottom }}px;
    font-size: {{ block.settings.font_size }}px;
    line-height: 1.4;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-sitemap-footer__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.column_gap }}px {{ block.settings.row_gap }}px;margin-bottom: {{ block.settings.copyright_spacing }}px;
  }

  .ai-sitemap-footer__column-{{ ai_gen_id }} {
    min-width: 0;
  }

  .ai-sitemap-footer__column-title-{{ ai_gen_id }} {
    font-weight: 600;
    margin-bottom: 8px;
    color: {{ block.settings.heading_color }};
    font-size: {{ block.settings.heading_font_size }}px;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer__link-item-{{ ai_gen_id }} {
    margin-bottom: 4px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
    display: block;
    word-break: break-word;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    text-align: {{ block.settings.copyright_alignment }};
    padding-top: {{ block.settings.copyright_spacing }}px;
    border-top: 1px solid {{ block.settings.border_color }};
    color: {{ block.settings.copyright_color }};
    font-size: {{ block.settings.copyright_font_size }}px;
  }

  @media screen and (max-width: 768px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_tablet }}, 1fr);
      gap: {{ block.settings.column_gap | times: 0.8 }}px {{ block.settings.row_gap | times: 0.8 }}px;
    }

    .ai-sitemap-footer-{{ ai_gen_id }} {
      padding: {{ block.settings.padding_top | times: 0.8 }}px {{ block.settings.padding_horizontal | times: 0.8 }}px {{ block.settings.padding_bottom | times: 0.8 }}px;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<footer class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__grid-{{ ai_gen_id }}">
      {% if block.settings.show_main_menu and linklists[block.settings.main_menu].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.main_menu_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.main_menu].links limit: block.settings.max_links_per_column %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_footer_menu and linklists[block.settings.footer_menu].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.footer_menu_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.footer_menu].links limit: block.settings.max_links_per_column %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_pages %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.pages_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for page in pages limit: block.settings.max_links_per_column %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ page.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ page.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_collections %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.collections_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for collection in collections limit: block.settings.max_links_per_column %}
              {% unless collection.handle == 'frontpage' %}
                <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                  <a href="{{ collection.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ collection.title }}</a>
                </li>
              {% endunless %}
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_blogs %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.blogs_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for blog in blogs limit: block.settings.max_links_per_column %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ blog.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ blog.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}</div>

    {% if block.settings.copyright_text != blank %}
      <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
        {{ block.settings.copyright_text }}
      </div>
    {% endif %}
  </div>
</footer>

{% schema %}
{
  "name": "Compact Sitemap Footer",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "checkbox",
      "id": "show_main_menu",
      "label": "Show main menu",
      "default": true
    },
    {
      "type": "link_list",
      "id": "main_menu",
      "label": "Main menu"
    },
    {
      "type": "text",
      "id": "main_menu_title",
      "label": "Main menu title",
      "default": "Navigation"
    },
    {
      "type": "checkbox",
      "id": "show_footer_menu",
      "label": "Show footer menu",
      "default": true
    },
    {
      "type": "link_list",
      "id": "footer_menu",
      "label": "Footer menu"
    },
    {
      "type": "text",
      "id": "footer_menu_title",
      "label": "Footer menu title",
      "default": "Quick Links"
    },
    {
      "type": "checkbox",
      "id": "show_pages",
      "label": "Show pages",
      "default": true
    },
    {
      "type": "text",
      "id": "pages_title",
      "label": "Pages section title",
      "default": "Pages"
    },
    {
      "type": "checkbox",
      "id": "show_collections",
      "label": "Show collections",
      "default": true
    },
    {
      "type": "text",
      "id": "collections_title",
      "label": "Collections section title",
      "default": "Collections"
    },
    {
      "type": "checkbox",
      "id": "show_blogs",
      "label": "Show blogs",
      "default": false
    },
    {
      "type": "text",
      "id": "blogs_title",
      "label": "Blogs section title",
      "default": "Blogs"
    },
    {
      "type": "range",
      "id": "max_links_per_column",
      "min": 3,
      "max": 15,
      "step": 1,
      "label": "Maximum links per column",
      "default": 8
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "options": [
        {"value": "2", "label": "2"},
        {"value": "3", "label": "3"},
        {"value": "4", "label": "4"},
        {"value": "5", "label": "5"}
      ],
      "default": "4"
    },
    {
      "type": "select",
      "id": "columns_tablet",
      "label": "Columns on tablet",
      "options": [
        {"value": "2", "label": "2"},
        {"value": "3", "label": "3"}
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {"value": "1", "label": "1"},
        {"value": "2", "label": "2"}
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "column_gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Column gap",
      "default": 30
    },
    {
      "type": "range",
      "id": "row_gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Row gap",
      "default": 20
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Top padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Bottom padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Horizontal padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "copyright_spacing",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Copyright spacing",
      "default": 20
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Link font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "heading_font_size",
      "min": 14,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Heading font size",
      "default": 16
    },
    {
      "type": "range",
      "id": "copyright_font_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Copyright font size",
      "default": 12
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f8f8f8"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#e0e0e0"
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright text color",
      "default": "#999999"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "<p>&copy; 2024 Your Store Name. All rights reserved.</p>"
    },
    {
      "type": "select",
      "id": "copyright_alignment",
      "label": "Copyright alignment",
      "options": [
        {"value": "left", "label": "Left"},
        {"value": "center", "label": "Center"},
        {"value": "right", "label": "Right"}
      ],
      "default": "center"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap Footer"
    }
  ],
  "tag": null
}
{% endschema %}