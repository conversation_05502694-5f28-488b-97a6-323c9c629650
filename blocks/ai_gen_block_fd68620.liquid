{% doc %}
  @prompt
    Create a simple single image section with one image picker setting. Use basic Shopify liquid code to display the image with proper responsive design. Include basic styling with max-width container and ensure the image displays correctly when uploaded. Keep the code minimal and focused on image visibility.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-image-block-{{ ai_gen_id }} {
    display: block;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: auto;
    overflow: hidden;
  }

  .ai-image-{{ ai_gen_id }} {
    display: block;
    width: 100%;
    height: auto;
    max-width: 100%;
  }

  .ai-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    aspect-ratio: 16/9;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .ai-image-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
  }
{% endstyle %}

<div class="ai-image-block-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-image-wrapper-{{ ai_gen_id }}">
    {% if block.settings.image != blank %}
      <img
        src="{{ block.settings.image | image_url: width: 2000 }}"
        alt="{{ block.settings.image.alt | escape }}"
        loading="lazy"
        width="{{ block.settings.image.width }}"
        height="{{ block.settings.image.height }}"
        class="ai-image-{{ ai_gen_id }}"
      >
    {% else %}
      <div class="ai-image-placeholder-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Simple Image",
  "tag": null,
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
  ],
  "presets": [
    {
      "name": "Simple Image"
    }
  ]
}
{% endschema %}