{% doc %}
  @prompt
    Create a before/after comparison section with 4 horizontal cards side by side. Each card should have a before/after image slider that allows users to drag to reveal the transformation. The layout should be compact and beautiful, with proper image aspect ratio preservation to prevent distortion or stretching. Include customizable settings for uploading before and after images for each of the 4 cards, with smooth slider functionality and responsive design.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-before-after-section-{{ ai_gen_id }} {
    padding: 40px 20px;
    background-color: {{ block.settings.section_background }};
  }

  .ai-before-after-container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-before-after-heading-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 40px;
    color: {{ block.settings.heading_color }};
    font-size: {{ block.settings.heading_size }}px;
    font-weight: 600;
  }

  .ai-before-after-grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .ai-before-after-card-{{ ai_gen_id }} {
    background: {{ block.settings.card_background }};
    border-radius: {{ block.settings.card_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
  }

  .ai-before-after-card-{{ ai_gen_id }}:hover {
    transform: translateY(-4px);
  }

  .ai-before-after-slider-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    aspect-ratio: {{ block.settings.image_aspect_ratio }};
    overflow: hidden;
    cursor: grab;
    user-select: none;
  }

  .ai-before-after-slider-{{ ai_gen_id }}:active {
    cursor: grabbing;
  }

  .ai-before-after-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;}

  .ai-before-after-before-{{ ai_gen_id }} {
    z-index: 2;
    clip-path: inset(0 50% 0 0);
    transition: clip-path 0.1s ease;
  }

  .ai-before-after-after-{{ ai_gen_id }} {
    z-index: 1;
  }

  .ai-before-after-divider-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: 3px;
    height: 100%;
    background: {{ block.settings.divider_color }};
    z-index: 3;
    transform: translateX(-50%);
    transition: left 0.1s ease;
  }

  .ai-before-after-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: {{ block.settings.handle_color }};
    border: 3px solid {{ block.settings.handle_border_color }};
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 4;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: left 0.1s ease;
  }

  .ai-before-after-handle-{{ ai_gen_id }}::before,
  .ai-before-after-handle-{{ ai_gen_id }}::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;}

  .ai-before-after-handle-{{ ai_gen_id }}::before {
    border-width: 6px 8px 6px 0;
    border-color: transparent {{ block.settings.handle_arrow_color }} transparent transparent;
    left: 8px;
  }

  .ai-before-after-handle-{{ ai_gen_id }}::after {
    border-width: 6px 0 6px 8px;
    border-color: transparent transparent transparent {{ block.settings.handle_arrow_color }};
    right: 8px;
  }

  .ai-before-after-labels-{{ ai_gen_id }} {
    position: absolute;
    top: 16px;
    left: 0;
    right: 0;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    pointer-events: none;
  }

  .ai-before-after-label-{{ ai_gen_id }} {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .ai-before-after-card-content-{{ ai_gen_id }} {
    padding: 20px;
    text-align: center;}

  .ai-before-after-card-title-{{ ai_gen_id }} {
    color: {{ block.settings.card_title_color }};
    font-size: {{ block.settings.card_title_size }}px;
    font-weight: 600;
    margin: 0 0 8px;
  }

  .ai-before-after-card-description-{{ ai_gen_id }} {
    color: {{ block.settings.card_description_color }};
    font-size: {{ block.settings.card_description_size }}px;
    line-height: 1.4;
    margin: 0;
  }

  .ai-before-after-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;}

  @media screen and (max-width: 990px) {
    .ai-before-after-grid-{{ ai_gen_id }} {
      grid-template-columns: repeat(2, 1fr);gap: 16px;
    }
  }

  @media screen and (max-width: 749px) {
    .ai-before-after-section-{{ ai_gen_id }} {
      padding: 30px 16px;
    }

    .ai-before-after-grid-{{ ai_gen_id }} {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .ai-before-after-heading-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | times: 0.8 }}px;margin-bottom: 30px;
    }

    .ai-before-after-card-content-{{ ai_gen_id }} {
      padding: 16px;
    }
  }
{% endstyle %}

<before-after-comparison-{{ ai_gen_id }}
  class="ai-before-after-section-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-before-after-container-{{ ai_gen_id }}">
    {% if block.settings.section_heading != blank %}
      <h2 class="ai-before-after-heading-{{ ai_gen_id }}">{{ block.settings.section_heading }}</h2>
    {% endif %}

    <div class="ai-before-after-grid-{{ ai_gen_id }}">
      {% for i in (1..4) %}
        {% liquid
          assign before_image_key = 'card_' | append: i | append: '_before_image'
          assign after_image_key = 'card_' | append: i | append: '_after_image'
          assign title_key = 'card_' | append: i | append: '_title'
          assign description_key = 'card_' | append: i | append: '_description'

          assign before_image = block.settings[before_image_key]
          assign after_image = block.settings[after_image_key]
          assign card_title = block.settings[title_key]
          assign card_description = block.settings[description_key]
        %}<div class="ai-before-after-card-{{ ai_gen_id }}">
          <div
            class="ai-before-after-slider-{{ ai_gen_id }}"
            data-card="{{ i }}"
          >
            {% if before_image and after_image %}
              <img
                src="{{ after_image | image_url: width: 600 }}"
                alt="After image {{ i }}"
                class="ai-before-after-image-{{ ai_gen_id }} ai-before-after-after-{{ ai_gen_id }}"
                loading="lazy"
              ><img
                src="{{ before_image | image_url: width: 600 }}"
                alt="Before image {{ i }}"
                class="ai-before-after-image-{{ ai_gen_id }} ai-before-after-before-{{ ai_gen_id }}"
                loading="lazy"
              >
              <div class="ai-before-after-divider-{{ ai_gen_id }}"></div>
              <div class="ai-before-after-handle-{{ ai_gen_id }}"></div>
              <div class="ai-before-after-labels-{{ ai_gen_id }}">
                <span class="ai-before-after-label-{{ ai_gen_id }}">Before</span>
                <span class="ai-before-after-label-{{ ai_gen_id }}">After</span>
              </div>
            {% else %}
              <div class="ai-before-after-placeholder-{{ ai_gen_id }}">
                Upload before & after images
              </div>
            {% endif %}
          </div>

          {% if card_title != blank or card_description != blank %}
            <div class="ai-before-after-card-content-{{ ai_gen_id }}">
              {% if card_title != blank %}
                <h3 class="ai-before-after-card-title-{{ ai_gen_id }}">{{ card_title }}</h3>
              {% endif %}
              {% if card_description != blank %}
                <p class="ai-before-after-card-description-{{ ai_gen_id }}">{{ card_description }}</p>
              {% endif %}
            </div>
          {% endif %}
        </div>
      {% endfor %}
    </div>
  </div>
</before-after-comparison-{{ ai_gen_id }}>

<script>
  (function() {
    class BeforeAfterComparison{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.sliders = [];}

      connectedCallback() {
        this.initializeSliders();
      }

      initializeSliders() {
        const sliderElements = this.querySelectorAll('.ai-before-after-slider-{{ ai_gen_id }}');
        sliderElements.forEach((slider) => {
          const beforeImage = slider.querySelector('.ai-before-after-before-{{ ai_gen_id }}');
          const divider = slider.querySelector('.ai-before-after-divider-{{ ai_gen_id }}');
          const handle = slider.querySelector('.ai-before-after-handle-{{ ai_gen_id }}');
          
          if (!beforeImage || !divider || !handle) return;

          const sliderData = {
            element: slider,
            beforeImage,
            divider,
            handle,
            isDragging: false,
            currentPosition: 50
          };

          this.sliders.push(sliderData);
          this.setupSliderEvents(sliderData);
        });
      }

      setupSliderEvents(sliderData) {
        const { element, beforeImage, divider, handle } = sliderData;

        const updatePosition = (percentage) => {
          const clampedPercentage = Math.max(0, Math.min(100, percentage));
          sliderData.currentPosition = clampedPercentage;
          
          beforeImage.style.clipPath = `inset(0 ${100 - clampedPercentage}% 0 0)`;
          divider.style.left = `${clampedPercentage}%`;
          handle.style.left = `${clampedPercentage}%`;
        };

        const getPercentageFromEvent = (event) => {
          const rect = element.getBoundingClientRect();
          const clientX = event.type.includes('touch') ? event.touches[0].clientX : event.clientX;
          return ((clientX - rect.left) / rect.width) * 100;
        };

        const handleStart = (event) => {
          event.preventDefault();
          sliderData.isDragging = true;
          element.style.cursor = 'grabbing';
          const percentage = getPercentageFromEvent(event);
          updatePosition(percentage);
        };

        const handleMove = (event) => {
          if (!sliderData.isDragging) return;
          event.preventDefault();
          
          const percentage = getPercentageFromEvent(event);
          updatePosition(percentage);
        };

        const handleEnd = () => {
          sliderData.isDragging = false;
          element.style.cursor = 'grab';
        };

        element.addEventListener('mousedown', handleStart);
        element.addEventListener('touchstart', handleStart, { passive: false });document.addEventListener('mousemove', handleMove);
        document.addEventListener('touchmove', handleMove, { passive: false });

        document.addEventListener('mouseup', handleEnd);
        document.addEventListener('touchend', handleEnd);

        element.addEventListener('click', (event) => {
          if (!sliderData.isDragging) {
            const percentage = getPercentageFromEvent(event);
            updatePosition(percentage);
          }
        });
      }
    }

    customElements.define('before-after-comparison-{{ ai_gen_id }}', BeforeAfterComparison{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Before/After Comparison",
  "settings": [
    {
      "type": "header",
      "content": "Section"
    },
    {
      "type": "text",
      "id": "section_heading",
      "label": "Section heading",
      "default": "Amazing Transformations"
    },
    {
      "type": "color",
      "id": "section_background",
      "label": "Section background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 32
    },
    {
      "type": "header",
      "content": "Cards"
    },
    {
      "type": "color",
      "id": "card_background",
      "label": "Card background",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "card_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Card border radius",
      "default": 8
    },
    {
      "type": "select",
      "id": "image_aspect_ratio",
      "label": "Image aspect ratio",
      "options": [
        {
          "value": "1/1",
          "label": "Square (1:1)"
        },
        {
          "value": "4/3",
          "label": "Landscape (4:3)"
        },
        {
          "value": "3/4",
          "label": "Portrait (3:4)"
        },
        {
          "value": "16/9",
          "label": "Wide (16:9)"
        }
      ],
      "default": "4/3"
    },
    {
      "type": "color",
      "id": "card_title_color",
      "label": "Card title color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "card_title_size",
      "min": 14,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Card title size",
      "default": 18
    },
    {
      "type": "color",
      "id": "card_description_color",
      "label": "Card description color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "card_description_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Card description size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Slider"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "handle_color",
      "label": "Handle color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "handle_border_color",
      "label": "Handle border color",
      "default": "#e0e0e0"
    },
    {
      "type": "color",
      "id": "handle_arrow_color",
      "label": "Handle arrow color",
      "default": "#666666"
    },
    {
      "type": "header",
      "content": "Card 1"
    },
    {
      "type": "image_picker",
      "id": "card_1_before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "card_1_after_image",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "card_1_title",
      "label": "Title",
      "default": "Transformation 1"
    },
    {
      "type": "textarea",
      "id": "card_1_description",
      "label": "Description",
      "default": "Amazing results in just 30 days"
    },
    {
      "type": "header",
      "content": "Card 2"
    },
    {
      "type": "image_picker",
      "id": "card_2_before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "card_2_after_image",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "card_2_title",
      "label": "Title",
      "default": "Transformation 2"
    },
    {
      "type": "textarea",
      "id": "card_2_description",
      "label": "Description",
      "default": "Incredible change with our solution"
    },
    {
      "type": "header",
      "content": "Card 3"
    },
    {
      "type": "image_picker",
      "id": "card_3_before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "card_3_after_image",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "card_3_title",
      "label": "Title",
      "default": "Transformation 3"
    },
    {
      "type": "textarea",
      "id": "card_3_description",
      "label": "Description",
      "default": "Outstanding improvement achieved"
    },
    {
      "type": "header",
      "content": "Card 4"
    },
    {
      "type": "image_picker",
      "id": "card_4_before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "card_4_after_image",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "card_4_title",
      "label": "Title",
      "default": "Transformation 4"
    },
    {
      "type": "textarea",
      "id": "card_4_description",
      "label": "Description",
      "default": "Remarkable results you can see"
    }
  ],
  "presets": [
    {
      "name": "Before/After Comparison"
    }
  ]
}
{% endschema %}