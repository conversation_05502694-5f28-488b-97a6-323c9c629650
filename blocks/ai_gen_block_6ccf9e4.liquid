{% doc %}
  @prompt
    Create a compact footer sitemap section for a travel agency with customizable settings. Include 4 columns: 1) Tours (All Tours, Our most popular trips, Home page), 2) Popular Tours (Weekend Batumi 5 Days, Around Georgia's Roads 14-Day Jeep Tour, Around Georgia's Roads 10-Day Jeep Tour), 3) Company (About Us, Contact, Privacy Policy, Terms of Service), 4) Contact Info (Phone, Email, Address). Add theme settings for: background color (default white), text color, padding top/bottom, section height, font size. Make it compact and fully customizable through theme editor settings.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .footer-sitemap-{{ ai_gen_id }} {
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    padding-top: {{ block.settings.padding_top }}px;
    padding-bottom: {{ block.settings.padding_bottom }}px;
    min-height: {{ block.settings.section_height }}px;
    font-size: {{ block.settings.font_size }}px;
  }

  .footer-sitemap-container-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-sitemap-column-{{ ai_gen_id }} {
    flex: 1;
    min-width: 200px;
    margin-bottom: 20px;
    padding-right: 20px;
  }

  .footer-sitemap-column-title-{{ ai_gen_id }} {
    font-weight: bold;
    margin-bottom: 15px;
    font-size: calc({{ block.settings.font_size }}px + 2px);
    color: {{ block.settings.heading_color }};
  }

  .footer-sitemap-links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .footer-sitemap-link-{{ ai_gen_id }} {
    margin-bottom: 8px;
  }

  .footer-sitemap-link-{{ ai_gen_id }} a {
    text-decoration: none;
    color: {{ block.settings.link_color }};
    transition: color 0.2s ease;
  }

  .footer-sitemap-link-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .footer-contact-item-{{ ai_gen_id }} {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
  }

  .footer-contact-icon-{{ ai_gen_id }} {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    margin-top: 3px;
  }

  @media screen and (max-width: 767px) {
    .footer-sitemap-container-{{ ai_gen_id }} {
      flex-direction: column;
    }
    
    .footer-sitemap-column-{{ ai_gen_id }} {
      width: 100%;
      margin-bottom: 30px;
    }
  }
{% endstyle %}

<div class="footer-sitemap-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="footer-sitemap-container-{{ ai_gen_id }}">
    <!-- Tours Column -->
    <div class="footer-sitemap-column-{{ ai_gen_id }}">
      <h3 class="footer-sitemap-column-title-{{ ai_gen_id }}">{{ block.settings.column1_title }}</h3>
      <ul class="footer-sitemap-links-{{ ai_gen_id }}">
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.tours_all_link }}">{{ block.settings.tours_all_text }}</a>
        </li>
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.tours_popular_link }}">{{ block.settings.tours_popular_text }}</a>
        </li>
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.tours_home_link }}">{{ block.settings.tours_home_text }}</a>
        </li>
      </ul>
    </div>

    <!-- Popular Tours Column -->
    <div class="footer-sitemap-column-{{ ai_gen_id }}">
      <h3 class="footer-sitemap-column-title-{{ ai_gen_id }}">{{ block.settings.column2_title }}</h3>
      <ul class="footer-sitemap-links-{{ ai_gen_id }}">
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.popular_tour1_link }}">{{ block.settings.popular_tour1_text }}</a>
        </li>
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.popular_tour2_link }}">{{ block.settings.popular_tour2_text }}</a>
        </li>
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.popular_tour3_link }}">{{ block.settings.popular_tour3_text }}</a>
        </li>
      </ul>
    </div>

    <!-- Company Column -->
    <div class="footer-sitemap-column-{{ ai_gen_id }}">
      <h3 class="footer-sitemap-column-title-{{ ai_gen_id }}">{{ block.settings.column3_title }}</h3>
      <ul class="footer-sitemap-links-{{ ai_gen_id }}">
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.company_about_link }}">{{ block.settings.company_about_text }}</a>
        </li>
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.company_contact_link }}">{{ block.settings.company_contact_text }}</a>
        </li>
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.company_privacy_link }}">{{ block.settings.company_privacy_text }}</a>
        </li>
        <li class="footer-sitemap-link-{{ ai_gen_id }}">
          <a href="{{ block.settings.company_terms_link }}">{{ block.settings.company_terms_text }}</a>
        </li>
      </ul>
    </div>

    <!-- Contact Info Column -->
    <div class="footer-sitemap-column-{{ ai_gen_id }}">
      <h3 class="footer-sitemap-column-title-{{ ai_gen_id }}">{{ block.settings.column4_title }}</h3>
      <div class="footer-sitemap-links-{{ ai_gen_id }}">
        <div class="footer-contact-item-{{ ai_gen_id }}">
          <svg class="footer-contact-icon-{{ ai_gen_id }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
          </svg>
          <span>{{ block.settings.contact_phone }}</span>
        </div>
        <div class="footer-contact-item-{{ ai_gen_id }}">
          <svg class="footer-contact-icon-{{ ai_gen_id }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
          <span>{{ block.settings.contact_email }}</span>
        </div>
        <div class="footer-contact-item-{{ ai_gen_id }}">
          <svg class="footer-contact-icon-{{ ai_gen_id }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
          <span>{{ block.settings.contact_address }}</span>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Footer Sitemap",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Padding top",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Padding bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "section_height",
      "label": "Minimum height",
      "min": 100,
      "max": 500,
      "step": 10,
      "default": 200,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "font_size",
      "label": "Font size",
      "min": 12,
      "max": 20,
      "step": 1,
      "default": 14,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Column 1 - Tours"
    },
    {
      "type": "text",
      "id": "column1_title",
      "label": "Column title",
      "default": "Tours"
    },
    {
      "type": "text",
      "id": "tours_all_text",
      "label": "All tours text",
      "default": "All Tours"
    },
    {
      "type": "url",
      "id": "tours_all_link",
      "label": "All tours link"
    },
    {
      "type": "text",
      "id": "tours_popular_text",
      "label": "Popular tours text",
      "default": "Our most popular trips"
    },
    {
      "type": "url",
      "id": "tours_popular_link",
      "label": "Popular tours link"
    },
    {
      "type": "text",
      "id": "tours_home_text",
      "label": "Home page text",
      "default": "Home page"
    },
    {
      "type": "url",
      "id": "tours_home_link",
      "label": "Home page link"
    },
    {
      "type": "header",
      "content": "Column 2 - Popular Tours"
    },
    {
      "type": "text",
      "id": "column2_title",
      "label": "Column title",
      "default": "Popular Tours"
    },
    {
      "type": "text",
      "id": "popular_tour1_text",
      "label": "Tour 1 text",
      "default": "Weekend Batumi 5 Days"
    },
    {
      "type": "url",
      "id": "popular_tour1_link",
      "label": "Tour 1 link"
    },
    {
      "type": "text",
      "id": "popular_tour2_text",
      "label": "Tour 2 text",
      "default": "Around Georgia's Roads 14-Day Jeep Tour"
    },
    {
      "type": "url",
      "id": "popular_tour2_link",
      "label": "Tour 2 link"
    },
    {
      "type": "text",
      "id": "popular_tour3_text",
      "label": "Tour 3 text",
      "default": "Around Georgia's Roads 10-Day Jeep Tour"
    },
    {
      "type": "url",
      "id": "popular_tour3_link",
      "label": "Tour 3 link"
    },
    {
      "type": "header",
      "content": "Column 3 - Company"
    },
    {
      "type": "text",
      "id": "column3_title",
      "label": "Column title",
      "default": "Company"
    },
    {
      "type": "text",
      "id": "company_about_text",
      "label": "About us text",
      "default": "About Us"
    },
    {
      "type": "url",
      "id": "company_about_link",
      "label": "About us link"
    },
    {
      "type": "text",
      "id": "company_contact_text",
      "label": "Contact text",
      "default": "Contact"
    },
    {
      "type": "url",
      "id": "company_contact_link",
      "label": "Contact link"
    },
    {
      "type": "text",
      "id": "company_privacy_text",
      "label": "Privacy policy text",
      "default": "Privacy Policy"
    },
    {
      "type": "url",
      "id": "company_privacy_link",
      "label": "Privacy policy link"
    },
    {
      "type": "text",
      "id": "company_terms_text",
      "label": "Terms of service text",
      "default": "Terms of Service"
    },
    {
      "type": "url",
      "id": "company_terms_link",
      "label": "Terms of service link"
    },
    {
      "type": "header",
      "content": "Column 4 - Contact Info"
    },
    {
      "type": "text",
      "id": "column4_title",
      "label": "Column title",
      "default": "Contact Info"
    },
    {
      "type": "text",
      "id": "contact_phone",
      "label": "Phone number",
      "default": "+*********** 456"
    },
    {
      "type": "text",
      "id": "contact_email",
      "label": "Email address",
      "default": "<EMAIL>"
    },
    {
      "type": "text",
      "id": "contact_address",
      "label": "Address",
      "default": "123 Rustaveli Ave, Tbilisi, Georgia"
    }
  ],
  "presets": [
    {
      "name": "Footer Sitemap"
    }
  ],
  "tag": null
}
{% endschema %}