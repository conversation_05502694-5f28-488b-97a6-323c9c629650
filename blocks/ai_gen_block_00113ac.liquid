{% doc %}
  @prompt
    Create CSS code for RTL (right-to-left) support for Hebrew language that mirrors day navigation buttons and tour description elements. The code should detect when Hebrew is selected and apply RTL styling including reversed button order, right-aligned text, and proper spacing for tour day switcher buttons.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .rtl-support-{{ ai_gen_id }} {
    display: block;
    width: 100%;
  }

  /* Base RTL styles that will be applied when Hebrew is selected */
  html[lang="he"] .rtl-element {
    direction: rtl;
    text-align: right;
  }

  /* Mirror navigation buttons */
  html[lang="he"] .rtl-nav-buttons {
    flex-direction: row-reverse;
  }

  html[lang="he"] .rtl-nav-button {
    transform: scaleX(-1);
  }

  /* Adjust spacing and alignment for tour day switcher */
  html[lang="he"] .rtl-day-switcher {
    direction: rtl;
  }

  html[lang="he"] .rtl-day-switcher-button {
    margin-right: 0;
    margin-left: {{ block.settings.button_spacing }}px;
  }

  html[lang="he"] .rtl-day-switcher-button:last-child {
    margin-left: 0;
  }

  /* Tour description elements */
  html[lang="he"] .rtl-tour-description {
    text-align: right;
  }

  html[lang="he"] .rtl-tour-feature {
    padding-left: 0;
    padding-right: {{ block.settings.feature_indent }}px;
  }

  html[lang="he"] .rtl-tour-feature-icon {
    margin-right: 0;
    margin-left: {{ block.settings.icon_spacing }}px;
  }

  /* Additional custom RTL adjustments based on settings */
  {% if block.settings.custom_rtl_elements %}
    html[lang="he"] {{ block.settings.custom_rtl_elements }} {
      direction: rtl;
      text-align: right;
    }
  {% endif %}

  /* Apply custom CSS */
  {% if block.settings.custom_css != blank %}
    {{ block.settings.custom_css }}
  {% endif %}
{% endstyle %}

<div class="rtl-support-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.show_rtl_indicator and request.locale.iso_code == 'he' %}
    <div style="padding: 10px; background-color: {{ block.settings.indicator_color }}; color: #fff; text-align: center; margin-bottom: 10px;">
      {{ block.settings.rtl_indicator_text }}
    </div>
  {% endif %}
</div>

<script>
  (function() {
    class RtlSupport extends HTMLElement {
      constructor() {
        super();
        this.init();
      }

      init() {
        // Check if the current language is Hebrew
        const isHebrew = document.documentElement.lang === 'he';
        
        if (isHebrew) {
          this.applyRtlClasses();
        }
      }

      applyRtlClasses() {
        // Apply RTL classes to navigation elements
        const navButtons = document.querySelectorAll('{{ block.settings.nav_buttons_selector }}');
        navButtons.forEach(el => {
          el.classList.add('rtl-nav-buttons');
        });

        // Apply RTL classes to navigation button icons
        const navButtonIcons = document.querySelectorAll('{{ block.settings.nav_button_icons_selector }}');
        navButtonIcons.forEach(el => {
          el.classList.add('rtl-nav-button');
        });

        // Apply RTL classes to day switcher
        const daySwitchers = document.querySelectorAll('{{ block.settings.day_switcher_selector }}');
        daySwitchers.forEach(el => {
          el.classList.add('rtl-day-switcher');
        });

        // Apply RTL classes to day switcher buttons
        const daySwitcherButtons = document.querySelectorAll('{{ block.settings.day_switcher_buttons_selector }}');
        daySwitcherButtons.forEach(el => {
          el.classList.add('rtl-day-switcher-button');
        });

        // Apply RTL classes to tour descriptions
        const tourDescriptions = document.querySelectorAll('{{ block.settings.tour_description_selector }}');
        tourDescriptions.forEach(el => {
          el.classList.add('rtl-tour-description');
        });

        // Apply RTL classes to tour features
        const tourFeatures = document.querySelectorAll('{{ block.settings.tour_features_selector }}');
        tourFeatures.forEach(el => {
          el.classList.add('rtl-tour-feature');
        });

        // Apply RTL classes to tour feature icons
        const tourFeatureIcons = document.querySelectorAll('{{ block.settings.tour_feature_icons_selector }}');
        tourFeatureIcons.forEach(el => {
          el.classList.add('rtl-tour-feature-icon');
        });
      }
    }

    customElements.define('rtl-support-{{ ai_gen_id }}', RtlSupport);
  })();
</script>

{% schema %}
{
  "name": "RTL Support",
  "settings": [
    {
      "type": "header",
      "content": "RTL Element Selectors"
    },
    {
      "type": "paragraph",
      "content": "Enter CSS selectors for elements that need RTL support"
    },
    {
      "type": "text",
      "id": "nav_buttons_selector",
      "label": "Navigation buttons container",
      "default": ".tour-navigation, .day-navigation"
    },
    {
      "type": "text",
      "id": "nav_button_icons_selector",
      "label": "Navigation button icons",
      "default": ".nav-arrow, .nav-icon"
    },
    {
      "type": "text",
      "id": "day_switcher_selector",
      "label": "Day switcher container",
      "default": ".day-switcher, .tour-days"
    },
    {
      "type": "text",
      "id": "day_switcher_buttons_selector",
      "label": "Day switcher buttons",
      "default": ".day-button, .day-tab"
    },
    {
      "type": "text",
      "id": "tour_description_selector",
      "label": "Tour description elements",
      "default": ".tour-description, .itinerary-description"
    },
    {
      "type": "text",
      "id": "tour_features_selector",
      "label": "Tour features",
      "default": ".tour-feature, .itinerary-feature"
    },
    {
      "type": "text",
      "id": "tour_feature_icons_selector",
      "label": "Tour feature icons",
      "default": ".feature-icon, .itinerary-icon"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "range",
      "id": "button_spacing",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Button spacing",
      "default": 10
    },
    {
      "type": "range",
      "id": "feature_indent",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Feature indent",
      "default": 20
    },
    {
      "type": "range",
      "id": "icon_spacing",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Icon spacing",
      "default": 8
    },
    {
      "type": "header",
      "content": "Advanced Settings"
    },
    {
      "type": "text",
      "id": "custom_rtl_elements",
      "label": "Additional RTL elements (CSS selector)",
      "default": ".custom-element, .special-text"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS",
      "info": "Add any additional CSS needed for RTL support"
    },
    {
      "type": "header",
      "content": "Debug Options"
    },
    {
      "type": "checkbox",
      "id": "show_rtl_indicator",
      "label": "Show RTL indicator (Hebrew only)",
      "default": false
    },
    {
      "type": "text",
      "id": "rtl_indicator_text",
      "label": "RTL indicator text",
      "default": "RTL mode active - Hebrew detected"
    },
    {
      "type": "color",
      "id": "indicator_color",
      "label": "Indicator background color",
      "default": "#3a3a3a"
    }
  ],
  "presets": [
    {
      "name": "RTL Support"
    }
  ],
  "tag": null
}
{% endschema %}