{% doc %}
  @prompt
    Create an attractive contact information section with customizable fields for email address and two phone numbers. The section should have a modern, professional design with clear visual hierarchy, icons for each contact method, and be mobile-responsive. Include placeholder text that can be easily customized.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-contact-info-{{ ai_gen_id }} {
    padding: {{ block.settings.section_padding }}px;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
    max-width: 100%;
  }

  .ai-contact-info__container-{{ ai_gen_id }} {
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;}

  .ai-contact-info__header-{{ ai_gen_id }} {
    text-align: {{ block.settings.text_alignment }};
    margin-bottom: {{ block.settings.item_spacing }}px;
  }

  .ai-contact-info__title-{{ ai_gen_id }} {
    color: {{ block.settings.heading_color }};
    font-size: {{ block.settings.heading_size }}px;
    font-weight: 600;
    margin: 0 0 8px 0;line-height: 1.2;
  }

  .ai-contact-info__subtitle-{{ ai_gen_id }} {
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.subtitle_size }}px;
    margin: 0;opacity: 0.8;
  }

  .ai-contact-info__list-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: {{ block.settings.item_spacing }}px;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .ai-contact-info__item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: {{ block.settings.item_padding }}px;
    background-color: {{ block.settings.item_background_color }};
    border-radius: {{ block.settings.item_border_radius }}px;
    border: 1px solid {{ block.settings.border_color }};
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .ai-contact-info__item-{{ ai_gen_id }}:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .ai-contact-info__icon-{{ ai_gen_id }} {
    flex-shrink: 0;
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    background-color: {{ block.settings.icon_background_color }};
    color: {{ block.settings.icon_color }};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-contact-info__icon-{{ ai_gen_id }} svg {
    width: calc({{ block.settings.icon_size }}px * 0.6);
    height: calc({{ block.settings.icon_size }}px * 0.6);
  }

  .ai-contact-info__content-{{ ai_gen_id }} {
    flex-grow: 1;
  }

  .ai-contact-info__label-{{ ai_gen_id }} {
    color: {{ block.settings.label_color }};
    font-size: {{ block.settings.label_size }}px;
    font-weight: 500;
    margin: 0 0 4px 0;line-height: 1.2;
  }

  .ai-contact-info__value-{{ ai_gen_id }} {
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
    margin: 0;
    line-height: 1.4;
  }

  .ai-contact-info__link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .ai-contact-info__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  @media screen and (max-width: 749px) {
    .ai-contact-info-{{ ai_gen_id }} {
      padding: {{ block.settings.section_padding | times: 0.7 }}px;
    }

    .ai-contact-info__item-{{ ai_gen_id }} {
      padding: {{ block.settings.item_padding | times: 0.8 }}px;
      gap: 12px;
    }

    .ai-contact-info__icon-{{ ai_gen_id }} {
      width: {{ block.settings.icon_size | times: 0.9 }}px;
      height: {{ block.settings.icon_size | times: 0.9 }}px;
    }

    .ai-contact-info__title-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | times: 0.9 }}px;
    }

    .ai-contact-info__subtitle-{{ ai_gen_id }} {
      font-size: {{ block.settings.subtitle_size | times: 0.9 }}px;
    }
  }

  @media screen and (min-width: 750px) {
    {% if block.settings.layout == 'horizontal' %}
      .ai-contact-info__list-{{ ai_gen_id }} {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;}

      .ai-contact-info__item-{{ ai_gen_id }} {
        flex: 1;
        min-width: 250px;
        max-width: 350px;
      }
    {% endif %}
  }
{% endstyle %}

<div class="ai-contact-info-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-contact-info__container-{{ ai_gen_id }}">
    {% if block.settings.heading != blank or block.settings.subtitle != blank %}
      <div class="ai-contact-info__header-{{ ai_gen_id }}">
        {% if block.settings.heading != blank %}
          <h2 class="ai-contact-info__title-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
        {% endif %}
        {% if block.settings.subtitle != blank %}
          <p class="ai-contact-info__subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</p>
        {% endif %}
      </div>
    {% endif %}<ul class="ai-contact-info__list-{{ ai_gen_id }}">
      {% if block.settings.email != blank %}
        <li class="ai-contact-info__item-{{ ai_gen_id }}">
          <div class="ai-contact-info__icon-{{ ai_gen_id }}">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
          </div>
          <div class="ai-contact-info__content-{{ ai_gen_id }}">
            <h3 class="ai-contact-info__label-{{ ai_gen_id }}">{{ block.settings.email_label }}</h3>
            <p class="ai-contact-info__value-{{ ai_gen_id }}"><a href="mailto:{{ block.settings.email }}" class="ai-contact-info__link-{{ ai_gen_id }}">
                {{ block.settings.email }}
              </a>
            </p>
          </div>
        </li>
      {% endif %}

      {% if block.settings.phone_1 != blank %}
        <li class="ai-contact-info__item-{{ ai_gen_id }}">
          <div class="ai-contact-info__icon-{{ ai_gen_id }}">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
            </svg>
          </div>
          <div class="ai-contact-info__content-{{ ai_gen_id }}">
            <h3 class="ai-contact-info__label-{{ ai_gen_id }}">{{ block.settings.phone_1_label }}</h3>
            <p class="ai-contact-info__value-{{ ai_gen_id }}">
              <a href="tel:{{ block.settings.phone_1 | replace: ' ', '' | replace: '-', '' | replace: '(', '' | replace: ')', '' }}" class="ai-contact-info__link-{{ ai_gen_id }}">
                {{ block.settings.phone_1 }}
              </a>
            </p>
          </div>
        </li>
      {% endif %}

      {% if block.settings.phone_2 != blank %}
        <li class="ai-contact-info__item-{{ ai_gen_id }}">
          <div class="ai-contact-info__icon-{{ ai_gen_id }}">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
            </svg>
          </div>
          <div class="ai-contact-info__content-{{ ai_gen_id }}">
            <h3 class="ai-contact-info__label-{{ ai_gen_id }}">{{ block.settings.phone_2_label }}</h3>
            <p class="ai-contact-info__value-{{ ai_gen_id }}">
              <a href="tel:{{ block.settings.phone_2 | replace: ' ', '' | replace: '-', '' | replace: '(', '' | replace: ')', '' }}" class="ai-contact-info__link-{{ ai_gen_id }}">
                {{ block.settings.phone_2 }}
              </a>
            </p>
          </div>
        </li>
      {% endif %}
    </ul>
  </div>
</div>

{% schema %}
{
  "name": "Contact Information",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Get in Touch"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "We're here to help with any questions you may have"
    },
    {
      "type": "text",
      "id": "email",
      "label": "Email address",
      "default": "<EMAIL>"
    },
    {
      "type": "text",
      "id": "email_label",
      "label": "Email label",
      "default": "Email Us"
    },
    {
      "type": "text",
      "id": "phone_1",
      "label": "Primary phone number",
      "default": "(*************"
    },
    {
      "type": "text",
      "id": "phone_1_label",
      "label": "Primary phone label",
      "default": "Customer Service"
    },
    {
      "type": "text",
      "id": "phone_2",
      "label": "Secondary phone number",
      "default": "(*************"
    },
    {
      "type": "text",
      "id": "phone_2_label",
      "label": "Secondary phone label",
      "default": "Sales Support"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Desktop layout",
      "options": [
        {
          "value": "vertical",
          "label": "Vertical"
        },
        {
          "value": "horizontal",
          "label": "Horizontal"
        }
      ],
      "default": "vertical"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 400,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Maximum width",
      "default": 800
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Section padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "item_spacing",
      "min": 8,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Item spacing",
      "default": 20
    },
    {
      "type": "range",
      "id": "item_padding",
      "min": 8,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Item padding",
      "default": 20
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 28
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 16
    },
    {
      "type": "range",
      "id": "label_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Label size",
      "default": 14
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Icons"
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 32,
      "max": 64,
      "step": 4,
      "unit": "px",
      "label": "Icon size",
      "default": 48
    },
    {
      "type": "color",
      "id": "icon_background_color",
      "label": "Icon background color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Section background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "item_background_color",
      "label": "Item background color",
      "default": "#f8f9fa"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#e9ecef"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#212529"
    },
    {
      "type": "color",
      "id": "label_color",
      "label": "Label color",
      "default": "#495057"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#6c757d"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Border Radius"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Section border radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "item_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Item border radius",
      "default": 8
    }
  ],
  "presets": [
    {
      "name": "Contact Information"
    }
  ],
  "tag": null
}
{% endschema %}