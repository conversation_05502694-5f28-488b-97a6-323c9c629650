{% doc %}
  @prompt
    Create a simple, reliable before-and-after image comparison slider gallery for dental procedures. The gallery should have 3-4 comparison examples with clearly labeled placeholders for before and after images. Use a straightforward drag or click mechanism that works reliably across devices. Include proper image loading and display functionality to ensure uploaded images appear correctly. Add clear instructions within the code for where to upload images and how to configure each slider.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-before-after-gallery-{{ ai_gen_id }} {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .ai-before-after-heading-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 30px;
    color: {{ block.settings.heading_color }};
    font-size: {{ block.settings.heading_size }}px;
  }

  .ai-before-after-subheading-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 40px;
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
  }

  .ai-before-after-items-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: 30px;
  }

  @media screen and (max-width: 749px) {
    .ai-before-after-items-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }

  .ai-before-after-item-{{ ai_gen_id }} {
    position: relative;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .ai-before-after-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    overflow: hidden;
  }

  .ai-before-after-image-wrapper-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .ai-before-image-{{ ai_gen_id }},
  .ai-after-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-before-image-placeholder-{{ ai_gen_id }},
  .ai-after-image-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .ai-before-image-placeholder-{{ ai_gen_id }} svg,
  .ai-after-image-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-after-image-wrapper-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
  }

  .ai-slider-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background-color: {{ block.settings.slider_color }};
    transform: translateX(-50%);
    cursor: ew-resize;
    z-index: 10;
  }

  .ai-slider-button-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: {{ block.settings.slider_color }};
    border: 3px solid white;
    transform: translate(-50%, -50%);
    cursor: ew-resize;
    z-index: 11;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-slider-button-{{ ai_gen_id }}::before,
  .ai-slider-button-{{ ai_gen_id }}::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 2px;
    background-color: white;
  }

  .ai-slider-button-{{ ai_gen_id }}::before {
    transform: translateX(-5px) rotate(45deg);
  }

  .ai-slider-button-{{ ai_gen_id }}::after {
    transform: translateX(5px) rotate(-45deg);
  }

  .ai-before-label-{{ ai_gen_id }},
  .ai-after-label-{{ ai_gen_id }} {
    position: absolute;
    top: 20px;
    color: white;
    background-color: {{ block.settings.label_bg_color }};
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    z-index: 9;
  }

  .ai-before-label-{{ ai_gen_id }} {
    left: 20px;
  }

  .ai-after-label-{{ ai_gen_id }} {
    right: 20px;
  }

  .ai-before-after-title-{{ ai_gen_id }} {
    margin-top: 15px;
    margin-bottom: 5px;
    font-size: 18px;
    font-weight: bold;
    color: {{ block.settings.text_color }};
  }

  .ai-before-after-description-{{ ai_gen_id }} {
    font-size: 14px;
    color: {{ block.settings.text_color }};
    line-height: 1.5;
  }
{% endstyle %}

<div class="ai-before-after-gallery-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.heading != blank %}
    <h2 class="ai-before-after-heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
  {% endif %}
  
  {% if block.settings.subheading != blank %}
    <div class="ai-before-after-subheading-{{ ai_gen_id }}">{{ block.settings.subheading }}</div>
  {% endif %}
  
  <div class="ai-before-after-items-{{ ai_gen_id }}">
    {% for i in (1..4) %}
      {% liquid
        assign before_image_key = 'before_image_' | append: i
        assign after_image_key = 'after_image_' | append: i
        assign title_key = 'title_' | append: i
        assign description_key = 'description_' | append: i
        assign before_label_key = 'before_label_' | append: i
        assign after_label_key = 'after_label_' | append: i
        
        assign before_image = block.settings[before_image_key]
        assign after_image = block.settings[after_image_key]
        assign title = block.settings[title_key]
        assign description = block.settings[description_key]
        assign before_label = block.settings[before_label_key]
        assign after_label = block.settings[after_label_key]
      %}
      
      {% if title != blank or before_image != blank or after_image != blank %}
        <div class="ai-before-after-item-{{ ai_gen_id }}">
          <div class="ai-before-after-container-{{ ai_gen_id }}" data-index="{{ i }}">
            <div class="ai-before-after-image-wrapper-{{ ai_gen_id }}">
              {% if before_image != blank %}
                <img 
                  src="{{ before_image | image_url: width: 800 }}" 
                  alt="{{ before_label | escape }}" 
                  class="ai-before-image-{{ ai_gen_id }}" 
                  loading="lazy"
                  width="{{ before_image.width }}"
                  height="{{ before_image.height }}"
                >
              {% else %}
                <div class="ai-before-image-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}<div class="ai-after-image-wrapper-{{ ai_gen_id }}">
                {% if after_image != blank %}
                  <img 
                    src="{{ after_image | image_url: width: 800 }}" 
                    alt="{{ after_label | escape }}" 
                    class="ai-after-image-{{ ai_gen_id }}" 
                    loading="lazy"
                    width="{{ after_image.width }}"
                    height="{{ after_image.height }}"
                  >
                {% else %}
                  <div class="ai-after-image-placeholder-{{ ai_gen_id }}">
                    {{ 'image' | placeholder_svg_tag }}
                  </div>
                {% endif %}
              </div>
              
              <div class="ai-slider-handle-{{ ai_gen_id }}">
                <div class="ai-slider-button-{{ ai_gen_id }}"></div>
              </div>
              
              <div class="ai-before-label-{{ ai_gen_id }}">{{ before_label }}</div>
              <div class="ai-after-label-{{ ai_gen_id }}">{{ after_label }}</div>
            </div>
          </div>
          
          {% if title != blank %}
            <h3 class="ai-before-after-title-{{ ai_gen_id }}">{{ title }}</h3>
          {% endif %}
          
          {% if description != blank %}
            <div class="ai-before-after-description-{{ ai_gen_id }}">{{ description }}</div>
          {% endif %}
        </div>
      {% endif %}
    {% endfor %}
  </div>
</div>

<script>
  (function() {
    class BeforeAfterSlider extends HTMLElement {
      constructor() {
        super();
        this.sliders = this.querySelectorAll('.ai-before-after-container-{{ ai_gen_id }}');
        this.isDragging = false;
        this.activeSlider = null;
        this.startX = 0;
      }

      connectedCallback() {
        this.setupEventListeners();
        this.initializeSliders();
      }

      initializeSliders() {
        this.sliders.forEach(slider => {
          const afterWrapper = slider.querySelector('.ai-after-image-wrapper-{{ ai_gen_id }}');
          const handle = slider.querySelector('.ai-slider-handle-{{ ai_gen_id }}');
          
          // Set initial position
          this.setSliderPosition(slider, 50);
        });
      }

      setupEventListeners() {
        this.sliders.forEach(slider => {
          // Mouse events
          slider.addEventListener('mousedown', this.startDragging.bind(this));
          document.addEventListener('mousemove', this.drag.bind(this));
          document.addEventListener('mouseup', this.stopDragging.bind(this));
          
          // Touch events
          slider.addEventListener('touchstart', this.startDragging.bind(this), { passive: true });
          document.addEventListener('touchmove', this.drag.bind(this), { passive: false });
          document.addEventListener('touchend', this.stopDragging.bind(this));
          
          // Click event for immediate position change
          slider.addEventListener('click', this.handleClick.bind(this));
        });
      }

      startDragging(e) {
        this.isDragging = true;
        this.activeSlider = e.currentTarget;
        
        // Record the starting position
        if (e.type === 'mousedown') {
          this.startX = e.clientX;
        } else if (e.type === 'touchstart') {
          this.startX = e.touches[0].clientX;
        }
        
        // Prevent default behavior only for mouse events
        if (e.type === 'mousedown') {
          e.preventDefault();
        }
      }

      drag(e) {
        if (!this.isDragging || !this.activeSlider) return;
        
        // Prevent default to stop scrolling on touch devices
        if (e.type === 'touchmove') {
          e.preventDefault();
        }
        
        let clientX;
        if (e.type === 'mousemove') {
          clientX = e.clientX;
        } else if (e.type === 'touchmove') {
          clientX = e.touches[0].clientX;
        }
        
        const rect = this.activeSlider.getBoundingClientRect();
        const position = ((clientX - rect.left) / rect.width) * 100;
        
        this.setSliderPosition(this.activeSlider, position);
      }

      stopDragging() {
        this.isDragging = false;
        this.activeSlider = null;
      }

      handleClick(e) {
        const rect = e.currentTarget.getBoundingClientRect();
        const position = ((e.clientX - rect.left) / rect.width) * 100;
        
        this.setSliderPosition(e.currentTarget, position);
      }

      setSliderPosition(slider, position) {
        // Clamp position between 0 and 100
        const clampedPosition = Math.max(0, Math.min(100, position));
        
        const afterWrapper = slider.querySelector('.ai-after-image-wrapper-{{ ai_gen_id }}');
        const handle = slider.querySelector('.ai-slider-handle-{{ ai_gen_id }}');
        
        afterWrapper.style.width = `${clampedPosition}%`;
        handle.style.left = `${clampedPosition}%`;
      }
    }

    customElements.define('before-after-slider-{{ ai_gen_id }}', BeforeAfterSlider);
    
    // Initialize all sliders
    const sliderGallery = document.querySelector('.ai-before-after-gallery-{{ ai_gen_id }}');
    new BeforeAfterSlider().connectedCallback();
  })();
</script>

{% schema %}
{
  "name": "Before & After Gallery",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Gallery Settings"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Dental Transformation Gallery"
    },
    {
      "type": "textarea",
      "id": "subheading",
      "label": "Subheading",
      "default": "See the amazing results of our dental procedures. Drag the slider to compare before and after."
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider color",
      "default": "#4A90E2"
    },
    {
      "type": "color",
      "id": "label_bg_color",
      "label": "Label background color",
      "default": "rgba(0, 0, 0, 0.5)"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 32
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Before & After 1"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title",
      "default": "Teeth Whitening"
    },
    {
      "type": "textarea",
      "id": "description_1",
      "label": "Description",
      "default": "Professional teeth whitening treatment that removed years of stains and discoloration."
    },
    {
      "type": "text",
      "id": "before_label_1",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label_1",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Before & After 2"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title",
      "default": "Dental Veneers"
    },
    {
      "type": "textarea",
      "id": "description_2",
      "label": "Description",
      "default": "Porcelain veneers that transformed chipped and misaligned teeth into a perfect smile."
    },
    {
      "type": "text",
      "id": "before_label_2",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label_2",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Before & After 3"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title",
      "default": "Invisalign Treatment"
    },
    {
      "type": "textarea",
      "id": "description_3",
      "label": "Description",
      "default": "Clear aligners that gradually straightened teeth without traditional braces."
    },
    {
      "type": "text",
      "id": "before_label_3",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label_3",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Before & After 4"
    },
    {
      "type": "text",
      "id": "title_4",
      "label": "Title",
      "default": "Dental Implants"
    },
    {
      "type": "textarea",
      "id": "description_4",
      "label": "Description",
      "default": "Permanent replacement for missing teeth that look and function like natural teeth."
    },
    {
      "type": "text",
      "id": "before_label_4",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label_4",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "image_picker",
      "id": "before_image_4",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_4",
      "label": "After image"
    }
  ],
  "presets": [
    {
      "name": "Before & After Gallery"
    }
  ]
}
{% endschema %}