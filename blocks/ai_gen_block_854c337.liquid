{% doc %}
  @prompt
    Create a universal tour itinerary section with customizable number of days. Add a setting to choose number of days (1-20) and dynamically show that many day tabs (Day 1, Day 2, etc.). Each day should have a text field for description and an image field. Make it look exactly like a clean, responsive travel website with smooth tab transitions. The merchant should be able to set the number of days in the block settings, and only that many tabs should appear.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
/* Общие стили */
.ai-tour-itinerary__day-{{ ai_gen_id }} {
  display: none;
}
.ai-tour-itinerary__day-{{ ai_gen_id }}.active {
  display: block;
}

  .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
  max-width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  border-radius: 8px;
}

  .ai-tour-itinerary__content-{{ ai_gen_id }},
.ai-tour-itinerary__day-content-{{ ai_gen_id }} {
  max-width: 1200px;
  margin: 0 auto;
}

  @media (min-width: 768px) {
  .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
    max-width: 800px;
    width: 100%;
  }
}

  .ai-tour-itinerary__header-{{ ai_gen_id }} {
  text-align: center;
  padding-left: 15px;
  padding-right: 15px;
  max-width: 1200px;
  margin: 0 auto 20px auto;
}

  .ai-tour-itinerary__day-title-{{ ai_gen_id }} {
  text-align: center;
}

  .ai-tour-itinerary__tabs-{{ ai_gen_id }} {
  display: flex;
  justify-content: center;
  flex-wrap: wrap; /* Чтобы кнопки не ломались на маленьких экранах */
  gap: 10px; /* Немного расстояния между кнопками */
  margin-bottom: 20px;
  padding-left: 15px;
  padding-right: 15px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

  .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
  display: flex;
  justify-content: center; /* центрируем по горизонтали */
  align-items: flex-start; /* выровнять по верхнему краю */
  gap: 30px; /* расстояние между изображением и текстом */
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 15px;
  flex-wrap: wrap; /* чтобы на маленьких экранах переносилось */
}

.ai-tour-itinerary__image-section-{{ ai_gen_id }},
.ai-tour-itinerary__text-section-{{ ai_gen_id }} {
  flex: 1 1 400px; /* обе части занимают минимум 400px, но могут сжиматься */
  max-width: 600px;
}

@media (max-width: 768px) {
  .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
    flex-direction: column;
    align-items: center;
  }
  .ai-tour-itinerary__image-section-{{ ai_gen_id }},
  .ai-tour-itinerary__text-section-{{ ai_gen_id }} {
    max-width: 100%;
  }
  .ai-tour-itinerary__image-section-{{ ai_gen_id }} {
    margin-bottom: 8px;
  }
}

  @media (max-width: 768px) {
  .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
    flex-direction: column;
    align-items: center;
  }

  .ai-tour-itinerary__image-section-{{ ai_gen_id }} {
    order: 1;  /* сначала изображение */
    max-width: 100%;
    margin-bottom: 8px;
  }

  .ai-tour-itinerary__text-section-{{ ai_gen_id }} {
    order: 2;  /* потом описание */
    max-width: 100%;
  }
}


/* Стили для адаптивности */
@media screen and (max-width: 768px) {
  .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .ai-tour-itinerary__image-section-{{ ai_gen_id }},
  .ai-tour-itinerary__text-section-{{ ai_gen_id }} {
    width: 100% !important;
    max-width: 100%;
  }

  .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
    width: 100%;
    height: auto;
    object-fit: cover;
  }

  .ai-tour-itinerary__day-description-{{ ai_gen_id }},
  .ai-tour-itinerary__day-title-{{ ai_gen_id }} {
    text-align: left;
  }

  .ai-tour-itinerary__tabs-{{ ai_gen_id }} {
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }} {
    flex: 1 1 auto;
  }
}
{% endstyle %}



<tour-itinerary-{{ ai_gen_id }} class="ai-tour-itinerary-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-tour-itinerary__header-{{ ai_gen_id }}">
    {% if block.settings.title != blank %}
      <h2 class="ai-tour-itinerary__title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
    {% endif %}
    {% if block.settings.subtitle != blank %}
      <p class="ai-tour-itinerary__subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</p>
    {% endif %}
  </div>

  <div class="ai-tour-itinerary__tabs-{{ ai_gen_id }}" role="tablist">
    {% for i in (1..block.settings.number_of_days) %}
      <button
        class="ai-tour-itinerary__tab-{{ ai_gen_id }}{% if forloop.first %} active{% endif %}"
        role="tab"
        aria-selected="{% if forloop.first %}true{% else %}false{% endif %}"
        aria-controls="day-{{ i }}-{{ ai_gen_id }}"
        data-day="{{ i }}"
      >
        Day {{ i }}
      </button>
    {% endfor %}
  </div>

  <div class="ai-tour-itinerary__content-{{ ai_gen_id }}">
    {% for i in (1..block.settings.number_of_days) %}
      {% liquid
        assign day_title_key = 'day_' | append: i | append: '_title'
        assign day_description_key = 'day_' | append: i | append: '_description'
        assign day_image_key = 'day_' | append: i | append: '_image'
        
        assign day_title = block.settings[day_title_key]
        assign day_description = block.settings[day_description_key]
        assign day_image = block.settings[day_image_key]
      %}
      
      <div
        class="ai-tour-itinerary__day-{{ ai_gen_id }}{% if forloop.first %} active{% endif %}"
        id="day-{{ i }}-{{ ai_gen_id }}"
        role="tabpanel"
        aria-labelledby="tab-{{ i }}-{{ ai_gen_id }}"
      >
        <div class="ai-tour-itinerary__day-content-{{ ai_gen_id }} text-{{ block.settings.text_position }}">
          <div class="ai-tour-itinerary__text-section-{{ ai_gen_id }}">
            <h3 class="ai-tour-itinerary__day-title-{{ ai_gen_id }}">
              {% if day_title != blank %}
                {{ day_title }}
              {% else %}
                Day {{ i }}
              {% endif %}
            </h3>
            <div class="ai-tour-itinerary__day-description-{{ ai_gen_id }}">
              {% if day_description != blank %}
                {{ day_description }}
              {% else %}
                <p>Add your day {{ i }} description in the block settings.</p>
              {% endif %}
            </div>
          </div>
          <div class="ai-tour-itinerary__image-section-{{ ai_gen_id }}">
            {% if day_image %}
              <img
                src="{{ day_image | image_url: width: 800 }}"
                alt="{{ day_image.alt | default: day_title | escape }}"
                class="ai-tour-itinerary__day-image-{{ ai_gen_id }}"
                loading="lazy"
                width="{{ day_image.width }}"
                height="{{ day_image.height }}"
              >
            {% else %}
              <div class="ai-tour-itinerary__image-placeholder-{{ ai_gen_id }}">
                {{ 'image' | placeholder_svg_tag }}
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    {% endfor %}
  </div>
</tour-itinerary-{{ ai_gen_id }}>

<script>
  (function() {
    class TourItinerary{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.tabs = this.querySelectorAll('.ai-tour-itinerary__tab-{{ ai_gen_id }}');
        this.days = this.querySelectorAll('.ai-tour-itinerary__day-{{ ai_gen_id }}');
        
        this.setupEventListeners();
      }

      setupEventListeners() {
        this.tabs.forEach((tab, index) => {
          tab.addEventListener('click', () => {
            this.switchToDay(index + 1);
          });

          tab.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              this.switchToDay(index + 1);
            }
          });
        });
      }

      switchToDay(dayNumber) {
        this.tabs.forEach((tab, index) => {
          const isActive = index + 1 === dayNumber;
          tab.classList.toggle('active', isActive);
          tab.setAttribute('aria-selected', isActive);
        });

        this.days.forEach((day, index) => {
          day.classList.toggle('active', index + 1 === dayNumber);
        });
      }
    }

    customElements.define('tour-itinerary-{{ ai_gen_id }}', TourItinerary{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Tour Itinerary",
  "settings": [
    {
      "type": "header",
      "content": "General Settings"
    },
    {
      "type": "range",
      "id": "number_of_days",
      "min": 1,
      "max": 20,
      "step": 1,
      "label": "Number of days",
      "default": 5
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section title",
      "default": "Tour Itinerary"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Section subtitle",
      "default": "Discover your perfect journey day by day"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle color",
      "default": "#7f8c8d"
    },
    {
      "type": "color",
      "id": "tab_text_color",
      "label": "Tab text color",
      "default": "#7f8c8d"
    },
    {
      "type": "color",
      "id": "tab_hover_color",
      "label": "Tab hover background",
      "default": "#ecf0f1"
    },
    {
      "type": "color",
      "id": "tab_hover_text_color",
      "label": "Tab hover text color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "tab_active_color",
      "label": "Active tab color",
      "default": "#3498db"
    },
    {
      "type": "color",
      "id": "tab_active_text_color",
      "label": "Active tab text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "content_bg_color",
      "label": "Content background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#ecf0f1"
    },
    {
      "type": "color",
      "id": "day_title_color",
      "label": "Day title color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#34495e"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 36
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 14,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 18
    },
    {
      "type": "range",
      "id": "day_title_size",
      "min": 18,
      "max": 36,
      "step": 2,
      "unit": "px",
      "label": "Day title size",
      "default": 28
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 14,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "content_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Content border radius",
      "default": 12
    },
    {
      "type": "header",
      "content": "Day 1"
    },
    {
      "type": "text",
      "id": "day_1_title",
      "label": "Day 1 title",
      "default": "Arrival & City Exploration"
    },
    {
      "type": "richtext",
      "id": "day_1_description",
      "label": "Day 1 description",
      "default": "<p>Welcome to your adventure! Start with a guided city tour and visit the main attractions. Enjoy a welcome dinner at a local restaurant.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_1_image",
      "label": "Day 1 image"
    },
    {
      "type": "header",
      "content": "Day 2"
    },
    {
      "type": "text",
      "id": "day_2_title",
      "label": "Day 2 title",
      "default": "Cultural Immersion"
    },
    {
      "type": "richtext",
      "id": "day_2_description",
      "label": "Day 2 description",
      "default": "<p>Dive deep into local culture with museum visits, traditional workshops, and authentic cuisine experiences.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_2_image",
      "label": "Day 2 image"
    },
    {
      "type": "header",
      "content": "Day 3"
    },
    {
      "type": "text",
      "id": "day_3_title",
      "label": "Day 3 title",
      "default": "Nature & Adventure"
    },
    {
      "type": "richtext",
      "id": "day_3_description",
      "label": "Day 3 description",
      "default": "<p>Explore the natural beauty with hiking trails, scenic viewpoints, and outdoor activities.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_3_image",
      "label": "Day 3 image"
    },
    {
      "type": "header",
      "content": "Day 4"
    },
    {
      "type": "text",
      "id": "day_4_title",
      "label": "Day 4 title",
      "default": "Local Markets & Shopping"
    },
    {
      "type": "richtext",
      "id": "day_4_description",
      "label": "Day 4 description",
      "default": "<p>Browse local markets, shop for souvenirs, and experience the vibrant street life.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_4_image",
      "label": "Day 4 image"
    },
    {
      "type": "header",
      "content": "Day 5"
    },
    {
      "type": "text",
      "id": "day_5_title",
      "label": "Day 5 title",
      "default": "Departure"
    },
    {
      "type": "richtext",
      "id": "day_5_description",
      "label": "Day 5 description",
      "default": "<p>Final morning at leisure, check-out, and departure transfer to the airport.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_5_image",
      "label": "Day 5 image"
    },
    {
      "type": "header",
      "content": "Day 6"
    },
    {
      "type": "text",
      "id": "day_6_title",
      "label": "Day 6 title"
    },
    {
      "type": "richtext",
      "id": "day_6_description",
      "label": "Day 6 description"
    },
    {
      "type": "image_picker",
      "id": "day_6_image",
      "label": "Day 6 image"
    },
    {
      "type": "header",
      "content": "Day 7"
    },
    {
      "type": "text",
      "id": "day_7_title",
      "label": "Day 7 title"
    },
    {
      "type": "richtext",
      "id": "day_7_description",
      "label": "Day 7 description"
    },
    {
      "type": "image_picker",
      "id": "day_7_image",
      "label": "Day 7 image"
    },
    {
      "type": "header",
      "content": "Day 8"
    },
    {
      "type": "text",
      "id": "day_8_title",
      "label": "Day 8 title"
    },
    {
      "type": "richtext",
      "id": "day_8_description",
      "label": "Day 8 description"
    },
    {
      "type": "image_picker",
      "id": "day_8_image",
      "label": "Day 8 image"
    },
    {
      "type": "header",
      "content": "Day 9"
    },
    {
      "type": "text",
      "id": "day_9_title",
      "label": "Day 9 title"
    },
    {
      "type": "richtext",
      "id": "day_9_description",
      "label": "Day 9 description"
    },
    {
      "type": "image_picker",
      "id": "day_9_image",
      "label": "Day 9 image"
    },
    {
      "type": "header",
      "content": "Day 10"
    },
    {
      "type": "text",
      "id": "day_10_title",
      "label": "Day 10 title"
    },
    {
      "type": "richtext",
      "id": "day_10_description",
      "label": "Day 10 description"
    },
    {
      "type": "image_picker",
      "id": "day_10_image",
      "label": "Day 10 image"
    },
    {
      "type": "header",
      "content": "Day 11"
    },
    {
      "type": "text",
      "id": "day_11_title",
      "label": "Day 11 title"
    },
    {
      "type": "richtext",
      "id": "day_11_description",
      "label": "Day 11 description"
    },
    {
      "type": "image_picker",
      "id": "day_11_image",
      "label": "Day 11 image"
    },
    {
      "type": "header",
      "content": "Day 12"
    },
    {
      "type": "text",
      "id": "day_12_title",
      "label": "Day 12 title"
    },
    {
      "type": "richtext",
      "id": "day_12_description",
      "label": "Day 12 description"
    },
    {
      "type": "image_picker",
      "id": "day_12_image",
      "label": "Day 12 image"
    },
    {
      "type": "header",
      "content": "Day 13"
    },
    {
      "type": "text",
      "id": "day_13_title",
      "label": "Day 13 title"
    },
    {
      "type": "richtext",
      "id": "day_13_description",
      "label": "Day 13 description"
    },
    {
      "type": "image_picker",
      "id": "day_13_image",
      "label": "Day 13 image"
    },
    {
      "type": "header",
      "content": "Day 14"
    },
    {
      "type": "text",
      "id": "day_14_title",
      "label": "Day 14 title"
    },
    {
      "type": "richtext",
      "id": "day_14_description",
      "label": "Day 14 description"
    },
    {
      "type": "image_picker",
      "id": "day_14_image",
      "label": "Day 14 image"
    },
    {
      "type": "header",
      "content": "Day 15"
    },
    {
      "type": "text",
      "id": "day_15_title",
      "label": "Day 15 title"
    },
    {
      "type": "richtext",
      "id": "day_15_description",
      "label": "Day 15 description"
    },
    {
      "type": "image_picker",
      "id": "day_15_image",
      "label": "Day 15 image"
    },
    {
      "type": "header",
      "content": "Day 16"
    },
    {
      "type": "text",
      "id": "day_16_title",
      "label": "Day 16 title"
    },
    {
      "type": "richtext",
      "id": "day_16_description",
      "label": "Day 16 description"
    },
    {
      "type": "image_picker",
      "id": "day_16_image",
      "label": "Day 16 image"
    },
    {
      "type": "header",
      "content": "Day 17"
    },
    {
      "type": "text",
      "id": "day_17_title",
      "label": "Day 17 title"
    },
    {
      "type": "richtext",
      "id": "day_17_description",
      "label": "Day 17 description"
    },
    {
      "type": "image_picker",
      "id": "day_17_image",
      "label": "Day 17 image"
    },
    {
      "type": "header",
      "content": "Day 18"
    },
    {
      "type": "text",
      "id": "day_18_title",
      "label": "Day 18 title"
    },
    {
      "type": "richtext",
      "id": "day_18_description",
      "label": "Day 18 description"
    },
    {
      "type": "image_picker",
      "id": "day_18_image",
      "label": "Day 18 image"
    },
    {
      "type": "header",
      "content": "Day 19"
    },
    {
      "type": "text",
      "id": "day_19_title",
      "label": "Day 19 title"
    },
    {
      "type": "richtext",
      "id": "day_19_description",
      "label": "Day 19 description"
    },
    {
      "type": "image_picker",
      "id": "day_19_image",
      "label": "Day 19 image"
    },
    {
      "type": "header",
      "content": "Day 20"
    },
    {
      "type": "text",
      "id": "day_20_title",
      "label": "Day 20 title"
    },
    {
      "type": "richtext",
      "id": "day_20_description",
      "label": "Day 20 description"
    },
    {
      "type": "image_picker",
      "id": "day_20_image",
      "label": "Day 20 image"
    }
  ],
  "presets": [
    {
      "name": "Tour Itinerary"
    }
  ],
  "tag": null
}
{% endschema %}