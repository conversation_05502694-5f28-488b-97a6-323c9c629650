{% doc %}
  @prompt
    Create a before/after comparison section with 3 horizontal cards side by side. Each card should have a before/after image slider that allows users to drag to reveal the transformation. The layout should be compact and beautiful, with proper image aspect ratio preservation to prevent distortion or stretching. Include customizable settings for uploading before and after images for each of the 3 cards, with smooth slider functionality and responsive design.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-before-after-{{ ai_gen_id }} {
    margin: 0 auto;
    padding: {{ block.settings.section_padding }}px 0;
  }

  .ai-before-after__heading-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: {{ block.settings.heading_spacing }}px;
    font-size: {{ block.settings.heading_size }}px;
    color: {{ block.settings.heading_color }};
  }

  .ai-before-after__description-{{ ai_gen_id }} {
    text-align: center;
    margin: 0 auto {{ block.settings.description_spacing }}px;
    max-width: 800px;
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.description_color }};
  }

  .ai-before-after__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: {{ block.settings.card_spacing }}px;
  }

  .ai-before-after__card-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    background-color: {{ block.settings.card_background }};
    border-radius: {{ block.settings.card_radius }}px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .ai-before-after__slider-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    padding-bottom: {{ block.settings.image_aspect_ratio }}%;
    overflow: hidden;
  }

  .ai-before-after__image-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .ai-before-after__before-{{ ai_gen_id }},
  .ai-before-after__after-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-before-after__before-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
  }

  .ai-before-after__slider-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background-color: {{ block.settings.slider_color }};
    transform: translateX(-50%);
    cursor: ew-resize;
    z-index: 10;
  }

  .ai-before-after__slider-button-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: {{ block.settings.slider_color }};
    transform: translate(-50%, -50%);
    cursor: ew-resize;
    z-index: 11;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-before-after__slider-button-{{ ai_gen_id }}::before,
  .ai-before-after__slider-button-{{ ai_gen_id }}::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 2px;
    background-color: white;
  }

  .ai-before-after__slider-button-{{ ai_gen_id }}::before {
    transform: translateX(-5px) rotate(-45deg);
  }

  .ai-before-after__slider-button-{{ ai_gen_id }}::after {
    transform: translateX(5px) rotate(45deg);
  }

  .ai-before-after__card-content-{{ ai_gen_id }} {
    padding: 20px;
  }

  .ai-before-after__card-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.card_title_size }}px;
    font-weight: 600;
    margin-bottom: 8px;
    color: {{ block.settings.card_title_color }};
  }

  .ai-before-after__card-text-{{ ai_gen_id }} {
    font-size: {{ block.settings.card_text_size }}px;
    color: {{ block.settings.card_text_color }};
  }

  .ai-before-after__labels-{{ ai_gen_id }} {
    position: absolute;
    bottom: 10px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    z-index: 5;
    pointer-events: none;
  }

  .ai-before-after__label-{{ ai_gen_id }} {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .ai-before-after__image-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .ai-before-after__image-placeholder-{{ ai_gen_id }} svg {
    width: 50%;
    height: 50%;
    opacity: 0.5;
  }

  @media screen and (max-width: 990px) {
    .ai-before-after__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media screen and (max-width: 749px) {
    .ai-before-after__grid-{{ ai_gen_id }} {
      grid-template-columns: 1fr;
    }
    
    .ai-before-after__heading-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.heading_size }}px * 0.8);
    }
    
    .ai-before-after__description-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.description_size }}px * 0.9);
    }
  }
{% endstyle %}

<before-after-comparison-{{ ai_gen_id }} class="ai-before-after-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.heading != blank %}
    <h2 class="ai-before-after__heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
  {% endif %}
  
  {% if block.settings.description != blank %}
    <div class="ai-before-after__description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
  {% endif %}
  
  <div class="ai-before-after__grid-{{ ai_gen_id }}">
    {% for i in (1..3) %}
      {% liquid
        assign before_image_key = 'before_image_' | append: i
        assign after_image_key = 'after_image_' | append: i
        assign title_key = 'card_title_' | append: i
        assign text_key = 'card_text_' | append: i
        
        assign before_image = block.settings[before_image_key]
        assign after_image = block.settings[after_image_key]
        assign title = block.settings[title_key]
        assign text = block.settings[text_key]
      %}
      
      <div class="ai-before-after__card-{{ ai_gen_id }}">
        <div class="ai-before-after__slider-container-{{ ai_gen_id }}" data-slider-id="{{ i }}">
          <div class="ai-before-after__image-container-{{ ai_gen_id }}">
            {% if after_image %}
              <img 
                src="{{ after_image | image_url: width: 1000 }}" 
                alt="{{ after_image.alt | escape }}" 
                loading="lazy"
                class="ai-before-after__after-{{ ai_gen_id }}"
              >
            {% else %}
              <div class="ai-before-after__image-placeholder-{{ ai_gen_id }}">
                {{ 'image' | placeholder_svg_tag }}
              </div>
            {% endif %}
            
            <div class="ai-before-after__before-container-{{ ai_gen_id }}">
              {% if before_image %}
                <img 
                  src="{{ before_image | image_url: width: 1000 }}" 
                  alt="{{ before_image.alt | escape }}" 
                  loading="lazy"
                  class="ai-before-after__before-{{ ai_gen_id }}"
                >
              {% else %}
                <div class="ai-before-after__image-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>
            
            <div class="ai-before-after__slider-handle-{{ ai_gen_id }}">
              <div class="ai-before-after__slider-button-{{ ai_gen_id }}"></div>
            </div>
            
            {% if block.settings.show_labels %}
              <div class="ai-before-after__labels-{{ ai_gen_id }}">
                <span class="ai-before-after__label-{{ ai_gen_id }}">{{ block.settings.before_label }}</span>
                <span class="ai-before-after__label-{{ ai_gen_id }}">{{ block.settings.after_label }}</span>
              </div>
            {% endif %}
          </div>
        </div>
        
        <div class="ai-before-after__card-content-{{ ai_gen_id }}">
          {% if title != blank %}
            <h3 class="ai-before-after__card-title-{{ ai_gen_id }}">{{ title }}</h3>
          {% endif %}
          
          {% if text != blank %}
            <div class="ai-before-after__card-text-{{ ai_gen_id }}">{{ text }}</div>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  </div>
</before-after-comparison-{{ ai_gen_id }}>

<script>
  (function() {
    class BeforeAfterComparison{{ai_gen_id}} extends HTMLElement {
      constructor() {
        super();
        this.sliders = this.querySelectorAll('.ai-before-after__slider-container-{{ ai_gen_id }}');
        this.activeSlider = null;
        this.isDragging = false;
        
        // Bind event handlers
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleTouchStart = this.handleTouchStart.bind(this);
        this.handleTouchMove = this.handleTouchMove.bind(this);
        this.handleTouchEnd = this.handleTouchEnd.bind(this);
      }
      
      connectedCallback() {
        this.setupEventListeners();
      }
      
      disconnectedCallback() {
        this.removeEventListeners();
      }
      
      setupEventListeners() {
        this.sliders.forEach(slider => {
          const handle = slider.querySelector('.ai-before-after__slider-handle-{{ ai_gen_id }}');
          const beforeContainer = slider.querySelector('.ai-before-after__before-container-{{ ai_gen_id }}');
          
          // Mouse events
          handle.addEventListener('mousedown', this.handleMouseDown);
          
          // Touch events
          handle.addEventListener('touchstart', this.handleTouchStart, { passive: false });
          
          // Initial position
          this.setSliderPosition(slider, 50);
        });
        
        // Global events
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mouseup', this.handleMouseUp);
        document.addEventListener('touchmove', this.handleTouchMove, { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd);
      }
      
      removeEventListeners() {
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mouseup', this.handleMouseUp);
        document.removeEventListener('touchmove', this.handleTouchMove);
        document.removeEventListener('touchend', this.handleTouchEnd);
      }
      
      handleMouseDown(event) {
        event.preventDefault();
        this.isDragging = true;
        this.activeSlider = event.target.closest('.ai-before-after__slider-container-{{ ai_gen_id }}');
      }
      
      handleMouseMove(event) {
        if (!this.isDragging || !this.activeSlider) return;
        
        const rect = this.activeSlider.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const percentage = Math.min(Math.max(0, x / rect.width * 100), 100);
        
        this.setSliderPosition(this.activeSlider, percentage);
      }
      
      handleMouseUp() {
        this.isDragging = false;
        this.activeSlider = null;
      }
      
      handleTouchStart(event) {
        event.preventDefault();
        this.isDragging = true;
        this.activeSlider = event.target.closest('.ai-before-after__slider-container-{{ ai_gen_id }}');
      }
      
      handleTouchMove(event) {
        if (!this.isDragging || !this.activeSlider) return;
        event.preventDefault();
        
        const touch = event.touches[0];
        const rect = this.activeSlider.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const percentage = Math.min(Math.max(0, x / rect.width * 100), 100);
        
        this.setSliderPosition(this.activeSlider, percentage);
      }
      
      handleTouchEnd() {
        this.isDragging = false;
        this.activeSlider = null;
      }
      
      setSliderPosition(slider, percentage) {
        const handle = slider.querySelector('.ai-before-after__slider-handle-{{ ai_gen_id }}');
        const beforeContainer = slider.querySelector('.ai-before-after__before-container-{{ ai_gen_id }}');
        
        handle.style.left = `${percentage}%`;
        beforeContainer.style.width = `${percentage}%`;
      }
    }
    
    customElements.define('before-after-comparison-{{ ai_gen_id }}', BeforeAfterComparison{{ai_gen_id}});
  })();
</script>

{% schema %}
{
  "name": "Before & After Comparison",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Section Content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "See the Transformation"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "Drag the slider to see the before and after results of our products."
    },
    {
      "type": "header",
      "content": "Card 1"
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "card_title_1",
      "label": "Title",
      "default": "Amazing Results"
    },
    {
      "type": "textarea",
      "id": "card_text_1",
      "label": "Description",
      "default": "See the incredible transformation with our product."
    },
    {
      "type": "header",
      "content": "Card 2"
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "card_title_2",
      "label": "Title",
      "default": "Stunning Difference"
    },
    {
      "type": "textarea",
      "id": "card_text_2",
      "label": "Description",
      "default": "Our customers love the visible improvement."
    },
    {
      "type": "header",
      "content": "Card 3"
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "card_title_3",
      "label": "Title",
      "default": "Real Results"
    },
    {
      "type": "textarea",
      "id": "card_text_3",
      "label": "Description",
      "default": "See the proof of our product's effectiveness."
    },
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show before/after labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label text",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label text",
      "default": "After"
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider color",
      "default": "#4A90E2"
    },
    {
      "type": "range",
      "id": "image_aspect_ratio",
      "min": 50,
      "max": 150,
      "step": 5,
      "unit": "%",
      "label": "Image aspect ratio",
      "default": 75,
      "info": "Percentage determines height relative to width (75% = 4:3 ratio)"
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Section padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "card_spacing",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Space between cards",
      "default": 20
    },
    {
      "type": "range",
      "id": "card_radius",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Card corner radius",
      "default": 8
    },
    {
      "type": "color",
      "id": "card_background",
      "label": "Card background color",
      "default": "#FFFFFF"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 32
    },
    {
      "type": "range",
      "id": "heading_spacing",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Space below heading",
      "default": 20
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "range",
      "id": "description_spacing",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Space below description",
      "default": 30
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#555555"
    },
    {
      "type": "range",
      "id": "card_title_size",
      "min": 14,
      "max": 28,
      "step": 1,
      "unit": "px",
      "label": "Card title size",
      "default": 18
    },
    {
      "type": "color",
      "id": "card_title_color",
      "label": "Card title color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "card_text_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Card text size",
      "default": 14
    },
    {
      "type": "color",
      "id": "card_text_color",
      "label": "Card text color",
      "default": "#555555"
    }
  ],
  "presets": [
    {
      "name": "Before & After Comparison"
    }
  ]
}
{% endschema %}