{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    padding: {{ block.settings.padding_top }}px {{ block.settings.padding_horizontal }}px {{ block.settings.padding_bottom }}px;
    font-size: {{ block.settings.font_size }}px;
    line-height: 1.4;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-sitemap-footer__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.column_gap }}px {{ block.settings.row_gap }}px;margin-bottom: {{ block.settings.copyright_spacing }}px;
  }

  .ai-sitemap-footer__column-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: {{ block.settings.link_spacing }}px;
  }

  .ai-sitemap-footer__column-title-{{ ai_gen_id }} {
    font-weight: 600;
    font-size: {{ block.settings.font_size | plus: 2 }}px;
    margin-bottom: {{ block.settings.title_spacing }}px;
    color: {{ block.settings.title_color }};
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    text-align: {{ block.settings.copyright_alignment }};
    padding-top: {{ block.settings.copyright_spacing }}px;
    border-top: 1px solid {{ block.settings.border_color }};
    color: {{ block.settings.copyright_color }};
    font-size: {{ block.settings.font_size | minus: 2 }}px;
    opacity: 0.8;
  }

  @media screen and (max-width: 768px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
      gap: {{ block.settings.column_gap | divided_by: 2 }}px {{ block.settings.row_gap | divided_by: 2 }}px;
    }

    .ai-sitemap-footer-{{ ai_gen_id }} {
      padding: {{ block.settings.padding_top | times: 0.7 }}px {{ block.settings.padding_horizontal | times: 0.7 }}px {{ block.settings.padding_bottom | times: 0.7 }}px;font-size: {{ block.settings.font_size | minus: 1 }}px;
    }

    .ai-sitemap-footer__column-title-{{ ai_gen_id }} {
      font-size: {{ block.settings.font_size | plus: 1 }}px;
    }

    .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
      font-size: {{ block.settings.font_size | minus: 3 }}px;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: 1fr;
    }
  }
{% endstyle %}

<footer class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__grid-{{ ai_gen_id }}">
      
      {% if block.settings.show_main_menu and linklists[block.settings.main_menu].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <div class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.main_menu_title }}</div>
          {% for link in linklists[block.settings.main_menu].links limit: block.settings.links_limit %}
            <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_footer_menu and linklists[block.settings.footer_menu].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <div class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.footer_menu_title }}</div>
          {% for link in linklists[block.settings.footer_menu].links limit: block.settings.links_limit %}
            <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_collections %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <div class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.collections_title }}</div>
          {% for collection in collections limit: block.settings.collections_limit %}
            {% unless collection.handle == 'frontpage' %}
              <a href="{{ collection.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ collection.title }}</a>
            {% endunless %}
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_pages %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <div class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.pages_title }}</div>
          {% for page in pages limit: block.settings.pages_limit %}
            <a href="{{ page.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ page.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_blogs %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <div class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.blogs_title }}</div>
          {% for blog in blogs limit: block.settings.blogs_limit %}
            <a href="{{ blog.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ blog.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_policies %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <div class="ai-sitemap-footer__column-title-{{ ai_gen_id }}">{{ block.settings.policies_title }}</div>
          {% if shop.privacy_policy %}
            <a href="{{ shop.privacy_policy.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ shop.privacy_policy.title }}</a>
          {% endif %}
          {% if shop.terms_of_service %}
            <a href="{{ shop.terms_of_service.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ shop.terms_of_service.title }}</a>
          {% endif %}
          {% if shop.refund_policy %}
            <a href="{{ shop.refund_policy.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ shop.refund_policy.title }}</a>
          {% endif %}
          {% if shop.shipping_policy %}
            <a href="{{ shop.shipping_policy.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ shop.shipping_policy.title }}</a>
          {% endif %}
        </div>
      {% endif %}

    </div>

    {% if block.settings.copyright_text != blank %}
      <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
        {{ block.settings.copyright_text }}
      </div>
    {% endif %}
  </div>
</footer>

{% schema %}
{
  "name": "Compact Sitemap Footer",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "options": [
        {"value": "2", "label": "2"},
        {"value": "3", "label": "3"},
        {"value": "4", "label": "4"},
        {"value": "5", "label": "5"},
        {"value": "6", "label": "6"}
      ],
      "default": "4"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {"value": "1", "label": "1"},
        {"value": "2", "label": "2"}
      ],
      "default": "2"
    },
    {
      "type": "range",
      "id": "column_gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Column gap",
      "default": 30
    },
    {
      "type": "range",
      "id": "row_gap",
      "min": 5,
      "max": 30,
      "step": 5,
      "unit": "px",
      "label": "Row gap",
      "default": 15
    },
    {
      "type": "header",
      "content": "Main Menu"
    },
    {
      "type": "checkbox",
      "id": "show_main_menu",
      "label": "Show main menu",
      "default": true
    },
    {
      "type": "link_list",
      "id": "main_menu",
      "label": "Main menu"
    },
    {
      "type": "text",
      "id": "main_menu_title",
      "label": "Main menu title",
      "default": "Shop"
    },
    {
      "type": "header",
      "content": "Footer Menu"
    },
    {
      "type": "checkbox",
      "id": "show_footer_menu",
      "label": "Show footer menu",
      "default": true
    },
    {
      "type": "link_list",
      "id": "footer_menu",
      "label": "Footer menu"
    },
    {
      "type": "text",
      "id": "footer_menu_title",
      "label": "Footer menu title",
      "default": "Quick Links"
    },
    {
      "type": "range",
      "id": "links_limit",
      "min": 3,
      "max": 15,
      "step": 1,
      "label": "Maximum menu links",
      "default": 8
    },
    {
      "type": "header",
      "content": "Collections"
    },
    {
      "type": "checkbox",
      "id": "show_collections",
      "label": "Show collections",
      "default": true
    },
    {
      "type": "text",
      "id": "collections_title",
      "label": "Collections title",
      "default": "Collections"
    },
    {
      "type": "range",
      "id": "collections_limit",
      "min": 3,
      "max": 15,
      "step": 1,
      "label": "Maximum collections",
      "default": 6
    },
    {
      "type": "header",
      "content": "Pages"
    },
    {
      "type": "checkbox",
      "id": "show_pages",
      "label": "Show pages",
      "default": true
    },
    {
      "type": "text",
      "id": "pages_title",
      "label": "Pages title",
      "default": "Pages"
    },
    {
      "type": "range",
      "id": "pages_limit",
      "min": 3,
      "max": 15,
      "step": 1,
      "label": "Maximum pages",
      "default": 6
    },
    {
      "type": "header",
      "content": "Blogs"
    },
    {
      "type": "checkbox",
      "id": "show_blogs",
      "label": "Show blogs",
      "default": false
    },
    {
      "type": "text",
      "id": "blogs_title",
      "label": "Blogs title",
      "default": "Blogs"
    },
    {
      "type": "range",
      "id": "blogs_limit",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "Maximum blogs",
      "default": 3
    },
    {
      "type": "header",
      "content": "Policies"
    },
    {
      "type": "checkbox",
      "id": "show_policies",
      "label": "Show policies",
      "default": true
    },
    {
      "type": "text",
      "id": "policies_title",
      "label": "Policies title",
      "default": "Policies"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "<p>&copy; 2024 Your Store Name. All rights reserved.</p>"
    },
    {
      "type": "select",
      "id": "copyright_alignment",
      "label": "Copyright alignment",
      "options": [
        {"value": "left", "label": "Left"},
        {"value": "center", "label": "Center"},
        {"value": "right", "label": "Right"}
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "copyright_spacing",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Copyright spacing",
      "default": 20
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 13
    },
    {
      "type": "range",
      "id": "link_spacing",
      "min": 2,
      "max": 15,
      "step": 1,
      "unit": "px",
      "label": "Link spacing",
      "default": 6
    },
    {
      "type": "range",
      "id": "title_spacing",
      "min": 5,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Title spacing",
      "default": 8
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Top padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Bottom padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "min": 15,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Horizontal padding",
      "default": 20
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f8f8f8"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright color",
      "default": "#888888"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#e0e0e0"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap Footer"
    }
  ],
  "tag": null
}
{% endschema %}