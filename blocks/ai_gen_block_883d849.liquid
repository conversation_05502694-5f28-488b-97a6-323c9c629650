{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-sitemap-footer__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.gap }}px;
  }

  .ai-sitemap-footer__section-{{ ai_gen_id }} {
    margin-bottom: {{ block.settings.section_spacing }}px;
  }

  .ai-sitemap-footer__heading-{{ ai_gen_id }} {
    font-size: calc({{ block.settings.font_size }}px * 1.2);
    font-weight: 600;
    margin-top: 0;
    margin-bottom: {{ block.settings.heading_spacing }}px;
    color: {{ block.settings.heading_color }};
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer__link-item-{{ ai_gen_id }} {
    margin-bottom: {{ block.settings.link_spacing }}px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    text-align: center;
    margin-top: {{ block.settings.copyright_spacing }}px;
    padding-top: {{ block.settings.copyright_spacing }}px;
    border-top: 1px solid {{ block.settings.divider_color }};
    font-size: calc({{ block.settings.font_size }}px * 0.9);
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__grid-{{ ai_gen_id }}">
      {% if block.settings.show_pages %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.pages_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists.main-menu.links %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_collections %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.collections_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for collection in collections limit: block.settings.collections_limit %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ collection.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ collection.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_policies %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.policies_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for policy in shop.policies %}
              {% if policy.title != blank %}
                <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                  <a href="{{ policy.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ policy.title }}</a>
                </li>
              {% endif %}
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_blogs %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.blogs_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for blog in blogs limit: block.settings.blogs_limit %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ blog.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ blog.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_custom_links and block.settings.custom_linklist != blank %}
        <div class="ai-sitemap-footer__section-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.custom_links_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.custom_linklist].links %}
              <li class="ai-sitemap-footer__link-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
      {% if block.settings.copyright_text != blank %}
        {{ block.settings.copyright_text }}
      {% else %}
        &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content Sections"
    },
    {
      "type": "checkbox",
      "id": "show_pages",
      "label": "Show pages",
      "default": true
    },
    {
      "type": "text",
      "id": "pages_heading",
      "label": "Pages heading",
      "default": "Pages"
    },
    {
      "type": "checkbox",
      "id": "show_collections",
      "label": "Show collections",
      "default": true
    },
    {
      "type": "text",
      "id": "collections_heading",
      "label": "Collections heading",
      "default": "Collections"
    },
    {
      "type": "range",
      "id": "collections_limit",
      "min": 3,
      "max": 20,
      "step": 1,
      "label": "Collections to show",
      "default": 6
    },
    {
      "type": "checkbox",
      "id": "show_policies",
      "label": "Show policies",
      "default": true
    },
    {
      "type": "text",
      "id": "policies_heading",
      "label": "Policies heading",
      "default": "Policies"
    },
    {
      "type": "checkbox",
      "id": "show_blogs",
      "label": "Show blogs",
      "default": true
    },
    {
      "type": "text",
      "id": "blogs_heading",
      "label": "Blogs heading",
      "default": "Blogs"
    },
    {
      "type": "range",
      "id": "blogs_limit",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "Blogs to show",
      "default": 3
    },
    {
      "type": "checkbox",
      "id": "show_custom_links",
      "label": "Show custom links",
      "default": false
    },
    {
      "type": "text",
      "id": "custom_links_heading",
      "label": "Custom links heading",
      "default": "Links"
    },
    {
      "type": "link_list",
      "id": "custom_linklist",
      "label": "Custom link list"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text",
      "info": "Leave blank to use store name and current year"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "options": [
        {"value": "1", "label": "1"},
        {"value": "2", "label": "2"},
        {"value": "3", "label": "3"},
        {"value": "4", "label": "4"},
        {"value": "5", "label": "5"}
      ],
      "default": "4",
      "label": "Columns on desktop"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {"value": "1", "label": "1"},
        {"value": "2", "label": "2"}
      ],
      "default": "1",
      "label": "Columns on mobile"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Column gap",
      "default": 30
    },
    {
      "type": "range",
      "id": "section_spacing",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Section spacing",
      "default": 20
    },
    {
      "type": "range",
      "id": "heading_spacing",
      "min": 5,
      "max": 30,
      "step": 5,
      "unit": "px",
      "label": "Heading spacing",
      "default": 15
    },
    {
      "type": "range",
      "id": "link_spacing",
      "min": 5,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Link spacing",
      "default": 8
    },
    {
      "type": "range",
      "id": "copyright_spacing",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Copyright spacing",
      "default": 20
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f3f3f3"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}