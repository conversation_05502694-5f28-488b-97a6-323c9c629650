{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-sitemap-footer__columns-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.gap }}px;
  }

  .ai-sitemap-footer__column-{{ ai_gen_id }} {
    margin-bottom: 20px;
  }

  .ai-sitemap-footer__title-{{ ai_gen_id }} {
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: calc({{ block.settings.font_size }}px + 2px);
    color: {{ block.settings.title_color }};
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    margin-bottom: 6px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} a {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid {{ block.settings.divider_color }};
    text-align: center;
    font-size: calc({{ block.settings.font_size }}px - 2px);
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__columns-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__columns-{{ ai_gen_id }}">
      {% if block.settings.show_pages %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.pages_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists.main-menu.links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_policies %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.policies_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for policy in shop.policies %}
              {% if policy != blank %}
                <li class="ai-sitemap-footer__link-{{ ai_gen_id }}"><a href="{{ policy.url }}">{{ policy.title }}</a>
                </li>
              {% endif %}
            {% endfor %}<li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
              <a href="{{ routes.search_url }}">{{ block.settings.search_text }}</a>
            </li>
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_collections %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.collections_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for collection in collections limit: block.settings.collections_limit %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ collection.url }}">{{ collection.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_custom_links and block.settings.custom_linklist != blank %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ block.settings.custom_links_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.custom_linklist].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
      {% if block.settings.copyright_text != blank %}
        {{ block.settings.copyright_text }}
      {% else %}
        &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 4,
      "label": "Columns on desktop"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "default": 20,
      "unit": "px",
      "label": "Padding"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "default": 20,
      "unit": "px",
      "label": "Column gap"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "checkbox",
      "id": "show_pages",
      "label": "Show pages",
      "default": true
    },
    {
      "type": "text",
      "id": "pages_title",
      "label": "Pages title",
      "default": "Pages"
    },
    {
      "type": "checkbox",
      "id": "show_policies",
      "label": "Show policies",
      "default": true
    },
    {
      "type": "text",
      "id": "policies_title",
      "label": "Policies title",
      "default": "Policies"
    },
    {
      "type": "text",
      "id": "search_text",
      "label": "Search link text",
      "default": "Search"
    },
    {
      "type": "checkbox",
      "id": "show_collections",
      "label": "Show collections",
      "default": true
    },
    {
      "type": "text",
      "id": "collections_title",
      "label": "Collections title",
      "default": "Collections"
    },
    {
      "type": "range",
      "id": "collections_limit",
      "min": 3,
      "max": 10,
      "step": 1,
      "default": 5,
      "label": "Collections to show"
    },
    {
      "type": "checkbox",
      "id": "show_custom_links",
      "label": "Show custom links",
      "default": false
    },
    {
      "type": "text",
      "id": "custom_links_title",
      "label": "Custom links title",
      "default": "Quick links"
    },
    {
      "type": "link_list",
      "id": "custom_linklist",
      "label": "Custom link list"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text",
      "info": "Leave blank to use shop name with current year"
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "default": 14,
      "unit": "px",
      "label": "Font size"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}