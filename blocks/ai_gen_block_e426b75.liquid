{% doc %}
  @prompt
    Create a simple before/after slider with three cards horizontally. Use basic approach: each card has two images stacked, top image has adjustable width controlled by range input slider. When slider is at 50%, top image width is 50% showing half of each image. Moving slider right increases top image width, moving left decreases it. Use simple HTML structure with img tags, CSS for positioning, and basic JavaScript to update image width based on slider value. Include image upload settings in schema.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
.before-after-container-{{ ai_gen_id }} {
  display: flex;
  flex-wrap: wrap;
  gap: {{ block.settings.gap }}px;
  justify-content: center;
  max-width: 100%;
  margin: 0 auto;
}

.before-after-card-{{ ai_gen_id }} {
  position: relative;
  width: calc((100% - {{ block.settings.gap | times: 2 }}px) / 3);
  border-radius: {{ block.settings.border_radius }}px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.before-after-card-{{ ai_gen_id }} h3 {
  text-align: center;
  margin: 10px 0;
  font-size: {{ block.settings.title_size }}px;
  color: {{ block.settings.title_color }};
}

.before-after-wrapper-{{ ai_gen_id }} {
  position: relative;
  width: 100%;
  aspect-ratio: {{ block.settings.aspect_ratio }};
  overflow: hidden;
}

.before-image-{{ ai_gen_id }},
.after-image-{{ ai_gen_id }} {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.before-image-{{ ai_gen_id }} {
  z-index: 1;
  clip-path: inset(0 var(--clip-amount-{{ ai_gen_id }}) 0 0);
}

.before-after-slider-{{ ai_gen_id }} {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  appearance: none;
  background: transparent;
  margin: 0;
  cursor: pointer;
  opacity: 0;
}

.slider-handle-{{ ai_gen_id }} {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: {{ block.settings.slider_color }};
  left: var(--slider-position-{{ ai_gen_id }});
  z-index: 3;
  transform: translateX(-50%);
  pointer-events: none;
}

.slider-handle-{{ ai_gen_id }}::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background-color: {{ block.settings.slider_color }};
  border-radius: 50%;
}

.slider-handle-{{ ai_gen_id }}::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
}

.before-after-placeholder-{{ ai_gen_id }} {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f4f4f4;
}

.before-after-placeholder-{{ ai_gen_id }} svg {
  width: 50%;
  height: 50%;
  opacity: 0.5;
}

@media screen and (max-width: 767px) {
  .before-after-card-{{ ai_gen_id }} {
    width: 100%;
    margin-bottom: 20px;
  }
}
{% endstyle %}

<div class="before-after-container-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% for i in (1..3) %}
    {% assign before_image_key = 'before_image_' | append: i %}
    {% assign after_image_key = 'after_image_' | append: i %}
    {% assign title_key = 'title_' | append: i %}
    {% assign before_image = block.settings[before_image_key] %}
    {% assign after_image = block.settings[after_image_key] %}
    {% assign title = block.settings[title_key] %}
    
    <div class="before-after-card-{{ ai_gen_id }}">
      {% if title != blank %}
        <h3>{{ title }}</h3>
      {% endif %}
      
      <div class="before-after-wrapper-{{ ai_gen_id }}">
        {% if after_image %}
          <img 
            src="{{ after_image | image_url: width: 800 }}" 
            alt="{{ after_image.alt | escape }}" 
            class="after-image-{{ ai_gen_id }}"
            loading="lazy"
            width="{{ after_image.width }}"
            height="{{ after_image.height }}"
          >
        {% else %}
          <div class="before-after-placeholder-{{ ai_gen_id }}">
            {{ 'image' | placeholder_svg_tag }}
          </div>
        {% endif %}
        
        {% if before_image %}
          <img 
            src="{{ before_image | image_url: width: 800 }}" 
            alt="{{ before_image.alt | escape }}" 
            class="before-image-{{ ai_gen_id }}"
            loading="lazy"
            width="{{ before_image.width }}"
            height="{{ before_image.height }}"
            style="--clip-amount-{{ ai_gen_id }}: 50%"
          >
        {% else %}
          <div class="before-after-placeholder-{{ ai_gen_id }}">
            {{ 'image' | placeholder_svg_tag }}
          </div>
        {% endif %}
        
        <div class="slider-handle-{{ ai_gen_id }}" style="--slider-position-{{ ai_gen_id }}: 50%"></div>
        <input 
          type="range" 
          min="0" 
          max="100" 
          value="50" 
          class="before-after-slider-{{ ai_gen_id }}"
          aria-label="Adjust before-after comparison"
        >
      </div>
    </div>
  {% endfor %}
</div>

<script>
(function() {
  const sliders = document.querySelectorAll('.before-after-slider-{{ ai_gen_id }}');
  
  sliders.forEach(slider => {
    const wrapper = slider.closest('.before-after-wrapper-{{ ai_gen_id }}');
    const beforeImage = wrapper.querySelector('.before-image-{{ ai_gen_id }}');
    const sliderHandle = wrapper.querySelector('.slider-handle-{{ ai_gen_id }}');
    
    function updateSlider() {
      const value = slider.value;
      const clipAmount = 100 - value + '%';
      beforeImage.style.setProperty('--clip-amount-{{ ai_gen_id }}', clipAmount);
      sliderHandle.style.setProperty('--slider-position-{{ ai_gen_id }}', value + '%');
    }
    
    // Set initial position
    updateSlider();
    
    // Update on slider change
    slider.addEventListener('input', updateSlider);
    
    // For touch devices
    slider.addEventListener('touchmove', function(e) {
      e.stopPropagation();
    });
  });
})();
</script>

{% schema %}
{
  "name": "Before After Slider",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Card 1"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title",
      "default": "Before & After"
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Card 2"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title",
      "default": "Before & After"
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Card 3"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title",
      "default": "Before & After"
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Image aspect ratio",
      "options": [
        {
          "value": "1/1",
          "label": "1:1 (Square)"
        },
        {
          "value": "3/4",
          "label": "3:4 (Portrait)"
        },
        {
          "value": "4/3",
          "label": "4:3 (Landscape)"
        },
        {
          "value": "16/9",
          "label": "16:9 (Widescreen)"
        }
      ],
      "default": "4/3"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Gap between cards",
      "default": 20
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 12,
      "max": 36,
      "step": 1,
      "unit": "px",
      "label": "Title size",
      "default": 18
    }
  ],
  "presets": [
    {
      "name": "Before After Slider"
    }
  ]
}
{% endschema %}