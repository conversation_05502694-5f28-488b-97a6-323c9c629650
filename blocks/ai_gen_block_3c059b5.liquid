{% doc %}
  @prompt
    Create a gallery section with before-and-after image sliders for dental procedures. Include multiple slider examples that showcase dental transformations with a clean, professional design. Each slider should allow visitors to compare before and after results by dragging or clicking., i can't see slider images


{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .dental-comparison-{{ ai_gen_id }} {
    margin-bottom: 30px;
  }

  .dental-comparison-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.title_color }};
    margin-bottom: 15px;
    text-align: {{ block.settings.text_alignment }};
  }

  .dental-comparison-description-{{ ai_gen_id }} {
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.description_color }};
    margin-bottom: 20px;
    text-align: {{ block.settings.text_alignment }};
  }

  .dental-slider-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, {{ block.settings.shadow_opacity | divided_by: 100.0 }});
    aspect-ratio: 4/3;
  }

  .dental-slider-before-{{ ai_gen_id }},
  .dental-slider-after-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .dental-slider-before-{{ ai_gen_id }} {
    z-index: 1;
  }

  .dental-slider-after-{{ ai_gen_id }} {
    z-index: 2;
    width: 50%;
    overflow: hidden;
  }

  .dental-slider-image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .dental-slider-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dental-slider-placeholder-{{ ai_gen_id }} svg {
    width: 50%;
    height: 50%;
    opacity: 0.5;
  }

  .dental-slider-divider-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background-color: {{ block.settings.divider_color }};
    z-index: 3;
    transform: translateX(-50%);
  }

  .dental-slider-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: {{ block.settings.handle_color }};
    border: 3px solid white;
    z-index: 4;
    transform: translate(-50%, -50%);
    cursor: ew-resize;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dental-slider-handle-{{ ai_gen_id }}::before,
  .dental-slider-handle-{{ ai_gen_id }}::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 2px;
    background-color: white;
  }

  .dental-slider-handle-{{ ai_gen_id }}::before {
    transform: translateX(-4px) rotate(45deg);
  }

  .dental-slider-handle-{{ ai_gen_id }}::after {
    transform: translateX(-4px) rotate(-45deg);
  }

  .dental-slider-label-{{ ai_gen_id }} {
    position: absolute;
    bottom: 10px;
    padding: 5px 10px;
    background-color: {{ block.settings.label_bg_color }};
    color: {{ block.settings.label_text_color }};
    font-size: 14px;
    z-index: 5;
    border-radius: 4px;
  }

  .dental-slider-label-before-{{ ai_gen_id }} {
    left: 10px;
  }

  .dental-slider-label-after-{{ ai_gen_id }} {
    right: 10px;
  }

  .dental-slider-gallery-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: 20px;
  }

  .dental-slider-item-{{ ai_gen_id }} {
    margin-bottom: 20px;
  }

  @media screen and (max-width: 749px) {
    .dental-slider-gallery-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<dental-before-after-{{ ai_gen_id }} class="dental-comparison-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.title != blank %}
    <h2 class="dental-comparison-title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
  {% endif %}
  
  {% if block.settings.description != blank %}
    <div class="dental-comparison-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
  {% endif %}

  <div class="dental-slider-gallery-{{ ai_gen_id }}">
    {% for i in (1..4) %}
      {% assign before_image_key = 'before_image_' | append: i %}
      {% assign after_image_key = 'after_image_' | append: i %}
      {% assign title_key = 'title_' | append: i %}
      
      {% assign before_image = block.settings[before_image_key] %}
      {% assign after_image = block.settings[after_image_key] %}
      {% assign title = block.settings[title_key] %}
      
      {% if title != blank %}
        <div class="dental-slider-item-{{ ai_gen_id }}" data-slider-index="{{ i }}">
          <h3 class="dental-comparison-title-{{ ai_gen_id }}" style="font-size: {{ block.settings.title_size | minus: 4 }}px;">{{ title }}</h3>
          
          <div class="dental-slider-container-{{ ai_gen_id }}">
            <div class="dental-slider-before-{{ ai_gen_id }}">
              {% if before_image != blank %}
                <img 
                  src="{{ before_image | image_url: width: 800 }}"
                  alt="{{ before_image.alt | escape }}"
                  width="{{ before_image.width }}"
                  height="{{ before_image.height }}"
                  loading="lazy"
                  class="dental-slider-image-{{ ai_gen_id }}"
                >
              {% else %}
                <div class="dental-slider-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>
            
            <div class="dental-slider-after-{{ ai_gen_id }}">
              {% if after_image != blank %}
                <img 
                  src="{{ after_image | image_url: width: 800 }}"
                  alt="{{ after_image.alt | escape }}"
                  width="{{ after_image.width }}"
                  height="{{ after_image.height }}"
                  loading="lazy"
                  class="dental-slider-image-{{ ai_gen_id }}"
                >
              {% else %}
                <div class="dental-slider-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>
            
            <div class="dental-slider-divider-{{ ai_gen_id }}"></div>
            <div class="dental-slider-handle-{{ ai_gen_id }}" role="slider" aria-valuemin="0" aria-valuemax="100" aria-valuenow="50" tabindex="0"></div>
            
            {% if block.settings.show_labels %}
              <div class="dental-slider-label-{{ ai_gen_id }} dental-slider-label-before-{{ ai_gen_id }}">{{ block.settings.before_label }}</div>
              <div class="dental-slider-label-{{ ai_gen_id }} dental-slider-label-after-{{ ai_gen_id }}">{{ block.settings.after_label }}</div>
            {% endif %}
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
</dental-before-after-{{ ai_gen_id }}>

<script>
  const slider = document.getElementById('gallery-slider');
  const images = slider.querySelectorAll('img');
  let currentSlide = 0;

  let startX = 0;
  let endX = 0;

  function showSlide(index) {
    images.forEach((img, i) => {
      img.style.display = i === index ? 'block' : 'none';
    });
  }

  // Начальная инициализация
  showSlide(currentSlide);

  // Свайп (тач)
  slider.addEventListener('touchstart', (e) => {
    startX = e.touches[0].clientX;
  });

  slider.addEventListener('touchend', (e) => {
    endX = e.changedTouches[0].clientX;
    handleSwipe();
  });

  // Свайп (мышь)
  slider.addEventListener('mousedown', (e) => {
    startX = e.clientX;
  });

  slider.addEventListener('mouseup', (e) => {
    endX = e.clientX;
    handleSwipe();
  });

  function handleSwipe() {
    let dx = endX - startX;
    if (Math.abs(dx) > 50) {
      if (dx < 0) {
        // Влево
        currentSlide = (currentSlide + 1) % images.length;
      } else {
        // Вправо
        currentSlide = (currentSlide - 1 + images.length) % images.length;
      }
      showSlide(currentSlide);
    }
  }
</script>


{% schema %}
{
  "name": "Dental Before-After",
  "tag": null,
  "class": "dental-before-after-section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Dental Transformations"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>See the remarkable results of our dental procedures. Drag the slider to compare before and after images.</p>"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "header",
      "content": "Slider 1"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title",
      "default": "Teeth Whitening"
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Slider 2"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title",
      "default": "Dental Implants"
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Slider 3"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title",
      "default": "Veneers"
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Slider 4"
    },
    {
      "type": "text",
      "id": "title_4",
      "label": "Title",
      "default": "Orthodontics"
    },
    {
      "type": "image_picker",
      "id": "before_image_4",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_4",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show before/after labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 28
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "shadow_opacity",
      "min": 0,
      "max": 30,
      "step": 5,
      "unit": "%",
      "label": "Shadow opacity",
      "default": 15
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "handle_color",
      "label": "Handle color",
      "default": "#0066cc"
    },
    {
      "type": "color",
      "id": "label_bg_color",
      "label": "Label background color",
      "default": "#0066cc"
    },
    {
      "type": "color",
      "id": "label_text_color",
      "label": "Label text color",
      "default": "#ffffff"
    }
  ],
  "presets": [
    {
      "name": "Dental Before-After"
    }
  ]
}
{% endschema %}