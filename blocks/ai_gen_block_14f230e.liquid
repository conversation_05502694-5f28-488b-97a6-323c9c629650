{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all website pages, responsive design for mobile and desktop. Include editable copyright information at the bottom, email and phone number on the right side. Make the layout small and compact with organized navigation links, contact details, and footer text. Ensure all elements are customizable through section settings.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .compact-sitemap-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px 0;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
  }

  .compact-sitemap__container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .compact-sitemap__top-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 30px;
  }

  .compact-sitemap__nav-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    flex: 1;
    min-width: 280px;
  }

  .compact-sitemap__nav-column-{{ ai_gen_id }} {
    min-width: 120px;
    flex: 1;
  }

  .compact-sitemap__nav-title-{{ ai_gen_id }} {
    font-size: calc({{ block.settings.text_size }}px * 1.2);
    font-weight: 600;
    margin-bottom: 15px;
    color: {{ block.settings.heading_color }};
  }

  .compact-sitemap__nav-list-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .compact-sitemap__nav-item-{{ ai_gen_id }} {
    margin-bottom: 8px;
  }

  .compact-sitemap__nav-link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s;
  }

  .compact-sitemap__nav-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .compact-sitemap__contact-{{ ai_gen_id }} {
    min-width: 200px;
  }

  .compact-sitemap__contact-item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .compact-sitemap__contact-icon-{{ ai_gen_id }} {
    margin-right: 8px;
    width: 16px;
    height: 16px;
  }

  .compact-sitemap__contact-link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s;
  }

  .compact-sitemap__contact-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .compact-sitemap__bottom-{{ ai_gen_id }} {
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid {{ block.settings.divider_color }};
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
  }

  .compact-sitemap__copyright-{{ ai_gen_id }} {
    font-size: calc({{ block.settings.text_size }}px * 0.9);
  }

  @media screen and (max-width: 767px) {
    .compact-sitemap__top-{{ ai_gen_id }} {
      flex-direction: column;
      gap: 20px;
    }

    .compact-sitemap__nav-{{ ai_gen_id }} {
      flex-direction: column;
      gap: 20px;
    }

    .compact-sitemap__bottom-{{ ai_gen_id }} {
      flex-direction: column;
      align-items: flex-start;
    }
  }
{% endstyle %}

<div class="compact-sitemap-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="compact-sitemap__container-{{ ai_gen_id }}">
    <div class="compact-sitemap__top-{{ ai_gen_id }}">
      <div class="compact-sitemap__nav-{{ ai_gen_id }}">
        {% if block.settings.menu_1 != blank %}
          <div class="compact-sitemap__nav-column-{{ ai_gen_id }}">
            <h3 class="compact-sitemap__nav-title-{{ ai_gen_id }}">{{ block.settings.menu_1_title }}</h3>
            <ul class="compact-sitemap__nav-list-{{ ai_gen_id }}">
              {% for link in linklists[block.settings.menu_1].links %}
                <li class="compact-sitemap__nav-item-{{ ai_gen_id }}">
                  <a href="{{ link.url }}" class="compact-sitemap__nav-link-{{ ai_gen_id }}">{{ link.title }}</a>
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if block.settings.menu_2 != blank %}
          <div class="compact-sitemap__nav-column-{{ ai_gen_id }}">
            <h3 class="compact-sitemap__nav-title-{{ ai_gen_id }}">{{ block.settings.menu_2_title }}</h3>
            <ul class="compact-sitemap__nav-list-{{ ai_gen_id }}">
              {% for link in linklists[block.settings.menu_2].links %}
                <li class="compact-sitemap__nav-item-{{ ai_gen_id }}">
                  <a href="{{ link.url }}" class="compact-sitemap__nav-link-{{ ai_gen_id }}">{{ link.title }}</a>
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if block.settings.menu_3 != blank %}
          <div class="compact-sitemap__nav-column-{{ ai_gen_id }}">
            <h3 class="compact-sitemap__nav-title-{{ ai_gen_id }}">{{ block.settings.menu_3_title }}</h3>
            <ul class="compact-sitemap__nav-list-{{ ai_gen_id }}">
              {% for link in linklists[block.settings.menu_3].links %}
                <li class="compact-sitemap__nav-item-{{ ai_gen_id }}">
                  <a href="{{ link.url }}" class="compact-sitemap__nav-link-{{ ai_gen_id }}">{{ link.title }}</a>
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>

      <div class="compact-sitemap__contact-{{ ai_gen_id }}">
        <h3 class="compact-sitemap__nav-title-{{ ai_gen_id }}">{{ block.settings.contact_title }}</h3>
        
        {% if block.settings.email != blank %}
          <div class="compact-sitemap__contact-item-{{ ai_gen_id }}">
            <svg class="compact-sitemap__contact-icon-{{ ai_gen_id }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <a href="mailto:{{ block.settings.email }}" class="compact-sitemap__contact-link-{{ ai_gen_id }}">{{ block.settings.email }}</a>
          </div>
        {% endif %}
        
        {% if block.settings.phone != blank %}
          <div class="compact-sitemap__contact-item-{{ ai_gen_id }}">
            <svg class="compact-sitemap__contact-icon-{{ ai_gen_id }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
            </svg>
            <a href="tel:{{ block.settings.phone }}" class="compact-sitemap__contact-link-{{ ai_gen_id }}">{{ block.settings.phone }}</a>
          </div>
        {% endif %}
        
        {% if block.settings.address != blank %}
          <div class="compact-sitemap__contact-item-{{ ai_gen_id }}">
            <svg class="compact-sitemap__contact-icon-{{ ai_gen_id }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <span>{{ block.settings.address }}</span>
          </div>
        {% endif %}
      </div>
    </div>

    <div class="compact-sitemap__bottom-{{ ai_gen_id }}">
      <div class="compact-sitemap__copyright-{{ ai_gen_id }}">
        {{ block.settings.copyright_text }}
      </div>
      
      {% if block.settings.show_payment_icons %}
        <div class="compact-sitemap__payment-icons-{{ ai_gen_id }}">
          {% if shop.enabled_payment_types != empty %}
            {%- for type in shop.enabled_payment_types -%}
              {{ type | payment_type_svg_tag: class: 'icon icon--payment' }}
            {%- endfor -%}
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Navigation Menus"
    },
    {
      "type": "text",
      "id": "menu_1_title",
      "label": "Menu 1 Title",
      "default": "Shop"
    },
    {
      "type": "link_list",
      "id": "menu_1",
      "label": "Menu 1"
    },
    {
      "type": "text",
      "id": "menu_2_title",
      "label": "Menu 2 Title",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "menu_2",
      "label": "Menu 2"
    },
    {
      "type": "text",
      "id": "menu_3_title",
      "label": "Menu 3 Title",
      "default": "Customer Service"
    },
    {
      "type": "link_list",
      "id": "menu_3",
      "label": "Menu 3"
    },
    {
      "type": "header",
      "content": "Contact Information"
    },
    {
      "type": "text",
      "id": "contact_title",
      "label": "Contact Title",
      "default": "Contact Us"
    },
    {
      "type": "text",
      "id": "email",
      "label": "Email Address",
      "default": "<EMAIL>"
    },
    {
      "type": "text",
      "id": "phone",
      "label": "Phone Number",
      "default": "+****************"
    },
    {
      "type": "text",
      "id": "address",
      "label": "Address",
      "default": "123 Main Street, City, Country"
    },
    {
      "type": "header",
      "content": "Footer Bottom"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright Text",
      "default": "<p>© 2023 Your Store Name. All rights reserved.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show payment icons",
      "default": true
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link Color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link Hover Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider Color",
      "default": "#dddddd"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Text Size",
      "default": 14
    },
    {
      "type": "range",
      "id": "padding",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 40
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ],
  "class": "compact-sitemap-block"
}
{% endschema %}