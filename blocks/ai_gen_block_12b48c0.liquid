{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-sitemap-footer__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.gap }}px;
    margin-bottom: {{ block.settings.gap }}px;
  }

  .ai-sitemap-footer__column-{{ ai_gen_id }} h3 {
    font-size: calc({{ block.settings.font_size }}px * 1.2);
    margin-top: 0;
    margin-bottom: {{ block.settings.gap | divided_by: 2 }}px;
    color: {{ block.settings.heading_color }};
    font-weight: 600;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    margin-bottom: {{ block.settings.gap | divided_by: 3 }}px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} a {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
    display: inline-block;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    text-align: center;
    padding-top: {{ block.settings.gap | divided_by: 2 }}px;
    border-top: 1px solid {{ block.settings.divider_color }};
    font-size: calc({{ block.settings.font_size }}px * 0.9);
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
    .ai-sitemap-footer-{{ ai_gen_id }} {
      padding: {{ block.settings.padding | divided_by: 1.5 }}px;
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__grid-{{ ai_gen_id }}">
      {% if linklists[block.settings.menu_main].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.main_menu_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_main].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_secondary].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.secondary_menu_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_secondary].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_tertiary].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.tertiary_menu_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_tertiary].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_quaternary].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.quaternary_menu_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_quaternary].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_policy_links %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.policy_menu_title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% if shop.privacy_policy %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ shop.privacy_policy.url }}">{{ shop.privacy_policy.title }}</a>
              </li>
            {% endif %}
            {% if shop.terms_of_service %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ shop.terms_of_service.url }}">{{ shop.terms_of_service.title }}</a>
              </li>
            {% endif %}
            {% if shop.refund_policy %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ shop.refund_policy.url }}">{{ shop.refund_policy.title }}</a>
              </li>
            {% endif %}
            {% if shop.shipping_policy %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ shop.shipping_policy.url }}">{{ shop.shipping_policy.title }}</a>
              </li>
            {% endif %}
          </ul>
        </div>
      {% endif %}
    </div>
    
    <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
      {% if block.settings.copyright_text != blank %}
        {{ block.settings.copyright_text }}
      {% else %}
        &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}. All rights reserved.
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Sitemap Footer",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Menu Links"
    },
    {
      "type": "text",
      "id": "main_menu_title",
      "label": "Main menu title",
      "default": "Main menu"
    },
    {
      "type": "link_list",
      "id": "menu_main",
      "label": "Main menu",
      "default": "main-menu"
    },
    {
      "type": "text",
      "id": "secondary_menu_title",
      "label": "Secondary menu title",
      "default": "Shop"
    },
    {
      "type": "link_list",
      "id": "menu_secondary",
      "label": "Secondary menu"
    },
    {
      "type": "text",
      "id": "tertiary_menu_title",
      "label": "Tertiary menu title",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "menu_tertiary",
      "label": "Tertiary menu"
    },
    {
      "type": "text",
      "id": "quaternary_menu_title",
      "label": "Quaternary menu title",
      "default": "Customer Service"
    },
    {
      "type": "link_list",
      "id": "menu_quaternary",
      "label": "Quaternary menu"
    },
    {
      "type": "checkbox",
      "id": "show_policy_links",
      "label": "Show policy links",
      "default": true
    },
    {
      "type": "text",
      "id": "policy_menu_title",
      "label": "Policy menu title",
      "default": "Policies"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "options": [
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        },
        {
          "value": "4",
          "label": "4"
        },
        {
          "value": "5",
          "label": "5"
        }
      ],
      "default": "4"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Gap between elements",
      "default": 20
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    }
  ],
  "presets": [
    {
      "name": "Sitemap Footer"
    }
  ]
}
{% endschema %}