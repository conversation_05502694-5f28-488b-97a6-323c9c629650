{% doc %}
  @prompt
    Create an attractive contact information section with customizable fields for email address and two phone numbers. The section should have a modern, professional design with clear visual hierarchy, icons for each contact method, and be mobile-responsive. Include placeholder text that can be easily customized.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-contact-info-{{ ai_gen_id }} {
    background-color: {{ block.settings.background_color }};
    padding: {{ block.settings.padding }}px;
    border-radius: {{ block.settings.border_radius }}px;
    max-width: 100%;
  }

  .ai-contact-info__container-{{ ai_gen_id }} {
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
  }

  .ai-contact-info__header-{{ ai_gen_id }} {
    text-align: {{ block.settings.text_alignment }};
    margin-bottom: 32px;
  }

  .ai-contact-info__title-{{ ai_gen_id }} {
    color: {{ block.settings.title_color }};
    font-size: {{ block.settings.title_size }}px;
    font-weight: 600;
    margin: 0 0 8px;line-height: 1.2;
  }

  .ai-contact-info__subtitle-{{ ai_gen_id }} {
    color: {{ block.settings.subtitle_color }};
    font-size: {{ block.settings.subtitle_size }}px;
    margin: 0;
    line-height: 1.4;
  }

  .ai-contact-info__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-top: 32px;
  }

  .ai-contact-info__item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background-color: {{ block.settings.item_background_color }};
    border-radius: {{ block.settings.item_border_radius }}px;
    border: 1px solid {{ block.settings.item_border_color }};
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .ai-contact-info__item-{{ ai_gen_id }}:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .ai-contact-info__icon-{{ ai_gen_id }} {
    flex-shrink: 0;
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    background-color: {{ block.settings.icon_background_color }};
    color: {{ block.settings.icon_color }};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-contact-info__icon-{{ ai_gen_id }} svg {
    width: calc({{ block.settings.icon_size }}px * 0.5);
    height: calc({{ block.settings.icon_size }}px * 0.5);
  }

  .ai-contact-info__content-{{ ai_gen_id }} {
    flex-grow: 1;
    min-width: 0;}

  .ai-contact-info__label-{{ ai_gen_id }} {
    color: {{ block.settings.label_color }};
    font-size: {{ block.settings.label_size }}px;
    font-weight: 500;
    margin: 0 0 4px;
    line-height: 1.2;
  }

  .ai-contact-info__value-{{ ai_gen_id }} {
    color: {{ block.settings.value_color }};
    font-size: {{ block.settings.value_size }}px;
    margin: 0;
    line-height: 1.4;word-break: break-all;
  }

  .ai-contact-info__link-{{ ai_gen_id }} {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .ai-contact-info__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  @media screen and (max-width: 749px) {
    .ai-contact-info__grid-{{ ai_gen_id }} {
      grid-template-columns:1fr;
      gap: 16px;
    }

    .ai-contact-info__item-{{ ai_gen_id }} {
      padding: 16px;gap: 12px;
    }

    .ai-contact-info__title-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.title_size }}px * 0.9);
    }

    .ai-contact-info__subtitle-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.subtitle_size }}px * 0.9);
    }
  }
{% endstyle %}<div class="ai-contact-info-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-contact-info__container-{{ ai_gen_id }}">
    {% if block.settings.title != blank or block.settings.subtitle != blank %}
      <div class="ai-contact-info__header-{{ ai_gen_id }}">
        {% if block.settings.title != blank %}
          <h2 class="ai-contact-info__title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
        {% endif %}
        {% if block.settings.subtitle != blank %}
          <div class="ai-contact-info__subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</div>
        {% endif %}
      </div>
    {% endif %}

    <div class="ai-contact-info__grid-{{ ai_gen_id }}">
      {% if block.settings.email_address != blank %}
        <div class="ai-contact-info__item-{{ ai_gen_id }}">
          <div class="ai-contact-info__icon-{{ ai_gen_id }}">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
              <polyline points="22,6 12,13 2,6"/>
            </svg>
          </div>
          <div class="ai-contact-info__content-{{ ai_gen_id }}">
            <div class="ai-contact-info__label-{{ ai_gen_id }}">{{ block.settings.email_label }}</div>
            <div class="ai-contact-info__value-{{ ai_gen_id }}"><a href="mailto:{{ block.settings.email_address }}" class="ai-contact-info__link-{{ ai_gen_id }}">
                {{ block.settings.email_address }}
              </a>
            </div>
          </div>
        </div>
      {% endif %}

      {% if block.settings.phone_1_number != blank %}
        <div class="ai-contact-info__item-{{ ai_gen_id }}">
          <div class="ai-contact-info__icon-{{ ai_gen_id }}">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
          </div>
          <div class="ai-contact-info__content-{{ ai_gen_id }}">
            <div class="ai-contact-info__label-{{ ai_gen_id }}">{{ block.settings.phone_1_label }}</div>
            <div class="ai-contact-info__value-{{ ai_gen_id }}">
              <a href="tel:{{ block.settings.phone_1_number }}" class="ai-contact-info__link-{{ ai_gen_id }}">
                {{ block.settings.phone_1_number }}
              </a>
            </div>
          </div>
        </div>
      {% endif %}

      {% if block.settings.phone_2_number != blank %}
        <div class="ai-contact-info__item-{{ ai_gen_id }}">
          <div class="ai-contact-info__icon-{{ ai_gen_id }}">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
          </div>
          <div class="ai-contact-info__content-{{ ai_gen_id }}">
            <div class="ai-contact-info__label-{{ ai_gen_id }}">{{ block.settings.phone_2_label }}</div>
            <div class="ai-contact-info__value-{{ ai_gen_id }}">
              <a href="tel:{{ block.settings.phone_2_number }}" class="ai-contact-info__link-{{ ai_gen_id }}">
                {{ block.settings.phone_2_number }}
              </a>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Contact Information",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Get in Touch"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "We're here to help and answer any questions you might have."
    },
    {
      "type": "header",
      "content": "Email"
    },
    {
      "type": "text",
      "id": "email_label",
      "label": "Email label",
      "default": "Email Address"
    },
    {
      "type": "text",
      "id": "email_address",
      "label": "Email address",
      "default": "<EMAIL>"
    },
    {
      "type": "header",
      "content": "Phone1"
    },
    {
      "type": "text",
      "id": "phone_1_label",
      "label": "Phone 1 label",
      "default": "Customer Service"
    },
    {
      "type": "text",
      "id": "phone_1_number",
      "label": "Phone 1 number",
      "default": "+****************"
    },
    {
      "type": "header",
      "content": "Phone 2"
    },
    {
      "type": "text",
      "id": "phone_2_label",
      "label": "Phone 2 label",
      "default": "Sales Support"
    },
    {
      "type": "text",
      "id": "phone_2_number",
      "label": "Phone 2 number",
      "default": "+****************"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 400,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Maximum width",
      "default": 800
    },
    {
      "type": "range",
      "id": "padding",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Section padding",
      "default": 40
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "item_background_color",
      "label": "Item background color",
      "default": "#f8f9fa"
    },
    {
      "type": "color",
      "id": "item_border_color",
      "label": "Item border color",
      "default": "#e9ecef"
    },
    {
      "type": "color",
      "id": "icon_background_color",
      "label": "Icon background color",
      "default": "#007bff"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "label_color",
      "label": "Label color",
      "default": "#495057"
    },
    {
      "type": "color",
      "id": "value_color",
      "label": "Value color",
      "default": "#212529"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#007bff"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 28
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 16
    },
    {
      "type": "range",
      "id": "label_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Label size",
      "default": 14
    },
    {
      "type": "range",
      "id": "value_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Value size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Section border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "item_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Item border radius",
      "default": 6
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 32,
      "max": 64,
      "step": 4,
      "unit": "px",
      "label": "Icon size",
      "default": 48
    }
  ],
  "presets": [
    {
      "name": "Contact Information"
    }
  ]
}
{% endschema %}