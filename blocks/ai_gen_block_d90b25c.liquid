{% doc %}
  @prompt
    Create a before and after image comparison slider that allows visitors to drag a slider to reveal the before and after images. The slider should be responsive and work well on both mobile and desktop devices., i can't see images because of text is covering it
, still can't see images
, no, it shoud be like an usual slider gallery when you can slide pics before/after results by yourself
, that was the good one, but can you please make gallery of pics like this? like 5 - 6 before/after slider pics gallery please


{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-gallery-container-{{ ai_gen_id }} {
    width: 100%;
    position: relative;
    overflow: hidden;
  }

  .ai-gallery-nav-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    gap: 10px;
  }

  .ai-gallery-nav-button-{{ ai_gen_id }} {
    background-color: {{ block.settings.nav_button_color }};
    color: {{ block.settings.nav_button_text_color }};
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .ai-gallery-nav-button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.nav_button_hover_color }};
  }

  .ai-gallery-pagination-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    gap: 8px;
  }

  .ai-gallery-dot-{{ ai_gen_id }} {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: {{ block.settings.dot_color }};
    opacity: 0.5;
    cursor: pointer;
    transition: opacity 0.3s;
  }

  .ai-gallery-dot-{{ ai_gen_id }}.active {
    opacity: 1;background-color: {{ block.settings.dot_active_color }};
  }

  .ai-gallery-slides-{{ ai_gen_id }} {
    display: flex;
    transition: transform 0.5s ease;
    width: 100%;}

  .ai-gallery-slide-{{ ai_gen_id }} {
    flex: 0 0 100%;
    width: 100%;}

  .ai-image-compare-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    padding-bottom: {{ block.settings.aspect_ratio }}%;
  }

  .ai-image-compare-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .ai-image-compare-before-{{ ai_gen_id }},
  .ai-image-compare-after-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .ai-image-compare-before-{{ ai_gen_id }} {
    left: 0;
    z-index: 1;
  }

  .ai-image-compare-after-{{ ai_gen_id }} {
    right: 0;
    z-index: 2;width: 50%;
  }

  .ai-image-compare-img-{{ ai_gen_id }} {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-image-compare-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;}

  .ai-image-compare-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
  }

  .ai-image-compare-slider-{{ ai_gen_id }} {
    position: absolute;
    z-index: 9;
    top: 0;
    bottom: 0;
    left: 50%;
    width: {{ block.settings.slider_width }}px;
    background-color: {{ block.settings.slider_color }};
    transform: translateX(-50%);
    cursor: ew-resize;
  }

  .ai-image-compare-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: {{ block.settings.handle_size }}px;
    height: {{ block.settings.handle_size }}px;
    border-radius: 50%;
    background-color: {{ block.settings.handle_color }};
    border: {{ block.settings.handle_border_width }}px solid {{ block.settings.handle_border_color }};
    transform: translate(-50%, -50%);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .ai-image-compare-handle-{{ ai_gen_id }}::before,
  .ai-image-compare-handle-{{ ai_gen_id }}::after {
    content: "";
    position: absolute;
    background-color: {{ block.settings.handle_arrow_color }};}

  .ai-image-compare-handle-{{ ai_gen_id }}::before {
    width: 2px;
    height: 10px;
    left: calc(50% - 6px);
    transform: rotate(45deg);
  }

  .ai-image-compare-handle-{{ ai_gen_id }}::after {
    width: 2px;
    height: 10px;
    right: calc(50% - 6px);
    transform: rotate(-45deg);
  }

  .ai-image-compare-labels-{{ ai_gen_id }} {
    position: absolute;
    bottom: 20px;
    width: 100%;
    z-index: 8;
    pointer-events: none;
  }

  .ai-image-compare-label-{{ ai_gen_id }} {
    position: absolute;
    background-color: {{ block.settings.label_bg_color }};
    color: {{ block.settings.label_text_color }};
    padding: 5px 10px;
    border-radius: 4px;
    font-size: {{ block.settings.label_font_size }}px;
    font-weight: 500;
    opacity: 0.9;
  }

  .ai-image-compare-label-before-{{ ai_gen_id }} {
    left: 20px;
  }

  .ai-image-compare-label-after-{{ ai_gen_id }} {
    right: 20px;
  }

  .ai-gallery-title-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 10px;
    color: {{ block.settings.title_color }};
    font-size: {{ block.settings.title_font_size }}px;}

  @media screen and (max-width: 749px) {
    .ai-image-compare-handle-{{ ai_gen_id }} {
      width: {{ block.settings.handle_size | times: 0.8 }}px;
      height: {{ block.settings.handle_size | times: 0.8 }}px;
    }

    .ai-image-compare-label-{{ ai_gen_id }} {
      font-size: {{ block.settings.label_font_size | times: 0.8 }}px;
      padding: 4px 8px;
    }
    
    .ai-image-compare-labels-{{ ai_gen_id }} {
      bottom: 10px;
    }
    .ai-image-compare-label-before-{{ ai_gen_id }} {
      left: 10px;
    }

    .ai-image-compare-label-after-{{ ai_gen_id }} {
      right: 10px;
    }
    
    .ai-gallery-title-{{ ai_gen_id }} {
      font-size: {{ block.settings.title_font_size | times: 0.8 }}px;
    }
  }
{% endstyle %}

<before-after-gallery-{{ ai_gen_id }}
  class="ai-gallery-container-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  {% if block.settings.gallery_title != blank %}
    <h2 class="ai-gallery-title-{{ ai_gen_id }}">{{ block.settings.gallery_title }}</h2>
  {% endif %}

  <div class="ai-gallery-slides-{{ ai_gen_id }}">
    {% for i in (1..6) %}
      {% liquid
        assign before_image_key = 'before_image_' | append: i
        assign after_image_key = 'after_image_' | append: i
        assign slide_title_key = 'slide_title_' | append: i
        
        assign before_image = block.settings[before_image_key]
        assign after_image = block.settings[after_image_key]
        assign slide_title = block.settings[slide_title_key]
      %}
      
      {% if before_image != blank or after_image != blank %}
        <div class="ai-gallery-slide-{{ ai_gen_id }}" data-index="{{ forloop.index0 }}">
          {% if slide_title != blank %}
            <h3 class="ai-gallery-title-{{ ai_gen_id }}">{{ slide_title }}</h3>
          {% endif %}
          
          <div class="ai-image-compare-{{ ai_gen_id }}">
            <div class="ai-image-wrapper-{{ ai_gen_id }}">
              <div class="ai-image-compare-container-{{ ai_gen_id }}">
                <div class="ai-image-compare-before-{{ ai_gen_id }}">
                  {% if before_image %}
                    <img
                      src="{{ before_image | image_url: width: 2000 }}"
                      alt="{{ before_image.alt | escape | default: block.settings.before_label }}"
                      class="ai-image-compare-img-{{ ai_gen_id }}"loading="lazy"width="{{ before_image.width }}"
                      height="{{ before_image.height }}"
                    >
                  {% else %}
                    <div class="ai-image-compare-placeholder-{{ ai_gen_id }}">
                      {{ 'image' | placeholder_svg_tag }}
                    </div>
                  {% endif %}
                </div>
                <div class="ai-image-compare-after-{{ ai_gen_id }}">
                  {% if after_image %}
                    <img
                      src="{{ after_image | image_url: width: 2000 }}"
                      alt="{{ after_image.alt | escape | default: block.settings.after_label }}"
                      class="ai-image-compare-img-{{ ai_gen_id }}"
                      loading="lazy"
                      width="{{ after_image.width }}"
                      height="{{ after_image.height }}"
                    >
                  {% else %}
                    <div class="ai-image-compare-placeholder-{{ ai_gen_id }}">
                      {{ 'image' | placeholder_svg_tag }}
                    </div>
                  {% endif %}
                </div><div class="ai-image-compare-slider-{{ ai_gen_id }}">
                  <div class="ai-image-compare-handle-{{ ai_gen_id }}"></div>
                </div>
                {% if block.settings.show_labels %}
                  <div class="ai-image-compare-labels-{{ ai_gen_id }}">
                    <div class="ai-image-compare-label-{{ ai_gen_id }} ai-image-compare-label-before-{{ ai_gen_id }}">
                      {{ block.settings.before_label }}
                    </div>
                    <div class="ai-image-compare-label-{{ ai_gen_id }} ai-image-compare-label-after-{{ ai_gen_id }}">
                      {{ block.settings.after_label }}
                    </div>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>

  <div class="ai-gallery-nav-{{ ai_gen_id }}">
    <button class="ai-gallery-nav-button-{{ ai_gen_id }} ai-gallery-prev-{{ ai_gen_id }}" aria-label="Previous slide">
      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    <button class="ai-gallery-nav-button-{{ ai_gen_id }} ai-gallery-next-{{ ai_gen_id }}" aria-label="Next slide">
      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 6L15 12L9 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>

  <div class="ai-gallery-pagination-{{ ai_gen_id }}"></div>
</before-after-gallery-{{ ai_gen_id }}>

<script>
  (function() {
    class BeforeAfterGallery{{ai_gen_id}} extends HTMLElement {
      constructor() {
        super();
        this.slidesContainer = this.querySelector('.ai-gallery-slides-{{ ai_gen_id }}');
        this.slides = this.querySelectorAll('.ai-gallery-slide-{{ ai_gen_id }}');
        this.prevButton = this.querySelector('.ai-gallery-prev-{{ ai_gen_id }}');
        this.nextButton = this.querySelector('.ai-gallery-next-{{ ai_gen_id }}');
        this.paginationContainer = this.querySelector('.ai-gallery-pagination-{{ ai_gen_id }}');
        this.currentIndex = 0;
        this.slideCount = this.slides.length;
        this.sliders = [];
        this.handleResize = this.handleResize.bind(this);
      }
      
      connectedCallback() {
        if (this.slideCount === 0) return;
        
        // Initialize pagination dots
        this.createPagination();
        
        // Initialize navigation
        this.prevButton.addEventListener('click', () => this.goToSlide(this.currentIndex - 1));
        this.nextButton.addEventListener('click', () => this.goToSlide(this.currentIndex + 1));
        
        // Initialize comparison sliders
        this.slides.forEach((slide, index) => {
          const container = slide.querySelector('.ai-image-compare-container-{{ ai_gen_id }}');
          const slider = slide.querySelector('.ai-image-compare-slider-{{ ai_gen_id }}');
          const afterImage = slide.querySelector('.ai-image-compare-after-{{ ai_gen_id }}');
          
          if (container && slider && afterImage) {
            const comparisonSlider = new ImageComparison{{ai_gen_id}}(container, slider, afterImage);
            this.sliders.push(comparisonSlider);}
        });
        
        // Set initial slide
        this.goToSlide(0);
        
        // Handle window resize
        window.addEventListener('resize', this.handleResize);
      }
      
      disconnectedCallback() {
        window.removeEventListener('resize', this.handleResize);this.sliders.forEach(slider => slider.destroy());
      }
      
      handleResize() {
        this.goToSlide(this.currentIndex, false);
      }
      
      createPagination() {
        for (let i = 0; i < this.slideCount; i++) {
          const dot = document.createElement('div');
          dot.classList.add('ai-gallery-dot-{{ ai_gen_id }}');
          dot.setAttribute('data-index', i);
          dot.addEventListener('click', () => this.goToSlide(i));
          this.paginationContainer.appendChild(dot);
        }
      }
      
      goToSlide(index, animate = true) {
        // Handle circular navigation
        if (index < 0) index = this.slideCount - 1;
        if (index >= this.slideCount) index = 0;
        
        // Update current index
        this.currentIndex = index;
        
        // Update slides position
        if (!animate) {
          this.slidesContainer.style.transition = 'none';} else {
          this.slidesContainer.style.transition = 'transform 0.5s ease';
        }
        
        this.slidesContainer.style.transform = `translateX(-${index * 100}%)`;
        
        if (!animate) {
          // Force reflow
          this.slidesContainer.offsetHeight;
          this.slidesContainer.style.transition = 'transform 0.5s ease';
        }
        
        // Update pagination dots
        const dots = this.paginationContainer.querySelectorAll('.ai-gallery-dot-{{ ai_gen_id }}');
        dots.forEach((dot, i) => {
          if (i === index) {
            dot.classList.add('active');
          } else {
            dot.classList.remove('active');
          }
        });
      }
    }
    
    class ImageComparison{{ai_gen_id}} {
      constructor(container, slider, afterImage) {
        this.container = container;
        this.slider = slider;
        this.afterImage = afterImage;
        this.isDragging = false;
        this.startDrag = this.startDrag.bind(this);
        this.stopDrag = this.stopDrag.bind(this);
        this.drag = this.drag.bind(this);
        this.slider.addEventListener('mousedown', this.startDrag);
        this.slider.addEventListener('touchstart', this.startDrag, { passive: true });
        
        window.addEventListener('mouseup', this.stopDrag);
        window.addEventListener('touchend', this.stopDrag);
        
        window.addEventListener('mousemove', this.drag);
        window.addEventListener('touchmove', this.drag, { passive: false });
        
        // Set initial position
        this.setSliderPosition(50);
      }
      
      destroy() {
        this.slider.removeEventListener('mousedown', this.startDrag);
        this.slider.removeEventListener('touchstart', this.startDrag);
        
        window.removeEventListener('mouseup', this.stopDrag);
        window.removeEventListener('touchend', this.stopDrag);
        
        window.removeEventListener('mousemove', this.drag);
        window.removeEventListener('touchmove', this.drag);
      }
      
      startDrag(e) {
        this.isDragging = true;
      }
      
      stopDrag() {
        this.isDragging = false;
      }
      
      drag(e) {
        if (!this.isDragging) return;
        
        let clientX;
        if (e.type === 'touchmove') {
          clientX = e.touches[0].clientX;
          e.preventDefault();
        } else {
          clientX = e.clientX;
        }
        
        const containerRect = this.container.getBoundingClientRect();
        const containerWidth = containerRect.width;
        
        let position = ((clientX - containerRect.left) / containerWidth) * 100;
        
        // Constrain position between 0 and 100
        position = Math.max(0, Math.min(position, 100));
        
        this.setSliderPosition(position);
      }
      
      setSliderPosition(position) {
        const sliderPosition = `${position}%`;
        this.slider.style.left = sliderPosition;
        this.afterImage.style.width = `${100 - position}%`;
      }
    }
    
    customElements.define('before-after-gallery-{{ ai_gen_id }}', BeforeAfterGallery{{ai_gen_id}});
  })();
</script>

{% schema %}
{
  "name": "Before After Gallery",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Gallery Settings"
    },
    {
      "type": "text",
      "id": "gallery_title",
      "label": "Gallery title",
      "default": "Before & After Results"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "title_font_size",
      "min": 16,
      "max": 36,
      "step": 1,
      "unit": "px",
      "label": "Title font size",
      "default": 24
    },
    {
      "type": "color",
      "id": "nav_button_color",
      "label": "Navigation button color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "nav_button_text_color",
      "label": "Navigation button icon color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "nav_button_hover_color",
      "label": "Navigation button hover color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "dot_color",
      "label": "Pagination dot color",
      "default": "#cccccc"
    },
    {
      "type": "color",
      "id": "dot_active_color",
      "label": "Active pagination dot color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Slide1"
    },
    {
      "type": "text",
      "id": "slide_title_1",
      "label": "Slide 1 title",
      "default": "Result 1"
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image 1"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image 1"
    },
    {
      "type": "header",
      "content": "Slide 2"
    },
    {
      "type": "text",
      "id": "slide_title_2",
      "label": "Slide 2 title",
      "default": "Result 2"
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image 2"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image 2"
    },
    {
      "type": "header",
      "content": "Slide 3"
    },
    {
      "type": "text",
      "id": "slide_title_3",
      "label": "Slide 3 title",
      "default": "Result 3"
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image 3"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image 3"
    },
    {
      "type": "header",
      "content": "Slide 4"
    },
    {
      "type": "text",
      "id": "slide_title_4",
      "label": "Slide 4 title",
      "default": "Result 4"
    },
    {
      "type": "image_picker",
      "id": "before_image_4",
      "label": "Before image 4"
    },
    {
      "type": "image_picker",
      "id": "after_image_4",
      "label": "After image 4"
    },
    {
      "type": "header",
      "content": "Slide 5"
    },
    {
      "type": "text",
      "id": "slide_title_5",
      "label": "Slide 5 title",
      "default": "Result 5"
    },
    {
      "type": "image_picker",
      "id": "before_image_5",
      "label": "Before image 5"
    },
    {
      "type": "image_picker",
      "id": "after_image_5",
      "label": "After image 5"
    },
    {
      "type": "header",
      "content": "Slide 6"
    },
    {
      "type": "text",
      "id": "slide_title_6",
      "label": "Slide 6 title",
      "default": "Result 6"
    },
    {
      "type": "image_picker",
      "id": "before_image_6",
      "label": "Before image 6"
    },
    {
      "type": "image_picker",
      "id": "after_image_6",
      "label": "After image 6"
    },
    {
      "type": "header",
      "content": "Comparison Settings"
    },
    {
      "type": "range",
      "id": "aspect_ratio",
      "min": 40,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Aspect ratio",
      "default": 75,
      "info": "Sets the height relative to width. Use 100% for square, 75% for 4:3, 56.25% for 16:9"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show before/after labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "color",
      "id": "label_bg_color",
      "label": "Label background color",
      "default": "rgba(0, 0, 0, 0.7)"
    },
    {
      "type": "color",
      "id": "label_text_color",
      "label": "Label text color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "label_font_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Label font size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Slider Style"
    },
    {
      "type": "range",
      "id": "slider_width",
      "min": 1,
      "max": 10,
      "step": 1,
      "unit": "px",
      "label": "Slider width",
      "default": 2
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "handle_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Handle size",
      "default": 40
    },
    {
      "type": "color",
      "id": "handle_color",
      "label": "Handle color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "handle_border_color",
      "label": "Handle border color",
      "default": "#dddddd"
    },
    {
      "type": "range",
      "id": "handle_border_width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Handle border width",
      "default": 1
    },
    {
      "type": "color",
      "id": "handle_arrow_color",
      "label": "Handle arrow color",
      "default": "#333333"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Before After Gallery"
    }
  ]
}
{% endschema %}