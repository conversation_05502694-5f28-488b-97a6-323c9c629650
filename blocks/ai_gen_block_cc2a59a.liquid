{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px 0;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-container-{{ ai_gen_id }} {
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .ai-sitemap-columns-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: 20px;
  }

  .ai-sitemap-column-{{ ai_gen_id }} h3 {
    font-size: {{ block.settings.heading_size }}px;
    margin-top: 0;
    margin-bottom: 15px;
    color: {{ block.settings.heading_color }};
    font-weight: 600;
  }

  .ai-sitemap-links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-links-{{ ai_gen_id }} li {
    margin-bottom: 8px;
  }

  .ai-sitemap-links-{{ ai_gen_id }} a {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .ai-sitemap-links-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
    {% if block.settings.show_underline_on_hover %}
      text-decoration: underline;
    {% endif %}
  }

  .ai-sitemap-copyright-{{ ai_gen_id }} {
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid {{ block.settings.divider_color }};
    text-align: center;
    font-size: {{ block.settings.copyright_size }}px;
    color: {{ block.settings.copyright_color }};
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-columns-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-container-{{ ai_gen_id }}">
    <div class="ai-sitemap-columns-{{ ai_gen_id }}">
      {% if block.settings.menu_1 != blank %}
        <div class="ai-sitemap-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_1 }}</h3>
          <ul class="ai-sitemap-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_1].links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_2 != blank %}
        <div class="ai-sitemap-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_2 }}</h3>
          <ul class="ai-sitemap-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_2].links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_3 != blank %}
        <div class="ai-sitemap-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_3 }}</h3>
          <ul class="ai-sitemap-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_3].links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_4 != blank %}
        <div class="ai-sitemap-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_4 }}</h3>
          <ul class="ai-sitemap-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_4].links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    {% if block.settings.show_copyright %}
      <div class="ai-sitemap-copyright-{{ ai_gen_id }}">
        {{ block.settings.copyright_text }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 800,
      "max": 1600,
      "step": 50,
      "unit": "px",
      "label": "Maximum width",
      "default": 1200
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Vertical padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 4,
      "step": 1,
      "label": "Number of columns on desktop",
      "default": 4
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Number of columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "header",
      "content": "Menu Columns"
    },
    {
      "type": "text",
      "id": "heading_1",
      "label": "Heading 1",
      "default": "Shop"
    },
    {
      "type": "link_list",
      "id": "menu_1",
      "label": "Menu 1",
      "default": "main-menu"
    },
    {
      "type": "text",
      "id": "heading_2",
      "label": "Heading 2",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "menu_2",
      "label": "Menu 2",
      "default": "footer"
    },
    {
      "type": "text",
      "id": "heading_3",
      "label": "Heading 3",
      "default": "Customer Service"
    },
    {
      "type": "link_list",
      "id": "menu_3",
      "label": "Menu 3"
    },
    {
      "type": "text",
      "id": "heading_4",
      "label": "Heading 4",
      "default": "About Us"
    },
    {
      "type": "link_list",
      "id": "menu_4",
      "label": "Menu 4"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 14,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Heading size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#505050"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#e0e0e0"
    },
    {
      "type": "checkbox",
      "id": "show_underline_on_hover",
      "label": "Show underline on link hover",
      "default": true
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "checkbox",
      "id": "show_copyright",
      "label": "Show copyright",
      "default": true
    },
    {
      "type": "textarea",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "© 2023 Your Store. All rights reserved."
    },
    {
      "type": "range",
      "id": "copyright_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Copyright text size",
      "default": 12
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright text color",
      "default": "#707070"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}