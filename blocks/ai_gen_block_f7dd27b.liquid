{% doc %}
  @prompt
    Create a before/after comparison section with 3 horizontal cards side by side. Each card should have a before/after image slider that allows users to drag to reveal the transformation. The layout should be compact and beautiful, with proper image aspect ratio preservation to prevent distortion or stretching. Include customizable settings for uploading before and after images for each of the 3 cards, with smooth slider functionality and responsive design.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .before-after-container-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    gap: {{ block.settings.gap }}px;
    justify-content: center;
    margin: 0 auto;
    max-width: 100%;
    padding: {{ block.settings.padding }}px;
  }

  .before-after-card-{{ ai_gen_id }} {
    flex: 1;
    min-width: 280px;
    max-width: 100%;
    margin-bottom: 20px;
    border-radius: {{ block.settings.border_radius }}px;
    overflow: hidden;
    box-shadow: {{ block.settings.enable_shadow | default: false | ternary: '0 4px 10px rgba(0, 0, 0, 0.1)', 'none' }};
    background-color: {{ block.settings.card_background_color }};
  }

  .before-after-title-{{ ai_gen_id }} {
    padding: 15px;
    text-align: center;
    font-size: {{ block.settings.title_size }}px;
    font-weight: 600;
    color: {{ block.settings.title_color }};
    margin: 0;
  }

  .before-after-description-{{ ai_gen_id }} {
    padding: 0 15px 15px;
    text-align: center;
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.description_color }};
    margin: 0;
  }

  .before-after-slider-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: {{ block.settings.aspect_ratio }}%;
    overflow: hidden;
  }

  .before-after-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .before-image-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .after-image-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
  }

  .after-image-container-{{ ai_gen_id }} svg,
  .before-image-container-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .slider-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background-color: {{ block.settings.slider_color }};
    cursor: ew-resize;
    transform: translateX(-50%);
    z-index: 2;
  }

  .slider-button-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: {{ block.settings.slider_color }};
    border: 3px solid white;
    transform: translate(-50%, -50%);
    cursor: ew-resize;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .slider-button-{{ ai_gen_id }}::before,
  .slider-button-{{ ai_gen_id }}::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 2px;
    background-color: white;
  }

  .slider-button-{{ ai_gen_id }}::before {
    transform: translateX(-4px) rotate(-45deg);
  }

  .slider-button-{{ ai_gen_id }}::after {
    transform: translateX(4px) rotate(45deg);
  }

  .before-label-{{ ai_gen_id }},
  .after-label-{{ ai_gen_id }} {
    position: absolute;
    top: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1;
  }

  .before-label-{{ ai_gen_id }} {
    left: 20px;
  }

  .after-label-{{ ai_gen_id }} {
    right: 20px;
  }

  @media screen and (max-width: 990px) {
    .before-after-card-{{ ai_gen_id }} {
      flex-basis: calc(50% - {{ block.settings.gap }}px);
    }
  }

  @media screen and (max-width: 749px) {
    .before-after-card-{{ ai_gen_id }} {
      flex-basis: 100%;
    }
  }
{% endstyle %}

<before-after-comparison-{{ ai_gen_id }} 
  class="before-after-container-{{ ai_gen_id }}" 
  {{ block.shopify_attributes }}
>
  {% for i in (1..3) %}
    {% liquid
      assign before_image_key = 'before_image_' | append: i
      assign after_image_key = 'after_image_' | append: i
      assign title_key = 'title_' | append: i
      assign description_key = 'description_' | append: i
      
      assign before_image = block.settings[before_image_key]
      assign after_image = block.settings[after_image_key]
      assign title = block.settings[title_key]
      assign description = block.settings[description_key]
    %}

    <div class="before-after-card-{{ ai_gen_id }}">
      {% if title != blank %}
        <h3 class="before-after-title-{{ ai_gen_id }}">{{ title }}</h3>
      {% endif %}
      
      <div class="before-after-slider-container-{{ ai_gen_id }}" data-index="{{ i }}">
        <div class="before-image-container-{{ ai_gen_id }}">
          {% if before_image %}
            <img 
              src="{{ before_image | image_url: width: 1000 }}" 
              alt="{{ before_image.alt | default: 'Before image' }}" 
              loading="lazy" 
              class="before-after-image-{{ ai_gen_id }}"
            >
          {% else %}
            {{ 'image' | placeholder_svg_tag }}
          {% endif %}
        </div>
        
        <div class="after-image-container-{{ ai_gen_id }}">
          {% if after_image %}
            <img 
              src="{{ after_image | image_url: width: 1000 }}" 
              alt="{{ after_image.alt | default: 'After image' }}" 
              loading="lazy" 
              class="before-after-image-{{ ai_gen_id }}"
            >
          {% else %}
            {{ 'image' | placeholder_svg_tag }}
          {% endif %}
        </div>
        
        <div class="slider-handle-{{ ai_gen_id }}">
          <div class="slider-button-{{ ai_gen_id }}"></div>
        </div>
        
        {% if block.settings.show_labels %}
          <div class="before-label-{{ ai_gen_id }}">{{ block.settings.before_label }}</div>
          <div class="after-label-{{ ai_gen_id }}">{{ block.settings.after_label }}</div>
        {% endif %}
      </div>
      
      {% if description != blank %}
        <p class="before-after-description-{{ ai_gen_id }}">{{ description }}</p>
      {% endif %}
    </div>
  {% endfor %}
</before-after-comparison-{{ ai_gen_id }}>

<script>
  (function() {
    class BeforeAfterComparison{{ai_gen_id}} extends HTMLElement {
      constructor() {
        super();
        this.sliders = this.querySelectorAll('.before-after-slider-container-{{ ai_gen_id }}');
        this.isDragging = false;
        this.currentSlider = null;
      }

      connectedCallback() {
        this.setupEventListeners();
      }

      setupEventListeners() {
        this.sliders.forEach(slider => {
          const handle = slider.querySelector('.slider-handle-{{ ai_gen_id }}');
          const afterContainer = slider.querySelector('.after-image-container-{{ ai_gen_id }}');
          
          // Mouse events
          handle.addEventListener('mousedown', (e) => this.startDrag(e, slider, afterContainer));
          slider.addEventListener('mousemove', (e) => this.drag(e, slider, afterContainer));
          window.addEventListener('mouseup', () => this.stopDrag());
          
          // Touch events
          handle.addEventListener('touchstart', (e) => this.startDrag(e, slider, afterContainer));
          slider.addEventListener('touchmove', (e) => this.drag(e, slider, afterContainer));
          window.addEventListener('touchend', () => this.stopDrag());
          
          // Click anywhere on slider to move handle
          slider.addEventListener('click', (e) => this.moveHandleOnClick(e, slider, afterContainer));
        });
      }

      startDrag(e, slider, afterContainer) {
        e.preventDefault();
        this.isDragging = true;
        this.currentSlider = { slider, afterContainer };
      }

      drag(e, slider, afterContainer) {
        if (!this.isDragging || this.currentSlider.slider !== slider) return;
        
        let clientX;
        if (e.type === 'touchmove') {
          clientX = e.touches[0].clientX;
        } else {
          clientX = e.clientX;
        }
        
        const rect = slider.getBoundingClientRect();
        const x = clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
        
        this.updateSliderPosition(afterContainer, percentage);
      }

      stopDrag() {
        this.isDragging = false;
      }

      moveHandleOnClick(e, slider, afterContainer) {
        const rect = slider.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = (x / rect.width) * 100;
        
        this.updateSliderPosition(afterContainer, percentage);
      }

      updateSliderPosition(afterContainer, percentage) {
        afterContainer.style.width = `${percentage}%`;
        const handle = afterContainer.parentNode.querySelector('.slider-handle-{{ ai_gen_id }}');
        handle.style.left = `${percentage}%`;
      }
    }

    customElements.define('before-after-comparison-{{ ai_gen_id }}', BeforeAfterComparison{{ai_gen_id}});
  })();
</script>

{% schema %}
{
  "name": "Before After Comparison",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Gap between cards",
      "default": 20
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "aspect_ratio",
      "min": 50,
      "max": 150,
      "step": 5,
      "unit": "%",
      "label": "Image aspect ratio",
      "default": 75
    },
    {
      "type": "checkbox",
      "id": "enable_shadow",
      "label": "Enable shadow",
      "default": true
    },
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider color",
      "default": "#4A90E2"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show before/after labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "card_background_color",
      "label": "Card background color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 14,
      "max": 32,
      "step": 1,
      "unit": "px",
      "label": "Title font size",
      "default": 20
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Description font size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Card 1"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title",
      "default": "Transformation 1"
    },
    {
      "type": "textarea",
      "id": "description_1",
      "label": "Description",
      "default": "See the amazing transformation with our product."
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Card 2"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title",
      "default": "Transformation 2"
    },
    {
      "type": "textarea",
      "id": "description_2",
      "label": "Description",
      "default": "Another impressive result with our solution."
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "Card 3"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title",
      "default": "Transformation 3"
    },
    {
      "type": "textarea",
      "id": "description_3",
      "label": "Description",
      "default": "Check out this incredible change."
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image"
    }
  ],
  "presets": [
    {
      "name": "Before After Comparison"
    }
  ]
}
{% endschema %}