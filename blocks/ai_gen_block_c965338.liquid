{% doc %}
  @prompt
    Create an elegant wedding section with image on the right and text on the left. Add beautiful CSS floral decorations, flower petals, and botanical elements as background decorations. Include floating petals animation, delicate flower borders, and subtle floral patterns. The background should be soft pink with visible decorative flowers and petals scattered around. Include all text and image settings plus options to control the floral decorations intensity and petal animations.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .wedding-block-{{ ai_gen_id }} {
    position: relative;
    padding: {{ block.settings.section_padding }}px;
    background-color: {{ block.settings.background_color }};
    overflow: hidden;
  }

  .wedding-block__container-{{ ai_gen_id }} {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 40px;
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
  }

  .wedding-block__text-column-{{ ai_gen_id }} {
    flex: 1;
    padding: 30px;
    position: relative;
  }

  .wedding-block__image-column-{{ ai_gen_id }} {
    flex: 1;
    position: relative;
  }

  .wedding-block__image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 120%;
    overflow: hidden;
    border-radius: {{ block.settings.image_border_radius }}px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .wedding-block__image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .wedding-block__image-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f2f4;
  }

  .wedding-block__image-placeholder-{{ ai_gen_id }} svg {
    width: 50%;
    height: 50%;
    opacity: 0.5;
  }

  .wedding-block__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    margin-bottom: 20px;
    color: {{ block.settings.heading_color }};
    font-family: {{ block.settings.heading_font.family }}, serif;
    font-weight: {{ block.settings.heading_font.weight }};
    line-height: 1.2;
    position: relative;
  }

  .wedding-block__subtitle-{{ ai_gen_id }} {
    font-size: {{ block.settings.subtitle_size }}px;
    margin-bottom: 15px;
    color: {{ block.settings.subtitle_color }};
    font-family: {{ block.settings.subtitle_font.family }}, serif;
    font-weight: {{ block.settings.subtitle_font.weight }};
    font-style: italic;
  }

  .wedding-block__description-{{ ai_gen_id }} {
    font-size: {{ block.settings.text_size }}px;
    line-height: 1.6;
    margin-bottom: 25px;
    color: {{ block.settings.text_color }};
  }

  .wedding-block__button-{{ ai_gen_id }} {
    display: inline-block;
    padding: 12px 30px;
    background-color: {{ block.settings.button_color }};
    color: {{ block.settings.button_text_color }};
    text-decoration: none;
    border-radius: 30px;
    font-size: 16px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
  }

  .wedding-block__button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_color }};
    color: {{ block.settings.button_hover_text_color }};
  }

  /* Floral decorations */
  .wedding-block__decoration-{{ ai_gen_id }} {
    position: absolute;
    z-index: 1;
    opacity: {{ block.settings.decoration_opacity | divided_by: 100.0 }};
  }

  .wedding-block__corner-top-left-{{ ai_gen_id }} {
    top: 0;
    left: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle at top left, transparent 70%, {{ block.settings.background_color }}),
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200' fill='none'%3E%3Cpath d='M40 100C40 80 60 60 80 60C100 60 120 80 120 100C120 120 100 140 80 140C60 140 40 120 40 100Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M80 60C80 40 100 20 120 20C140 20 160 40 160 60C160 80 140 100 120 100C100 100 80 80 80 60Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M120 100C120 80 140 60 160 60C180 60 200 80 200 100C200 120 180 140 160 140C140 140 120 120 120 100Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M80 140C80 120 100 100 120 100C140 100 160 120 160 140C160 160 140 180 120 180C100 180 80 160 80 140Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M0 100C0 80 20 60 40 60C60 60 80 80 80 100C80 120 60 140 40 140C20 140 0 120 0 100Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Ccircle cx='100' cy='100' r='10' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' /%3E%3C/svg%3E");
  }

  .wedding-block__corner-bottom-right-{{ ai_gen_id }} {
    bottom: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle at bottom right, transparent 70%, {{ block.settings.background_color }}),
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200' fill='none'%3E%3Cpath d='M40 100C40 80 60 60 80 60C100 60 120 80 120 100C120 120 100 140 80 140C60 140 40 120 40 100Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M80 60C80 40 100 20 120 20C140 20 160 40 160 60C160 80 140 100 120 100C100 100 80 80 80 60Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M120 100C120 80 140 60 160 60C180 60 200 80 200 100C200 120 180 140 160 140C140 140 120 120 120 100Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M80 140C80 120 100 100 120 100C140 100 160 120 160 140C160 160 140 180 120 180C100 180 80 160 80 140Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Cpath d='M0 100C0 80 20 60 40 60C60 60 80 80 80 100C80 120 60 140 40 140C20 140 0 120 0 100Z' fill='none' stroke='{{ block.settings.flower_color | replace: '#', '%23' }}' stroke-width='1'/%3E%3Ccircle cx='100' cy='100' r='10' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' /%3E%3C/svg%3E");
  }

  .wedding-block__border-{{ ai_gen_id }} {
    position: absolute;
    left: 50%;
    top: 0;
    width: calc(100% - 100px);
    height: 100%;
    transform: translateX(-50%);
    pointer-events: none;
    z-index: 1;
    opacity: {{ block.settings.decoration_opacity | divided_by: 100.0 }};
    background-image: 
      url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'%3E%3Cpath d='M10 5C10 7.76142 7.76142 10 5 10C2.23858 10 0 7.76142 0 5C0 2.23858 2.23858 0 5 0C7.76142 0 10 2.23858 10 5Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3Cpath d='M20 5C20 7.76142 17.7614 10 15 10C12.2386 10 10 7.76142 10 5C10 2.23858 12.2386 0 15 0C17.7614 0 20 2.23858 20 5Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3Cpath d='M10 15C10 17.7614 7.76142 20 5 20C2.23858 20 0 17.7614 0 15C0 12.2386 2.23858 10 5 10C7.76142 10 10 12.2386 10 15Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3Cpath d='M20 15C20 17.7614 17.7614 20 15 20C12.2386 20 10 17.7614 10 15C10 12.2386 12.2386 10 15 10C17.7614 10 20 12.2386 20 15Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3C/svg%3E"),
      url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'%3E%3Cpath d='M10 5C10 7.76142 7.76142 10 5 10C2.23858 10 0 7.76142 0 5C0 2.23858 2.23858 0 5 0C7.76142 0 10 2.23858 10 5Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3Cpath d='M20 5C20 7.76142 17.7614 10 15 10C12.2386 10 10 7.76142 10 5C10 2.23858 12.2386 0 15 0C17.7614 0 20 2.23858 20 5Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3Cpath d='M10 15C10 17.7614 7.76142 20 5 20C2.23858 20 0 17.7614 0 15C0 12.2386 2.23858 10 5 10C7.76142 10 10 12.2386 10 15Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3Cpath d='M20 15C20 17.7614 17.7614 20 15 20C12.2386 20 10 17.7614 10 15C10 12.2386 12.2386 10 15 10C17.7614 10 20 12.2386 20 15Z' fill='{{ block.settings.flower_color | replace: '#', '%23' }}' fill-opacity='0.1'/%3E%3C/svg%3E");
    background-position: left top, right bottom;
    background-repeat: repeat-y;
    background-size: 20px;
  }

  /* Floating petals */
  .wedding-block__petal-{{ ai_gen_id }} {
    position: absolute;
    width: 15px;
    height: 15px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='15' height='15' viewBox='0 0 15 15' fill='none'%3E%3Cpath d='M7.5 0C7.5 4.14214 4.14214 7.5 0 7.5C4.14214 7.5 7.5 10.8579 7.5 15C7.5 10.8579 10.8579 7.5 15 7.5C10.8579 7.5 7.5 4.14214 7.5 0Z' fill='{{ block.settings.petal_color | replace: '#', '%23' }}'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.6;
    pointer-events: none;
    z-index: 1;
  }

  {% if block.settings.enable_petal_animation %}
    @keyframes float-{{ ai_gen_id }} {
      0% {
        transform: translateY(0) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
      100% {
        transform: translateY(-40px) rotate(360deg);
      }
    }

    .wedding-block__petal-{{ ai_gen_id }} {
      animation: float-{{ ai_gen_id }} 10s infinite linear;
    }
    
    .wedding-block__petal-{{ ai_gen_id }}:nth-child(2n) {
      animation-duration: 12s;
    }
    
    .wedding-block__petal-{{ ai_gen_id }}:nth-child(3n) {
      animation-duration: 15s;
    }
    
    .wedding-block__petal-{{ ai_gen_id }}:nth-child(4n) {
      animation-duration: 18s;
    }
  {% endif %}

  @media screen and (max-width: 749px) {
    .wedding-block__container-{{ ai_gen_id }} {
      flex-direction: column-reverse;
      gap: 20px;
    }

    .wedding-block__text-column-{{ ai_gen_id }},
    .wedding-block__image-column-{{ ai_gen_id }} {
      width: 100%;
    }

    .wedding-block__image-wrapper-{{ ai_gen_id }} {
      padding-bottom: 100%;
    }

    .wedding-block__title-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | minus: 10 }}px;
    }

    .wedding-block__subtitle-{{ ai_gen_id }} {
      font-size: {{ block.settings.subtitle_size | minus: 4 }}px;
    }

    .wedding-block__description-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | minus: 2 }}px;
    }
  }
{% endstyle %}

<wedding-block-{{ ai_gen_id }} 
  class="wedding-block-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="wedding-block__decoration-{{ ai_gen_id }} wedding-block__corner-top-left-{{ ai_gen_id }}"></div>
  <div class="wedding-block__decoration-{{ ai_gen_id }} wedding-block__corner-bottom-right-{{ ai_gen_id }}"></div>
  <div class="wedding-block__border-{{ ai_gen_id }}"></div>
  
  {% if block.settings.enable_petal_animation %}
    {% for i in (1..block.settings.petal_count) %}
      {% assign random_top = forloop.index | times: 17 | modulo: 100 %}
      {% assign random_left = forloop.index | times: 23 | modulo: 100 %}
      {% assign random_delay = forloop.index | divided_by: 10.0 %}
      
      <div class="wedding-block__petal-{{ ai_gen_id }}" style="top: {{ random_top }}%; left: {{ random_left }}%; animation-delay: {{ random_delay }}s;"></div>
    {% endfor %}
  {% endif %}
  
  <div class="wedding-block__container-{{ ai_gen_id }}">
    <div class="wedding-block__text-column-{{ ai_gen_id }}">
      {% if block.settings.subtitle != blank %}
        <div class="wedding-block__subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</div>
      {% endif %}
      
      {% if block.settings.title != blank %}
        <h2 class="wedding-block__title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
      {% endif %}
      
      {% if block.settings.description != blank %}
        <div class="wedding-block__description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
      {% endif %}
      
      {% if block.settings.button_text != blank and block.settings.button_link != blank %}
        <a href="{{ block.settings.button_link }}" class="wedding-block__button-{{ ai_gen_id }}">
          {{ block.settings.button_text }}
        </a>
      {% endif %}
    </div>
    
    <div class="wedding-block__image-column-{{ ai_gen_id }}">
      <div class="wedding-block__image-wrapper-{{ ai_gen_id }}">
        {% if block.settings.image %}
          <img
            src="{{ block.settings.image | image_url: width: 1000 }}"
            alt="{{ block.settings.image.alt | escape }}"
            loading="lazy"
            class="wedding-block__image-{{ ai_gen_id }}"
            width="{{ block.settings.image.width }}"
            height="{{ block.settings.image.height }}"
          >
        {% else %}
          <div class="wedding-block__image-placeholder-{{ ai_gen_id }}">
            {{ 'image' | placeholder_svg_tag }}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</wedding-block-{{ ai_gen_id }}>

<script>
  (function() {
    class WeddingBlock{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }
      
      connectedCallback() {
        // Future interactive features can be added here
      }
    }
    
    customElements.define('wedding-block-{{ ai_gen_id }}', WeddingBlock{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Wedding Section",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Celebrate Our Special Day"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "You're Invited"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Join us as we celebrate our wedding day. We're excited to share this special moment with our friends and family. The ceremony will be followed by dinner and dancing.</p>"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "RSVP Now"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#fff5f7"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle color",
      "default": "#d4a6b3"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "#d4a6b3"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_hover_color",
      "label": "Button hover color",
      "default": "#c08c99"
    },
    {
      "type": "color",
      "id": "button_hover_text_color",
      "label": "Button hover text color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "font_picker",
      "id": "heading_font",
      "label": "Heading font",
      "default": "serif"
    },
    {
      "type": "font_picker",
      "id": "subtitle_font",
      "label": "Subtitle font",
      "default": "serif"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 36
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 18
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Floral Decorations"
    },
    {
      "type": "color",
      "id": "flower_color",
      "label": "Flower color",
      "default": "#d4a6b3"
    },
    {
      "type": "range",
      "id": "decoration_opacity",
      "min": 10,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Decoration opacity",
      "default": 50
    },
    {
      "type": "checkbox",
      "id": "enable_petal_animation",
      "label": "Enable floating petals",
      "default": true
    },
    {
      "type": "color",
      "id": "petal_color",
      "label": "Petal color",
      "default": "#f2c4d0"
    },
    {
      "type": "range",
      "id": "petal_count",
      "min": 0,
      "max": 20,
      "step": 1,
      "label": "Number of petals",
      "default": 10
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 20,
      "max": 100,
      "step": 10,
      "unit": "px",
      "label": "Section padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    }
  ],
  "presets": [
    {
      "name": "Wedding Section"
    }
  ]
}
{% endschema %}