{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer-container-{{ ai_gen_id }} {
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
  }

  .ai-sitemap-footer-grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.gap }}px;
  }

  .ai-sitemap-footer-column-{{ ai_gen_id }} h3 {
    font-size: {{ block.settings.heading_size }}px;
    margin-top: 0;
    margin-bottom: {{ block.settings.gap | divided_by: 2 }}px;
    color: {{ block.settings.heading_color }};
    font-weight: 600;
  }

  .ai-sitemap-footer-links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer-links-{{ ai_gen_id }} li {
    margin-bottom: {{ block.settings.link_spacing }}px;
  }

  .ai-sitemap-footer-links-{{ ai_gen_id }} a {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
    display: inline-block;
  }

  .ai-sitemap-footer-links-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-sitemap-footer-copyright-{{ ai_gen_id }} {
    text-align: center;
    margin-top: {{ block.settings.gap }}px;
    padding-top: {{ block.settings.gap | divided_by: 2 }}px;
    border-top: 1px solid {{ block.settings.divider_color }};
    font-size: {{ block.settings.font_size | times: 0.9 }}px;
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer-grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
    
    .ai-sitemap-footer-{{ ai_gen_id }} {
      padding: {{ block.settings.padding | times: 0.75 }}px;
    }
    
    .ai-sitemap-footer-column-{{ ai_gen_id }} h3 {
      font-size: {{ block.settings.heading_size | times: 0.9 }}px;
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer-container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer-grid-{{ ai_gen_id }}">
      {% if block.settings.menu_1 != blank %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_1 }}</h3>
          <ul class="ai-sitemap-footer-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_1].links %}
              <li>
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_2 != blank %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_2 }}</h3>
          <ul class="ai-sitemap-footer-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_2].links %}
              <li>
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_3 != blank %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_3 }}</h3>
          <ul class="ai-sitemap-footer-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_3].links %}
              <li>
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.menu_4 != blank %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3>{{ block.settings.heading_4 }}</h3>
          <ul class="ai-sitemap-footer-links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_4].links %}
              <li>
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    {% if block.settings.show_copyright %}
      <div class="ai-sitemap-footer-copyright-{{ ai_gen_id }}">
        {% if block.settings.copyright_text != blank %}
          {{ block.settings.copyright_text }}
        {% else %}
          &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}
        {% endif %}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 4,
      "label": "Columns on desktop"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 800,
      "max": 1600,
      "step": 100,
      "default": 1200,
      "unit": "px",
      "label": "Maximum width"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 50,
      "step": 5,
      "default": 20,
      "unit": "px",
      "label": "Padding"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 40,
      "step": 5,
      "default": 20,
      "unit": "px",
      "label": "Gap between columns"
    },
    {
      "type": "range",
      "id": "link_spacing",
      "min": 4,
      "max": 16,
      "step": 2,
      "default": 8,
      "unit": "px",
      "label": "Spacing between links"
    },
    {
      "type": "header",
      "content": "First Column"
    },
    {
      "type": "text",
      "id": "heading_1",
      "label": "Heading",
      "default": "Shop"
    },
    {
      "type": "link_list",
      "id": "menu_1",
      "label": "Menu",
      "default": "main-menu"
    },
    {
      "type": "header",
      "content": "Second Column"
    },
    {
      "type": "text",
      "id": "heading_2",
      "label": "Heading",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "menu_2",
      "label": "Menu",
      "default": "footer"
    },
    {
      "type": "header",
      "content": "Third Column"
    },
    {
      "type": "text",
      "id": "heading_3",
      "label": "Heading",
      "default": "Customer Service"
    },
    {
      "type": "link_list",
      "id": "menu_3",
      "label": "Menu"
    },
    {
      "type": "header",
      "content": "Fourth Column"
    },
    {
      "type": "text",
      "id": "heading_4",
      "label": "Heading",
      "default": "Policies"
    },
    {
      "type": "link_list",
      "id": "menu_4",
      "label": "Menu"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "checkbox",
      "id": "show_copyright",
      "label": "Show copyright",
      "default": true
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Custom copyright text"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 16,
      "step": 1,
      "default": 14,
      "unit": "px",
      "label": "Font size"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 14,
      "max": 24,
      "step": 1,
      "default": 16,
      "unit": "px",
      "label": "Heading size"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}