{% doc %}
  @prompt
    Create a before/after comparison section with 3 horizontal cards side by side. Each card should have a before/after image slider that allows users to drag to reveal the transformation. The layout should be compact and beautiful, with proper image aspect ratio preservation to prevent distortion or stretching. Include customizable settings for uploading before and after images for each of the 3 cards, with smooth slider functionality and responsive design.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-before-after-{{ ai_gen_id }} {
    width: 100%;
    padding: {{ block.settings.section_padding }}px 0;
  }

  .ai-before-after-container-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    gap: {{ block.settings.gap }}px;
    justify-content: center;
    max-width: 100%;
  }

  .ai-before-after-card-{{ ai_gen_id }} {
    flex: 1;
    min-width: 250px;
    max-width: calc((100% - {{ block.settings.gap | times: 2 }}px) / 3);
    margin-bottom: 20px;
    border-radius: {{ block.settings.card_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: {{ block.settings.card_background }};
  }

  .ai-before-after-slider-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: {{ block.settings.image_aspect_ratio }}%;
    overflow: hidden;
  }

  .ai-before-after-slider-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .ai-before-image-{{ ai_gen_id }},
  .ai-after-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-before-image-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
  }

  .ai-after-image-{{ ai_gen_id }} {
    display: block;
  }

  .ai-slider-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background-color: {{ block.settings.slider_color }};
    transform: translateX(-50%);
    cursor: ew-resize;
    z-index: 10;
  }

  .ai-slider-handle-{{ ai_gen_id }}::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    background-color: {{ block.settings.slider_color }};
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0.3);
  }

  .ai-slider-handle-{{ ai_gen_id }}::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    border-left: 2px solid white;
    border-bottom: 2px solid white;
    transform: translate(-75%, -50%) rotate(45deg);
    z-index: 11;
  }

  .ai-slider-handle-{{ ai_gen_id }}::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    border-right: 2px solid white;
    border-top: 2px solid white;
    transform: translate(-25%, -50%) rotate(45deg);
    z-index: 11;
  }

  .ai-card-content-{{ ai_gen_id }} {
    padding: 20px;
    text-align: center;
  }

  .ai-card-title-{{ ai_gen_id }} {
    margin: 0 0 10px;
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.title_color }};
  }

  .ai-card-description-{{ ai_gen_id }} {
    margin: 0;
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.description_color }};
  }

  .ai-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .ai-image-placeholder-{{ ai_gen_id }} svg {
    width: 50%;
    height: 50%;
    opacity: 0.5;
  }

  .ai-before-label-{{ ai_gen_id }},
  .ai-after-label-{{ ai_gen_id }} {
    position: absolute;
    top: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    z-index: 5;
  }

  .ai-before-label-{{ ai_gen_id }} {
    left: 10px;
  }

  .ai-after-label-{{ ai_gen_id }} {
    right: 10px;
  }

  @media screen and (max-width: 990px) {
    .ai-before-after-card-{{ ai_gen_id }} {
      max-width: calc((100% - {{ block.settings.gap }}px) / 2);
    }
  }

  @media screen and (max-width: 749px) {
    .ai-before-after-card-{{ ai_gen_id }} {
      max-width: 100%;
    }
  }
{% endstyle %}

<div class="ai-before-after-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-before-after-container-{{ ai_gen_id }}">
    {% for i in (1..3) %}
      {% liquid
        assign before_image_key = 'before_image_' | append: i
        assign after_image_key = 'after_image_' | append: i
        assign title_key = 'title_' | append: i
        assign description_key = 'description_' | append: i
        
        assign before_image = block.settings[before_image_key]
        assign after_image = block.settings[after_image_key]
        assign title = block.settings[title_key]
        assign description = block.settings[description_key]
      %}

      <div class="ai-before-after-card-{{ ai_gen_id }}">
        <div class="ai-before-after-slider-container-{{ ai_gen_id }}">
          <div class="ai-before-after-slider-{{ ai_gen_id }}" data-slider-index="{{ i }}">
            {% if after_image %}
              <img 
                src="{{ after_image | image_url: width: 800 }}" 
                alt="{{ after_image.alt | escape }}" 
                class="ai-after-image-{{ ai_gen_id }}" 
                loading="lazy"
                width="{{ after_image.width }}"
                height="{{ after_image.height }}"
              >
            {% else %}
              <div class="ai-image-placeholder-{{ ai_gen_id }}">
                {{ 'image' | placeholder_svg_tag }}
              </div>
            {% endif %}

            <div class="ai-before-image-container-{{ ai_gen_id }}">
              {% if before_image %}
                <img 
                  src="{{ before_image | image_url: width: 800 }}" 
                  alt="{{ before_image.alt | escape }}" 
                  class="ai-before-image-{{ ai_gen_id }}" 
                  loading="lazy"
                  width="{{ before_image.width }}"
                  height="{{ before_image.height }}"
                >
              {% else %}
                <div class="ai-image-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>

            <div class="ai-slider-handle-{{ ai_gen_id }}"></div>

            {% if block.settings.show_labels %}
              <div class="ai-before-label-{{ ai_gen_id }}">{{ block.settings.before_label }}</div>
              <div class="ai-after-label-{{ ai_gen_id }}">{{ block.settings.after_label }}</div>
            {% endif %}
          </div>
        </div>

        <div class="ai-card-content-{{ ai_gen_id }}">
          {% if title != blank %}
            <h3 class="ai-card-title-{{ ai_gen_id }}">{{ title }}</h3>
          {% endif %}
          
          {% if description != blank %}
            <p class="ai-card-description-{{ ai_gen_id }}">{{ description }}</p>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  </div>
</div>

<script>
  (function() {
    class BeforeAfterSlider extends HTMLElement {
      constructor() {
        super();
        this.sliders = this.querySelectorAll('.ai-before-after-slider-{{ ai_gen_id }}');
        this.isDragging = false;
        this.currentSlider = null;
        this.initialX = 0;
        this.initialPosition = 0;
      }

      connectedCallback() {
        this.setupEventListeners();
      }

      setupEventListeners() {
        this.sliders.forEach(slider => {
          const handle = slider.querySelector('.ai-slider-handle-{{ ai_gen_id }}');
          const beforeContainer = slider.querySelector('.ai-before-image-container-{{ ai_gen_id }}');
          
          // Mouse events
          handle.addEventListener('mousedown', (e) => this.startDrag(e, slider, beforeContainer));
          document.addEventListener('mousemove', (e) => this.drag(e, beforeContainer));
          document.addEventListener('mouseup', () => this.endDrag());
          
          // Touch events
          handle.addEventListener('touchstart', (e) => this.startDrag(e, slider, beforeContainer));
          document.addEventListener('touchmove', (e) => this.drag(e, beforeContainer));
          document.addEventListener('touchend', () => this.endDrag());
          
          // Click/tap on slider area
          slider.addEventListener('click', (e) => this.handleSliderClick(e, slider, beforeContainer));
          slider.addEventListener('touchend', (e) => {
            if (!this.isDragging) {
              this.handleSliderClick(e, slider, beforeContainer);
            }
          });
        });
      }

      startDrag(e, slider, beforeContainer) {
        e.preventDefault();
        this.isDragging = true;
        this.currentSlider = slider;
        
        // Get the initial X position
        this.initialX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
        this.initialPosition = parseFloat(beforeContainer.style.width || '50');
        
        // Add active class for styling if needed
        slider.classList.add('active');
      }

      drag(e, beforeContainer) {
        if (!this.isDragging || !this.currentSlider) return;
        
        e.preventDefault();
        
        const sliderRect = this.currentSlider.getBoundingClientRect();
        const currentX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
        
        // Calculate the position as a percentage of the slider width
        let newPosition = ((currentX - sliderRect.left) / sliderRect.width) * 100;
        
        // Constrain the position between 0% and 100%
        newPosition = Math.max(0, Math.min(100, newPosition));
        
        // Update the width of the before image container
        this.updateSliderPosition(newPosition);
      }

      endDrag() {
        if (this.isDragging && this.currentSlider) {
          this.currentSlider.classList.remove('active');
        }
        
        this.isDragging = false;
        this.currentSlider = null;
      }

      handleSliderClick(e, slider, beforeContainer) {
        const sliderRect = slider.getBoundingClientRect();
        const clickX = e.type.includes('touch') ? e.changedTouches[0].clientX : e.clientX;
        
        // Calculate the position as a percentage of the slider width
        let newPosition = ((clickX - sliderRect.left) / sliderRect.width) * 100;
        
        // Constrain the position between 0% and 100%
        newPosition = Math.max(0, Math.min(100, newPosition));
        
        // Update the width of the before image container
        this.updateSliderPosition(newPosition);
      }

      updateSliderPosition(position) {
        if (!this.currentSlider) return;
        
        const beforeContainer = this.currentSlider.querySelector('.ai-before-image-container-{{ ai_gen_id }}');
        const handle = this.currentSlider.querySelector('.ai-slider-handle-{{ ai_gen_id }}');
        
        beforeContainer.style.width = `${position}%`;
        handle.style.left = `${position}%`;
      }
    }

    customElements.define('before-after-slider-{{ ai_gen_id }}', BeforeAfterSlider);

    // Initialize the component
    const sliderComponent = document.querySelector('.ai-before-after-{{ ai_gen_id }}');
    new BeforeAfterSlider().connectedCallback();
  })();
</script>

{% schema %}
{
  "name": "Before/After Comparison",
  "tag": null,
  "class": "before-after-section",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Section padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Gap between cards",
      "default": 20
    },
    {
      "type": "range",
      "id": "image_aspect_ratio",
      "min": 50,
      "max": 150,
      "step": 5,
      "unit": "%",
      "label": "Image aspect ratio",
      "default": 75
    },
    {
      "type": "range",
      "id": "card_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Card border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider handle color",
      "default": "#4A90E2"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show before/after labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label text",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label text",
      "default": "After"
    },
    {
      "type": "header",
      "content": "Card 1"
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title",
      "default": "Transformation 1"
    },
    {
      "type": "textarea",
      "id": "description_1",
      "label": "Description",
      "default": "See the amazing transformation with our product."
    },
    {
      "type": "header",
      "content": "Card 2"
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title",
      "default": "Transformation 2"
    },
    {
      "type": "textarea",
      "id": "description_2",
      "label": "Description",
      "default": "Another incredible result from our service."
    },
    {
      "type": "header",
      "content": "Card 3"
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title",
      "default": "Transformation 3"
    },
    {
      "type": "textarea",
      "id": "description_3",
      "label": "Description",
      "default": "The difference is clear with our solution."
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 14,
      "max": 32,
      "step": 1,
      "unit": "px",
      "label": "Title font size",
      "default": 20
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Description font size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "card_background",
      "label": "Card background color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#666666"
    }
  ],
  "presets": [
    {
      "name": "Before/After Comparison"
    }
  ]
}
{% endschema %}