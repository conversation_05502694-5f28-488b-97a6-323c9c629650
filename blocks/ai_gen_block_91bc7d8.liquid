{% doc %}
  @prompt
    Create a page-specific template for Real Estate Investment page without a slider photo gallery. first section with page information and a photo, second section an image gallery. Do not remove the booking section, first of all pictures gallery should be horizontal, second you should remove an  Before & After Results slider section


{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .real-estate-container-{{ ai_gen_id }} {
    margin: 0 auto;
    max-width: 100%;
  }

  .info-section-{{ ai_gen_id }} {
    display: flex;
    flex-direction: row;
    gap: 40px;
    margin-bottom: 60px;
    align-items: flex-start;
  }

  @media screen and (max-width: 990px) {
    .info-section-{{ ai_gen_id }} {
      flex-direction: column;
      gap: 30px;
    }
  }

  .info-content-{{ ai_gen_id }} {
    flex: 1;
  }

  .info-image-{{ ai_gen_id }} {
    flex: 1;
    position: relative;
    border-radius: {{ block.settings.image_border_radius }}px;
    overflow: hidden;
  }

  .info-image-{{ ai_gen_id }} img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
  }

  .info-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    aspect-ratio: 4/3;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: {{ block.settings.image_border_radius }}px;
  }

  .info-image-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
  }

  .info-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    color: {{ block.settings.heading_color }};
    margin-top: 0;
    margin-bottom: 20px;
  }

  .info-description-{{ ai_gen_id }} {
    font-size: {{ block.settings.text_size }}px;
    color: {{ block.settings.text_color }};
    line-height: 1.6;
  }

  .gallery-section-{{ ai_gen_id }} {
    margin-top: 60px;margin-bottom: 60px;
  }

  .gallery-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.gallery_heading_size }}px;
    color: {{ block.settings.heading_color }};
    margin-bottom: 30px;
    text-align: center;
  }

  .gallery-horizontal-{{ ai_gen_id }} {
    display: flex;
    overflow-x: auto;
    gap: 20px;
    padding-bottom: 20px;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }

  .gallery-item-{{ ai_gen_id }} {
    flex: 0 0 {{ block.settings.image_width }}px;
    position: relative;
    border-radius: {{ block.settings.gallery_border_radius }}px;
    overflow: hidden;
    scroll-snap-align: start;
    height: {{ block.settings.image_height }}px;
  }

  .gallery-item-{{ ai_gen_id }} img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
  }

  .gallery-item-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: {{ block.settings.gallery_border_radius }}px;
  }

  .gallery-item-placeholder-{{ ai_gen_id }} svg {
    width: 80%;
    height: 80%;
  }

  .gallery-caption-{{ ai_gen_id }} {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px15px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .gallery-item-{{ ai_gen_id }}:hover .gallery-caption-{{ ai_gen_id }} {
    opacity: 1;
  }

  .gallery-item-{{ ai_gen_id }}:hover img {
    transform: scale(1.05);
  }

  /* Hide scrollbar but keep functionality */
  .gallery-horizontal-{{ ai_gen_id }}::-webkit-scrollbar {
    height: 6px;
  }

  .gallery-horizontal-{{ ai_gen_id }}::-webkit-scrollbar-track {
    background: #f1f1f1;border-radius: 10px;
  }

  .gallery-horizontal-{{ ai_gen_id }}::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
  }

  .gallery-horizontal-{{ ai_gen_id }}::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
{% endstyle %}

<div class="real-estate-container-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <!-- Info Section -->
  <div class="info-section-{{ ai_gen_id }}">
    <div class="info-content-{{ ai_gen_id }}">
      <h2 class="info-title-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
      <div class="info-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
    </div>
    <div class="info-image-{{ ai_gen_id }}">
      {% if block.settings.main_image %}
        <img 
          src="{{ block.settings.main_image | image_url: width: 800 }}" 
          alt="{{ block.settings.main_image.alt | escape }}"
          loading="lazy"
          width="{{ block.settings.main_image.width }}"
          height="{{ block.settings.main_image.height }}"
        >
      {% else %}
        <div class="info-image-placeholder-{{ ai_gen_id }}">
          {{ 'image' | placeholder_svg_tag }}
        </div>
      {% endif %}
    </div>
  </div>

  <!-- Horizontal Gallery Section -->
  <div class="gallery-section-{{ ai_gen_id }}">
    {% if block.settings.gallery_heading != blank %}
      <h2 class="gallery-title-{{ ai_gen_id }}">{{ block.settings.gallery_heading }}</h2>
    {% endif %}
    
    <div class="gallery-horizontal-{{ ai_gen_id }}">
      {% for i in (1..8) %}
        {% assign image_setting = 'gallery_image_' | append: i %}
        {% assign caption_setting = 'gallery_caption_' | append: i %}
        {% assign image = block.settings[image_setting] %}
        {% assign caption = block.settings[caption_setting] %}
        
        {% if image or forloop.index <= block.settings.placeholder_count %}
          <div class="gallery-item-{{ ai_gen_id }}">
            {% if image %}
              <img 
                src="{{ image | image_url: width: 600 }}" 
                alt="{{ image.alt | escape }}"
                loading="lazy"
                width="{{ image.width }}"
                height="{{ image.height }}"
              >
              {% if caption != blank %}
                <div class="gallery-caption-{{ ai_gen_id }}">{{ caption }}</div>
              {% endif %}
            {% else %}
              <div class="gallery-item-placeholder-{{ ai_gen_id }}">
                {{ 'image' | placeholder_svg_tag }}
              </div>
            {% endif %}
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Real Estate Investment",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Information Section"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Real Estate Investment Opportunities"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Discover premium real estate investment opportunities with high returns. Our carefully selected properties offer excellent growth potential in prime locations.</p>"
    },
    {
      "type": "image_picker",
      "id": "main_image",
      "label": "Main image"
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Horizontal Gallery Section"
    },
    {
      "type": "text",
      "id": "gallery_heading",
      "label": "Gallery heading",
      "default": "Property Gallery"
    },
    {
      "type": "range",
      "id": "gallery_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Gallery image border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "image_width",
      "min": 200,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Image width",
      "default": 300
    },
    {
      "type": "range",
      "id": "image_height",
      "min": 150,
      "max": 400,
      "step": 10,
      "unit": "px",
      "label": "Image height",
      "default": 200
    },
    {
      "type": "range",
      "id": "placeholder_count",
      "min": 0,
      "max": 8,
      "step": 1,
      "label": "Number of placeholder images",
      "default": 6,
      "info": "Used when no images are uploaded"
    },
    {
      "type": "header",
      "content": "Gallery Images"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_1",
      "label": "Image 1"
    },
    {
      "type": "text",
      "id": "gallery_caption_1",
      "label": "Caption 1"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_2",
      "label": "Image 2"
    },
    {
      "type": "text",
      "id": "gallery_caption_2",
      "label": "Caption 2"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_3",
      "label": "Image 3"
    },
    {
      "type": "text",
      "id": "gallery_caption_3",
      "label": "Caption 3"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_4",
      "label": "Image 4"
    },
    {
      "type": "text",
      "id": "gallery_caption_4",
      "label": "Caption 4"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_5",
      "label": "Image 5"
    },
    {
      "type": "text",
      "id": "gallery_caption_5",
      "label": "Caption 5"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_6",
      "label": "Image 6"
    },
    {
      "type": "text",
      "id": "gallery_caption_6",
      "label": "Caption 6"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_7",
      "label": "Image 7"
    },
    {
      "type": "text",
      "id": "gallery_caption_7",
      "label": "Caption 7"
    },
    {
      "type": "image_picker",
      "id": "gallery_image_8",
      "label": "Image 8"
    },
    {
      "type": "text",
      "id": "gallery_caption_8",
      "label": "Caption 8"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 32
    },
    {
      "type": "range",
      "id": "gallery_heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Gallery heading size",
      "default": 28
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    }
  ],
  "presets": [
    {
      "name": "Real Estate Investment"
    }
  ]
}
{% endschema %}