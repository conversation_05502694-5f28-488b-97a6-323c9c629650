{% doc %}
  @prompt
    Create a tour itinerary section with day tabs. Include 5 tabs labeled Day 1, Day 2, Day 3, Day 4, Day 5. Each tab shows different content when clicked. Each day should have a text field for description and an image field. Make it responsive and clean looking for a travel website.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-tour-itinerary-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-tour-itinerary__header-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 40px;
  }

  .ai-tour-itinerary__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.title_color }};
    margin: 0 0 16px;
    font-weight: 600;
  }

  .ai-tour-itinerary__subtitle-{{ ai_gen_id }} {
    font-size: {{ block.settings.subtitle_size }}px;
    color: {{ block.settings.subtitle_color }};
    margin: 0;
    opacity: 0.8;
  }

  .ai-tour-itinerary__tabs-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
    border-bottom: 2px solid {{ block.settings.tab_border_color }};
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }} {
    background: none;
    border: none;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 500;
    color: {{ block.settings.tab_inactive_color }};
    cursor: pointer;
    position: relative;
    transition: color 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }}:hover {
    color: {{ block.settings.tab_active_color }};
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }}.active {
    color: {{ block.settings.tab_active_color }};
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }}.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 3px;
    background-color: {{ block.settings.tab_active_color }};
    border-radius: 2px 2px 0 0;
  }

  .ai-tour-itinerary__content-{{ ai_gen_id }} {
    min-height: 400px;
  }

  .ai-tour-itinerary__day-{{ ai_gen_id }} {
    display: none;
    animation: ai-tour-fade-in-{{ ai_gen_id }} 0.3s ease-in-out;
  }

  .ai-tour-itinerary__day-{{ ai_gen_id }}.active {
    display: block;
  }

  @keyframes ai-tour-fade-in-{{ ai_gen_id }} {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
  }

  .ai-tour-itinerary__day-text-{{ ai_gen_id }} {
    order: 1;
  }

  .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
    order: 2;
  }

  .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-text-{{ ai_gen_id }} {
    order: 2;
  }

  .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
    order: 1;
  }

  .ai-tour-itinerary__day-title-{{ ai_gen_id }} {
    font-size: 28px;
    color: {{ block.settings.day_title_color }};
    margin: 0 0 20px;
    font-weight: 600;
  }

  .ai-tour-itinerary__day-description-{{ ai_gen_id }} {
    font-size: 16px;
    line-height: 1.6;
    color: {{ block.settings.day_text_color }};
  }

  .ai-tour-itinerary__image-wrapper-{{ ai_gen_id }} {
    width: 100%;
    height: 350px;
    border-radius: {{ block.settings.image_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  .ai-tour-itinerary__image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-tour-itinerary__image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-tour-itinerary__image-placeholder-{{ ai_gen_id }} svg {
    width: 80px;
    height: 80px;
    opacity: 0.3;
  }

  @media screen and (max-width: 768px) {
    .ai-tour-itinerary-{{ ai_gen_id }} {
      padding: 30px 16px;
    }

    .ai-tour-itinerary__tabs-{{ ai_gen_id }} {
      justify-content: flex-start;
      margin-bottom: 30px;
    }

    .ai-tour-itinerary__tab-{{ ai_gen_id }} {
      padding: 12px 16px;
      font-size: 14px;
    }

    .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .ai-tour-itinerary__day-text-{{ ai_gen_id }},
    .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-text-{{ ai_gen_id }} {
      order: 2;
    }

    .ai-tour-itinerary__day-image-{{ ai_gen_id }},
    .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
      order: 1;
    }

    .ai-tour-itinerary__day-title-{{ ai_gen_id }} {
      font-size: 24px;
      margin-bottom: 16px;
    }

    .ai-tour-itinerary__image-wrapper-{{ ai_gen_id }} {
      height: 250px;
    }
  }
{% endstyle %}

<tour-itinerary-{{ ai_gen_id }}
  class="ai-tour-itinerary-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  {% if block.settings.title != blank or block.settings.subtitle != blank %}
    <div class="ai-tour-itinerary__header-{{ ai_gen_id }}">
      {% if block.settings.title != blank %}
        <h2 class="ai-tour-itinerary__title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
      {% endif %}
      {% if block.settings.subtitle != blank %}
        <p class="ai-tour-itinerary__subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</p>
      {% endif %}
    </div>
  {% endif %}

  <div class="ai-tour-itinerary__tabs-{{ ai_gen_id }}" role="tablist">
    {% for i in (1..5) %}
      <button
        class="ai-tour-itinerary__tab-{{ ai_gen_id }}{% if i == 1 %} active{% endif %}"
        role="tab"
        aria-selected="{% if i == 1 %}true{% else %}false{% endif %}"
        aria-controls="day-{{ i }}-{{ ai_gen_id }}"
        data-tab="day-{{ i }}"
      >
        Day {{ i }}
      </button>
    {% endfor %}
  </div>

  <div class="ai-tour-itinerary__content-{{ ai_gen_id }}">
    {% for i in (1..5) %}
      {% liquid
        assign day_title_key = 'day_' | append: i | append: '_title'
        assign day_description_key = 'day_' | append: i | append: '_description'
        assign day_image_key = 'day_' | append: i | append: '_image'
        
        assign day_title = block.settings[day_title_key]
        assign day_description = block.settings[day_description_key]
        assign day_image = block.settings[day_image_key]
        
        assign is_even = i | modulo: 2
      %}
      
      <div
        class="ai-tour-itinerary__day-{{ ai_gen_id }}{% if i == 1 %} active{% endif %}"
        role="tabpanel"
        id="day-{{ i }}-{{ ai_gen_id }}"
        aria-labelledby="day-{{ i }}-tab"
      >
        <div class="ai-tour-itinerary__day-content-{{ ai_gen_id }}{% if is_even == 0 %} reverse{% endif %}">
          <div class="ai-tour-itinerary__day-text-{{ ai_gen_id }}">
            <h3 class="ai-tour-itinerary__day-title-{{ ai_gen_id }}">
              {% if day_title != blank %}
                {{ day_title }}
              {% else %}
                Day {{ i }}
              {% endif %}
            </h3>
            <div class="ai-tour-itinerary__day-description-{{ ai_gen_id }}">
              {% if day_description != blank %}
                {{ day_description }}
              {% else %}
                <p>Add your day {{ i }} itinerary description here. Describe the activities, locations, and experiences planned for this day of the tour.</p>
              {% endif %}
            </div>
          </div>
          
          <div class="ai-tour-itinerary__day-image-{{ ai_gen_id }}">
            <div class="ai-tour-itinerary__image-wrapper-{{ ai_gen_id }}">
              {% if day_image %}
                <img
                  src="{{ day_image | image_url: width: 600 }}"
                  alt="{{ day_image.alt | escape }}"
                  class="ai-tour-itinerary__image-{{ ai_gen_id }}"
                  loading="lazy"
                  width="600"
                  height="350"
                >
              {% else %}
                <div class="ai-tour-itinerary__image-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    {% endfor %}
  </div>
</tour-itinerary-{{ ai_gen_id }}>

<script>
  (function() {
    class TourItinerary{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.tabs = this.querySelectorAll('.ai-tour-itinerary__tab-{{ ai_gen_id }}');
        this.panels = this.querySelectorAll('.ai-tour-itinerary__day-{{ ai_gen_id }}');
        this.setupEventListeners();
      }

      setupEventListeners() {
        this.tabs.forEach((tab, index) => {
          tab.addEventListener('click', () => {
            this.switchTab(index);
          });

          tab.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              this.switchTab(index);
            }
          });
        });
      }

      switchTab(activeIndex) {
        this.tabs.forEach((tab, index) => {
          const isActive = index === activeIndex;
          tab.classList.toggle('active', isActive);
          tab.setAttribute('aria-selected', isActive);
        });

        this.panels.forEach((panel, index) => {
          panel.classList.toggle('active', index === activeIndex);
        });

        this.tabs[activeIndex].focus();
      }
    }

    customElements.define('tour-itinerary-{{ ai_gen_id }}', TourItinerary{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Tour Itinerary",
  "settings": [
    {
      "type": "header",
      "content": "Section Header"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Tour Itinerary"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Discover your adventure day by day"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle color",
      "default": "#7f8c8d"
    },
    {
      "type": "color",
      "id": "tab_active_color",
      "label": "Active tab color",
      "default": "#3498db"
    },
    {
      "type": "color",
      "id": "tab_inactive_color",
      "label": "Inactive tab color",
      "default": "#95a5a6"
    },
    {
      "type": "color",
      "id": "tab_border_color",
      "label": "Tab border color",
      "default": "#ecf0f1"
    },
    {
      "type": "color",
      "id": "day_title_color",
      "label": "Day title color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "day_text_color",
      "label": "Day text color",
      "default": "#34495e"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 36
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Section border radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Day 1"
    },
    {
      "type": "text",
      "id": "day_1_title",
      "label": "Day 1 title",
      "default": "Arrival & City Exploration"
    },
    {
      "type": "richtext",
      "id": "day_1_description",
      "label": "Day 1 description",
      "default": "<p>Welcome to your adventure! Start with a guided city tour, visit local landmarks, and enjoy a welcome dinner at a traditional restaurant. Get acquainted with the local culture and prepare for the exciting days ahead.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_1_image",
      "label": "Day 1 image"
    },
    {
      "type": "header",
      "content": "Day 2"
    },
    {
      "type": "text",
      "id": "day_2_title",
      "label": "Day 2 title",
      "default": "Mountain Adventure"
    },
    {
      "type": "richtext",
      "id": "day_2_description",
      "label": "Day 2 description",
      "default": "<p>Embark on a thrilling mountain hiking experience. Enjoy breathtaking views, discover hidden trails, and connect with nature. Professional guides will ensure your safety while sharing local knowledge about the flora and fauna.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_2_image",
      "label": "Day 2 image"
    },
    {
      "type": "header",
      "content": "Day 3"
    },
    {
      "type": "text",
      "id": "day_3_title",
      "label": "Day 3 title",
      "default": "Cultural Immersion"
    },
    {
      "type": "richtext",
      "id": "day_3_description",
      "label": "Day 3 description",
      "default": "<p>Dive deep into local culture with visits to museums, art galleries, and historical sites. Participate in traditional workshops, meet local artisans, and learn about the rich heritage of the region.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_3_image",
      "label": "Day 3 image"
    },
    {
      "type": "header",
      "content": "Day 4"
    },
    {
      "type": "text",
      "id": "day_4_title",
      "label": "Day 4 title",
      "default": "Beach & Relaxation"
    },
    {
      "type": "richtext",
      "id": "day_4_description",
      "label": "Day 4 description",
      "default": "<p>Unwind at pristine beaches with crystal-clear waters. Enjoy water sports, beach volleyball, or simply relax under the sun. End the day with a beachside barbecue and sunset viewing.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_4_image",
      "label": "Day 4 image"
    },
    {
      "type": "header",
      "content": "Day 5"
    },
    {
      "type": "text",
      "id": "day_5_title",
      "label": "Day 5 title",
      "default": "Farewell & Departure"
    },
    {
      "type": "richtext",
      "id": "day_5_description",
      "label": "Day 5 description",
      "default": "<p>Your final day includes last-minute shopping at local markets, a farewell brunch with fellow travelers, and departure arrangements. Take home unforgettable memories and new friendships from this amazing journey.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_5_image",
      "label": "Day 5 image"
    }
  ],
  "presets": [
    {
      "name": "Tour Itinerary"
    }
  ]
}
{% endschema %}