{% doc %}
  @prompt
    Create an elegant wedding section with image on the left and text content on the right. The background should be soft pink. Include settings for image upload, heading text, description text, button with customizable text and link. The section should be responsive with proper spacing and elegant typography suitable for wedding themes. Keep it simple and clean without complex decorative elements.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .wedding-block-{{ ai_gen_id }} {
    background-color: {{ block.settings.background_color }};
    padding: 60px 40px;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .wedding-container-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .wedding-image-wrapper-{{ ai_gen_id }} {
    flex: 1;
    min-width: 0;
  }

  .wedding-image-{{ ai_gen_id }} {
    width: 100%;
    height: auto;
    border-radius: {{ block.settings.image_border_radius }}px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
  }

  .wedding-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    aspect-ratio: 3/4;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: {{ block.settings.image_border_radius }}px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
  }

  .wedding-image-placeholder-{{ ai_gen_id }} svg {
    width: 80%;
    height: 80%;
    opacity: 0.3;
  }

  .wedding-content-{{ ai_gen_id }} {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .wedding-heading-{{ ai_gen_id }} {
    font-family: {{ block.settings.heading_font.family }}, serif;
    font-size: {{ block.settings.heading_size }}px;
    color: {{ block.settings.heading_color }};
    margin: 0;
    font-weight: 400;
    line-height: 1.2;
  }

  .wedding-description-{{ ai_gen_id }} {
    font-family: {{ block.settings.text_font.family }}, sans-serif;
    font-size: {{ block.settings.text_size }}px;
    color: {{ block.settings.text_color }};
    line-height: 1.6;
    margin: 0;
  }

  .wedding-button-{{ ai_gen_id }} {
    display: inline-block;
    padding: 12px 30px;
    background-color: {{ block.settings.button_color }};
    color: {{ block.settings.button_text_color }};
    text-decoration: none;
    border-radius: {{ block.settings.button_border_radius }}px;
    font-family: {{ block.settings.text_font.family }}, sans-serif;
    font-size: 16px;
    transition: background-color 0.3s ease;
    margin-top: 10px;
    align-self: flex-start;
    border: none;
  }

  .wedding-button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_color }};
  }

  @media screen and (max-width: 768px) {
    .wedding-block-{{ ai_gen_id }} {
      padding: 40px 20px;
    }
    
    .wedding-container-{{ ai_gen_id }} {
      flex-direction: column;
      gap: 30px;
    }
    
    .wedding-heading-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | minus: 8 }}px;
    }
    
    .wedding-description-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | minus: 1 }}px;
    }
  }
{% endstyle %}

<div class="wedding-block-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="wedding-container-{{ ai_gen_id }}">
    <div class="wedding-image-wrapper-{{ ai_gen_id }}">
      {% if block.settings.image %}
        <img 
          src="{{ block.settings.image | img_url: 'master' }}" 
          alt="{{ block.settings.image.alt | escape }}" 
          class="wedding-image-{{ ai_gen_id }}"
          loading="lazy"
          width="{{ block.settings.image.width }}"
          height="{{ block.settings.image.height }}"
        >
      {% else %}
        <div class="wedding-image-placeholder-{{ ai_gen_id }}">
          {{ 'image' | placeholder_svg_tag }}
        </div>
      {% endif %}
    </div>
    
    <div class="wedding-content-{{ ai_gen_id }}">
      {% if block.settings.heading != blank %}
        <h2 class="wedding-heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
      {% endif %}
      
      {% if block.settings.description != blank %}
        <div class="wedding-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
      {% endif %}
      
      {% if block.settings.button_text != blank and block.settings.button_link != blank %}
        <a href="{{ block.settings.button_link }}" class="wedding-button-{{ ai_gen_id }}">
          {{ block.settings.button_text }}
        </a>
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Wedding Section",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Our Special Day"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Join us to celebrate our wedding day. We're excited to share this special moment with our friends and family as we begin our journey together.</p>"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "RSVP Now"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#FFF0F5"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "#D8A9B3"
    },
    {
      "type": "color",
      "id": "button_hover_color",
      "label": "Button hover color",
      "default": "#C48490"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#FFFFFF"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "font_picker",
      "id": "heading_font",
      "label": "Heading font",
      "default": "serif"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 24,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 40
    },
    {
      "type": "font_picker",
      "id": "text_font",
      "label": "Text font",
      "default": "sans-serif"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 14,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Section border radius",
      "default": 0
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Button border radius",
      "default": 4
    }
  ],
  "presets": [
    {
      "name": "Wedding Section"
    }
  ]
}
{% endschema %}