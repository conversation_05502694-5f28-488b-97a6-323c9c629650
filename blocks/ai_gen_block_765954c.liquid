{% doc %}
  @prompt
    Create a horizontal photo gallery block that allows uploading up to 5 images. The gallery should be compact, responsive, and beautiful with the following features: horizontal scrolling through photos, click to enlarge images with a lightbox effect, smooth animations, and mobile-friendly design. Include customizable settings for gallery spacing, image aspect ratio, and styling options.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-gallery-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding: {{ block.settings.gallery_padding }}px;
    background-color: {{ block.settings.gallery_background }};
    border-radius: {{ block.settings.gallery_border_radius }}px;
  }

  .ai-gallery-container-{{ ai_gen_id }} {
    display: flex;
    gap: {{ block.settings.image_spacing }}px;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    padding-bottom: 15px;
    margin-bottom: -15px;
  }

  .ai-gallery-container-{{ ai_gen_id }}::-webkit-scrollbar {
    display: none;
  }

  .ai-gallery-item-{{ ai_gen_id }} {
    flex: 0 0 auto;
    width: calc({{ block.settings.images_per_row }}% - {{ block.settings.image_spacing | times: 0.9 }}px);
    min-width: 200px;
    position: relative;
    cursor: pointer;
    border-radius: {{ block.settings.image_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .ai-gallery-item-{{ ai_gen_id }}:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  .ai-gallery-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    padding-top: {{ block.settings.aspect_ratio }}%;
    overflow: hidden;
  }

  .ai-gallery-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: {{ block.settings.image_fit }};
    transition: transform 0.5s ease;
  }

  .ai-gallery-item-{{ ai_gen_id }}:hover .ai-gallery-image-{{ ai_gen_id }} {
    transform: scale(1.05);
  }

  .ai-gallery-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .ai-gallery-placeholder-{{ ai_gen_id }} svg {
    width: 50%;
    height: 50%;
    opacity: 0.5;
  }

  .ai-gallery-modal-{{ ai_gen_id }} {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .ai-gallery-modal-{{ ai_gen_id }}.active {
    opacity: 1;
    visibility: visible;
  }

  .ai-gallery-modal-content-{{ ai_gen_id }} {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .ai-gallery-modal-image-{{ ai_gen_id }} {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 4px;
  }

  .ai-gallery-modal-close-{{ ai_gen_id }} {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 30px;
    cursor: pointer;
    padding: 5px;
    line-height: 1;
  }

  .ai-gallery-modal-nav-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: background 0.3s ease;
  }

  .ai-gallery-modal-nav-{{ ai_gen_id }}:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .ai-gallery-modal-prev-{{ ai_gen_id }} {
    left: 20px;
  }

  .ai-gallery-modal-next-{{ ai_gen_id }} {
    right: 20px;
  }

  .ai-gallery-modal-counter-{{ ai_gen_id }} {
    color: white;
    margin-top: 15px;
    font-size: 14px;
  }

  .ai-gallery-navigation-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
  }

  .ai-gallery-dot-{{ ai_gen_id }} {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: {{ block.settings.dot_color }};
    opacity: 0.5;
    cursor: pointer;
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .ai-gallery-dot-{{ ai_gen_id }}.active {
    opacity: 1;
    transform: scale(1.2);
  }

  @media screen and (max-width: 749px) {
    .ai-gallery-item-{{ ai_gen_id }} {
      width: calc(100% / {{ block.settings.images_per_row_mobile }} - {{ block.settings.image_spacing | times: 0.8 }}px);
      min-width: 150px;
    }

    .ai-gallery-modal-nav-{{ ai_gen_id }} {
      width: 40px;
      height: 40px;}
  }
{% endstyle %}

<div class="ai-gallery-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-gallery-container-{{ ai_gen_id }}">
    {% for i in (1..5) %}
      {% assign image_setting = 'image_' | append: i %}
      {% assign image = block.settings[image_setting] %}
      {% if image != blank %}
        <div class="ai-gallery-item-{{ ai_gen_id }}" data-index="{{ forloop.index0 }}">
          <div class="ai-gallery-image-wrapper-{{ ai_gen_id }}">
            <img
              src="{{ image | image_url: width: 800 }}"
              alt="{{ image.alt | escape }}"
              class="ai-gallery-image-{{ ai_gen_id }}"
              loading="{% if forloop.index <= 2 %}eager{% else %}lazy{% endif %}"
              width="{{ image.width }}"
              height="{{ image.height }}"
            >
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>

  {% if block.settings.show_navigation_dots %}
    <div class="ai-gallery-navigation-{{ ai_gen_id }}">
      {% for i in (1..5) %}
        {% assign image_setting = 'image_' | append: i %}
        {% if block.settings[image_setting] != blank %}
          <div class="ai-gallery-dot-{{ ai_gen_id }} {% if forloop.first %}active{% endif %}" data-index="{{ forloop.index0 }}"></div>
        {% endif %}
      {% endfor %}
    </div>
  {% endif %}

  <div class="ai-gallery-modal-{{ ai_gen_id }}">
    <div class="ai-gallery-modal-content-{{ ai_gen_id }}">
      <button class="ai-gallery-modal-close-{{ ai_gen_id }}" aria-label="Close">×</button>
      <img src="" alt="" class="ai-gallery-modal-image-{{ ai_gen_id }}">
      <div class="ai-gallery-modal-counter-{{ ai_gen_id }}"></div>
      <button class="ai-gallery-modal-nav-{{ ai_gen_id }} ai-gallery-modal-prev-{{ ai_gen_id }}" aria-label="Previous">❮</button>
      <button class="ai-gallery-modal-nav-{{ ai_gen_id }} ai-gallery-modal-next-{{ ai_gen_id }}" aria-label="Next">❯</button>
    </div>
  </div>
</div>

<script>
  (function() {
    class GalleryLightbox extends HTMLElement {
      constructor() {
        super();
        this.currentIndex = 0;
        this.galleryItems = this.querySelectorAll('.ai-gallery-item-{{ ai_gen_id }}');
        this.modal = this.querySelector('.ai-gallery-modal-{{ ai_gen_id }}');
        this.modalImage = this.querySelector('.ai-gallery-modal-image-{{ ai_gen_id }}');
        this.modalCounter = this.querySelector('.ai-gallery-modal-counter-{{ ai_gen_id }}');
        this.closeButton = this.querySelector('.ai-gallery-modal-close-{{ ai_gen_id }}');
        this.prevButton = this.querySelector('.ai-gallery-modal-prev-{{ ai_gen_id }}');
        this.nextButton = this.querySelector('.ai-gallery-modal-next-{{ ai_gen_id }}');
        this.navigationDots = this.querySelectorAll('.ai-gallery-dot-{{ ai_gen_id }}');
        
        this.totalImages = this.galleryItems.length;
        
        this.init();
      }
      
      init() {
        // Open modal on image click
        this.galleryItems.forEach((item, index) => {
          item.addEventListener('click', () => {
            this.openModal(index);
          });
        });
        
        // Close modal
        this.closeButton.addEventListener('click', () => {
          this.closeModal();
        });
        
        // Navigate with buttons
        this.prevButton.addEventListener('click', () => {
          this.navigate(-1);
        });
        
        this.nextButton.addEventListener('click', () => {
          this.navigate(1);
        });
        
        // Close on ESC key
        document.addEventListener('keydown', (e) => {
          if (this.modal.classList.contains('active')) {
            if (e.key === 'Escape') {
              this.closeModal();
            } else if (e.key === 'ArrowLeft') {
              this.navigate(-1);
            } else if (e.key === 'ArrowRight') {
              this.navigate(1);
            }
          }
        });
        
        // Close on outside click
        this.modal.addEventListener('click', (e) => {
          if (e.target === this.modal) {
            this.closeModal();
          }
        });
        
        // Navigation dots
        this.navigationDots.forEach((dot, index) => {
          dot.addEventListener('click', () => {
            this.scrollToImage(index);
            this.updateActiveDot(index);
          });
        });
        
        // Scroll detection for dots
        const galleryContainer = this.querySelector('.ai-gallery-container-{{ ai_gen_id }}');
        if (galleryContainer) {
          galleryContainer.addEventListener('scroll', () => {
            this.handleScroll(galleryContainer);
          });
        }}
      
      handleScroll(container) {
        if (!container) return;
        
        const scrollLeft = container.scrollLeft;
        const containerWidth = container.offsetWidth;
        
        this.galleryItems.forEach((item, index) => {
          const itemLeft = item.offsetLeft - container.offsetLeft;
          const itemCenter = itemLeft + (item.offsetWidth / 2);
          
          if (itemCenter > scrollLeft && itemCenter < (scrollLeft + containerWidth)) {
            this.updateActiveDot(index);
          }
        });
      }
      
      scrollToImage(index) {
        const item = this.galleryItems[index];
        if (!item) return;
        
        const container = this.querySelector('.ai-gallery-container-{{ ai_gen_id }}');
        const scrollLeft = item.offsetLeft - container.offsetLeft -
                          (container.offsetWidth / 2) + (item.offsetWidth / 2);
        
        container.scrollTo({
          left: scrollLeft,
          behavior: 'smooth'
        });
      }
      
      updateActiveDot(index) {
        this.navigationDots.forEach((dot, i) => {
          if (i === index) {
            dot.classList.add('active');
          } else {
            dot.classList.remove('active');
          }
        });
      }
      
      openModal(index) {
        this.currentIndex = index;
        const currentItem = this.galleryItems[index];
        const imageElement = currentItem.querySelector('.ai-gallery-image-{{ ai_gen_id }}');
        
        this.modalImage.src = imageElement.src;
        this.modalImage.alt = imageElement.alt;
        this.updateCounter();
        
        this.modal.classList.add('active');document.body.style.overflow = 'hidden';
      }
      
      closeModal() {
        this.modal.classList.remove('active');
        document.body.style.overflow = '';
      }
      
      navigate(direction) {
        this.currentIndex = (this.currentIndex + direction + this.totalImages) % this.totalImages;
        
        const currentItem = this.galleryItems[this.currentIndex];
        const imageElement = currentItem.querySelector('.ai-gallery-image-{{ ai_gen_id }}');
        
        this.modalImage.src = imageElement.src;
        this.modalImage.alt = imageElement.alt;
        this.updateCounter();
        this.updateActiveDot(this.currentIndex);
      }
      
      updateCounter() {
        this.modalCounter.textContent = `${this.currentIndex + 1} / ${this.totalImages}`;
      }
    }
    
    customElements.define('gallery-lightbox-{{ ai_gen_id }}', GalleryLightbox);
    // Initialize the component
    const galleryElement = document.querySelector('.ai-gallery-{{ ai_gen_id }}');
    if (galleryElement && !galleryElement.matches(':defined')) {
      Object.setPrototypeOf(galleryElement, GalleryLightbox.prototype);
      galleryElement.connectedCallback();
    }
  })();
</script>

{% schema %}
{
  "name": "Photo Gallery",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Gallery Images"
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "Image 1"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Image 2"
    },
    {
      "type": "image_picker",
      "id": "image_3",
      "label": "Image 3"
    },
    {
      "type": "image_picker",
      "id": "image_4",
      "label": "Image 4"
    },
    {
      "type": "image_picker",
      "id": "image_5",
      "label": "Image 5"
    },
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "images_per_row",
      "min": 30,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Image width",
      "default": 40
    },
    {
      "type": "select",
      "id": "images_per_row_mobile",
      "label": "Images per row on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "aspect_ratio",
      "min": 50,
      "max": 150,
      "step": 10,
      "unit": "%",
      "label": "Image aspect ratio",
      "default": 100,
      "info": "Percentage of width.100% is square, 75% is 4:3, 56% is 16:9"
    },
    {
      "type": "select",
      "id": "image_fit",
      "label": "Image fit",
      "options": [
        {
          "value": "cover",
          "label": "Cover"
        },
        {
          "value": "contain",
          "label": "Contain"
        }
      ],
      "default": "cover"
    },
    {
      "type": "range",
      "id": "image_spacing",
      "min": 5,
      "max": 30,
      "step": 5,
      "unit": "px",
      "label": "Space between images",
      "default": 15
    },
    {
      "type": "header",
      "content": "Gallery Style"
    },
    {
      "type": "range",
      "id": "gallery_padding",
      "min": 0,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Gallery padding",
      "default": 20
    },
    {
      "type": "color",
      "id": "gallery_background",
      "label": "Gallery background",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "gallery_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Gallery border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "show_navigation_dots",
      "label": "Show navigation dots",
      "default": true
    },
    {
      "type": "color",
      "id": "dot_color",
      "label": "Navigation dot color",
      "default": "#000000"
    }
  ],
  "presets": [
    {
      "name": "Photo Gallery"
    }
  ]
}
{% endschema %}