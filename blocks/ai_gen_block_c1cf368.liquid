{% doc %}
  @prompt
    Booking window block:

First name

Last name

Phone number

Email

Select appointment date

"Book an appointment" button

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-booking-form-{{ ai_gen_id }} {
    padding: 30px;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-booking-form__title-{{ ai_gen_id }} {
    color: {{ block.settings.text_color }};
    margin-top: 0;
    margin-bottom: 20px;
    font-size: {{ block.settings.heading_size }}px;
    text-align: {{ block.settings.text_alignment }};
  }

  .ai-booking-form__description-{{ ai_gen_id }} {
    color: {{ block.settings.text_color }};
    margin-bottom: 25px;
    font-size: {{ block.settings.text_size }}px;
    text-align: {{ block.settings.text_alignment }};
  }

  .ai-booking-form__fields-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .ai-booking-form__field-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .ai-booking-form__label-{{ ai_gen_id }} {
    font-size: 14px;
    color: {{ block.settings.text_color }};
  }

  .ai-booking-form__input-{{ ai_gen_id }} {
    padding: 12px 15px;
    border: 1px solid {{ block.settings.input_border_color }};
    border-radius: {{ block.settings.input_border_radius }}px;
    background-color: {{ block.settings.input_background_color }};
    color: {{ block.settings.text_color }};
  }

  .ai-booking-form__input-{{ ai_gen_id }}:focus {
    outline: 2px solid {{ block.settings.accent_color }};
    outline-offset: 1px;
  }

  .ai-booking-form__button-{{ ai_gen_id }} {
    margin-top: 10px;
    padding: 14px 20px;
    background-color: {{ block.settings.button_color }};
    color: {{ block.settings.button_text_color }};
    border: none;
    border-radius: {{ block.settings.button_border_radius }}px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .ai-booking-form__button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_color }};
  }

  .ai-booking-form__success-{{ ai_gen_id }} {
    color: {{ block.settings.success_color }};
    margin-top: 15px;
    text-align: center;
    display: none;
  }

  .ai-booking-form__error-{{ ai_gen_id }} {
    color: {{ block.settings.error_color }};
    margin-top: 15px;
    text-align: center;
    display: none;
  }

  @media screen and (min-width: 768px) {
    .ai-booking-form__row-{{ ai_gen_id }} {
      display: flex;
      gap: 15px;
    }
    
    .ai-booking-form__row-{{ ai_gen_id }} .ai-booking-form__field-{{ ai_gen_id }} {
      flex: 1;
    }
  }
{% endstyle %}

<booking-form-{{ ai_gen_id }} class="ai-booking-form-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.heading != blank %}
    <h2 class="ai-booking-form__title-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
  {% endif %}
  
  {% if block.settings.description != blank %}
    <div class="ai-booking-form__description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
  {% endif %}
  
  <form class="ai-booking-form__fields-{{ ai_gen_id }}">
    <div class="ai-booking-form__row-{{ ai_gen_id }}">
      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="first-name-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.first_name_label }}</label>
        <input 
          type="text" 
          id="first-name-{{ ai_gen_id }}" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
          aria-required="true"
        >
      </div>
      
      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="last-name-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.last_name_label }}</label>
        <input 
          type="text" 
          id="last-name-{{ ai_gen_id }}" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
          aria-required="true"
        >
      </div>
    </div>
    
    <div class="ai-booking-form__row-{{ ai_gen_id }}">
      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="phone-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.phone_label }}</label>
        <input 
          type="tel" 
          id="phone-{{ ai_gen_id }}" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
          aria-required="true"
        >
      </div>
      
      <div class="ai-booking-form__field-{{ ai_gen_id }}">
        <label for="email-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.email_label }}</label>
        <input 
          type="email" 
          id="email-{{ ai_gen_id }}" 
          class="ai-booking-form__input-{{ ai_gen_id }}" 
          required
          aria-required="true"
        >
      </div>
    </div>
    
    <div class="ai-booking-form__field-{{ ai_gen_id }}">
      <label for="date-{{ ai_gen_id }}" class="ai-booking-form__label-{{ ai_gen_id }}">{{ block.settings.date_label }}</label>
      <input 
        type="date" 
        id="date-{{ ai_gen_id }}" 
        class="ai-booking-form__input-{{ ai_gen_id }}" 
        required
        aria-required="true"
      >
    </div>
    
    <button type="submit" class="ai-booking-form__button-{{ ai_gen_id }}">{{ block.settings.button_label }}</button>
  </form>
  
  <div class="ai-booking-form__success-{{ ai_gen_id }}">{{ block.settings.success_message }}</div>
  <div class="ai-booking-form__error-{{ ai_gen_id }}">{{ block.settings.error_message }}</div>
</booking-form-{{ ai_gen_id }}>

<script>
  (function() {
    class BookingForm{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.form = this.querySelector('form');
        this.successMessage = this.querySelector('.ai-booking-form__success-{{ ai_gen_id }}');
        this.errorMessage = this.querySelector('.ai-booking-form__error-{{ ai_gen_id }}');
        this.submitButton = this.querySelector('.ai-booking-form__button-{{ ai_gen_id }}');
        
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
        
        // Set minimum date to today
        const dateInput = this.querySelector('#date-{{ ai_gen_id }}');
        const today = new Date().toISOString().split('T')[0];
        dateInput.setAttribute('min', today);
      }
      
      handleSubmit(event) {
        event.preventDefault();
        
        // Hide any previous messages
        this.successMessage.style.display = 'none';
        this.errorMessage.style.display = 'none';
        
        // Validate form
        if (!this.form.checkValidity()) {
          this.form.reportValidity();
          return;
        }
        
        // Disable button to prevent multiple submissions
        this.submitButton.disabled = true;
        
        // Collect form data
        const formData = {
          firstName: this.querySelector('#first-name-{{ ai_gen_id }}').value,
          lastName: this.querySelector('#last-name-{{ ai_gen_id }}').value,
          phone: this.querySelector('#phone-{{ ai_gen_id }}').value,
          email: this.querySelector('#email-{{ ai_gen_id }}').value,
          date: this.querySelector('#date-{{ ai_gen_id }}').value
        };
        
        // In a real implementation, you would send this data to a booking API or endpoint
        // For this example, we'll simulate a successful booking
        setTimeout(() => {
          // Show success message
          this.successMessage.style.display = 'block';
          
          // Reset form
          this.form.reset();
          
          // Re-enable button
          this.submitButton.disabled = false;
        }, 1000);
        
        // For demonstration purposes, log the booking data to console
        console.log('Booking request:', formData);
      }
    }
    
    customElements.define('booking-form-{{ ai_gen_id }}', BookingForm{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Booking Window",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Book an Appointment"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Fill out the form below to schedule your appointment with us.</p>"
    },
    {
      "type": "text",
      "id": "first_name_label",
      "label": "First name label",
      "default": "First Name"
    },
    {
      "type": "text",
      "id": "last_name_label",
      "label": "Last name label",
      "default": "Last Name"
    },
    {
      "type": "text",
      "id": "phone_label",
      "label": "Phone label",
      "default": "Phone Number"
    },
    {
      "type": "text",
      "id": "email_label",
      "label": "Email label",
      "default": "Email Address"
    },
    {
      "type": "text",
      "id": "date_label",
      "label": "Date label",
      "default": "Select Appointment Date"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "default": "Book an Appointment"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success message",
      "default": "Thank you! Your appointment request has been received. We'll contact you shortly to confirm."
    },
    {
      "type": "text",
      "id": "error_message",
      "label": "Error message",
      "default": "There was a problem submitting your request. Please try again."
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f9f9f9"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent color",
      "default": "#4a90e2"
    },
    {
      "type": "color",
      "id": "input_background_color",
      "label": "Input background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "input_border_color",
      "label": "Input border color",
      "default": "#dddddd"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "#4a90e2"
    },
    {
      "type": "color",
      "id": "button_hover_color",
      "label": "Button hover color",
      "default": "#3a7bc8"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "success_color",
      "label": "Success message color",
      "default": "#4caf50"
    },
    {
      "type": "color",
      "id": "error_color",
      "label": "Error message color",
      "default": "#f44336"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Form border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "input_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Input border radius",
      "default": 4
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Button border radius",
      "default": 4
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 16,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Heading size",
      "default": 24
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    }
  ],
  "presets": [
    {
      "name": "Booking Window"
    }
  ]
}
{% endschema %}