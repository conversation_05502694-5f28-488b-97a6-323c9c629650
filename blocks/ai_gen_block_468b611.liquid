{% doc %}
  @prompt
    Create a compact sitemap footer section that displays links to all website pages in an organized layout. The footer should be responsive and work well on all devices with a small, space-efficient design. Include an editable copyright text field at the bottom. The sitemap should automatically pull in all published pages and organize them in a clean, navigable format suitable for the footer area.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-container-{{ ai_gen_id }} {
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
  }

  .ai-sitemap-grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.column_gap }}px;
    margin-bottom: {{ block.settings.spacing }}px;
  }

  .ai-sitemap-column-{{ ai_gen_id }} {
    padding: {{ block.settings.column_padding }}px;
  }

  .ai-sitemap-title-{{ ai_gen_id }} {
    font-size: calc({{ block.settings.font_size }}px * 1.2);
    font-weight: 600;
    margin-top: 0;
    margin-bottom: {{ block.settings.spacing }}px;
    color: {{ block.settings.title_color }};
  }

  .ai-sitemap-list-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-list-{{ ai_gen_id }} li {
    margin-bottom: {{ block.settings.link_spacing }}px;
  }

  .ai-sitemap-list-{{ ai_gen_id }} a {
    text-decoration: none;
    color: {{ block.settings.link_color }};
    transition: color 0.2s ease;
    display: inline-block;
  }

  .ai-sitemap-list-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-sitemap-copyright-{{ ai_gen_id }} {
    text-align: center;
    padding-top: {{ block.settings.spacing }}px;
    border-top: 1px solid {{ block.settings.divider_color }};
    font-size: calc({{ block.settings.font_size }}px * 0.9);
    color: {{ block.settings.copyright_color }};
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
    
    .ai-sitemap-footer-{{ ai_gen_id }} {
      padding: calc({{ block.settings.padding }}px * 0.75);
      font-size: calc({{ block.settings.font_size }}px * 0.9);
    }
    
    .ai-sitemap-title-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.font_size }}px * 1.1);
    }
    
    .ai-sitemap-list-{{ ai_gen_id }} li {
      margin-bottom: calc({{ block.settings.link_spacing }}px * 0.8);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-container-{{ ai_gen_id }}">
    {% liquid
      assign all_pages = pages
      assign page_groups = all_pages | map: 'handle' | map: 'first' | uniq | sort
      assign pages_per_column = all_pages.size | divided_by: block.settings.columns_desktop | ceil
      
      if block.settings.group_by_first_letter
        assign grouped_pages = all_pages | group_by: 'handle' | map: 'first' | sort
      else
        assign grouped_pages = all_pages | sort: 'title'
      endif
    %}

    <div class="ai-sitemap-grid-{{ ai_gen_id }}">
      {% if block.settings.group_by_first_letter %}
        {% for letter in page_groups %}
          {% assign letter_pages = all_pages | where_exp: 'page', 'page.handle contains letter' | sort: 'title' %}
          {% if letter_pages.size > 0 %}
            <div class="ai-sitemap-column-{{ ai_gen_id }}">
              <h3 class="ai-sitemap-title-{{ ai_gen_id }}">{{ letter | upcase }}</h3>
              <ul class="ai-sitemap-list-{{ ai_gen_id }}">
                {% for page in letter_pages %}
                  <li><a href="{{ page.url }}">{{ page.title }}</a></li>
                {% endfor %}
              </ul>
            </div>
          {% endif %}
        {% endfor %}
      {% else %}
        {% assign column_count = block.settings.columns_desktop %}
        {% for i in (1..column_count) %}
          {% assign start_index = forloop.index0 | times: pages_per_column %}
          {% assign end_index = start_index | plus: pages_per_column | minus: 1 %}<div class="ai-sitemap-column-{{ ai_gen_id }}">
            {% if i == 1 and block.settings.column_1_title != blank %}
              <h3 class="ai-sitemap-title-{{ ai_gen_id }}">{{ block.settings.column_1_title }}</h3>
            {% elsif i == 2 and block.settings.column_2_title != blank %}
              <h3 class="ai-sitemap-title-{{ ai_gen_id }}">{{ block.settings.column_2_title }}</h3>
            {% elsif i == 3 and block.settings.column_3_title != blank %}
              <h3 class="ai-sitemap-title-{{ ai_gen_id }}">{{ block.settings.column_3_title }}</h3>
            {% elsif i == 4 and block.settings.column_4_title != blank %}
              <h3 class="ai-sitemap-title-{{ ai_gen_id }}">{{ block.settings.column_4_title }}</h3>
            {% endif %}
            
            <ul class="ai-sitemap-list-{{ ai_gen_id }}">
              {% for page in grouped_pages offset: start_index limit: pages_per_column %}
                <li><a href="{{ page.url }}">{{ page.title }}</a></li>
              {% endfor %}
            </ul>
          </div>
        {% endfor %}
      {% endif %}
    </div>
    
    {% if block.settings.copyright_text != blank %}
      <div class="ai-sitemap-copyright-{{ ai_gen_id }}">
        {{ block.settings.copyright_text }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Sitemap Footer",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "checkbox",
      "id": "group_by_first_letter",
      "label": "Group pages by first letter",
      "default": false,
      "info": "When enabled, pages are grouped by their first letter. When disabled, pages are distributed evenly across columns."
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 4,
      "label": "Number of columns (desktop)"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Number of columns (mobile)",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 800,
      "max": 1600,
      "step": 100,
      "default": 1200,
      "unit": "px",
      "label": "Maximum width"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "default": 30,
      "unit": "px",
      "label": "Padding"
    },
    {
      "type": "range",
      "id": "column_gap",
      "min": 10,
      "max": 60,
      "step": 5,
      "default": 30,
      "unit": "px",
      "label": "Column gap"
    },
    {
      "type": "range",
      "id": "column_padding",
      "min": 0,
      "max": 30,
      "step": 5,
      "default": 10,
      "unit": "px",
      "label": "Column padding"
    },
    {
      "type": "range",
      "id": "spacing",
      "min": 5,
      "max": 40,
      "step": 5,
      "default": 20,
      "unit": "px",
      "label": "Vertical spacing"
    },
    {
      "type": "range",
      "id": "link_spacing",
      "min": 2,
      "max": 20,
      "step": 1,
      "default": 8,
      "unit": "px",
      "label": "Link spacing"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "default": 14,
      "unit": "px",
      "label": "Font size"
    },
    {
      "type": "header",
      "content": "Column Titles"
    },
    {
      "type": "text",
      "id": "column_1_title",
      "label": "Column 1 title"
    },
    {
      "type": "text",
      "id": "column_2_title",
      "label": "Column 2 title"
    },
    {
      "type": "text",
      "id": "column_3_title",
      "label": "Column 3 title"
    },
    {
      "type": "text",
      "id": "column_4_title",
      "label": "Column 4 title"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "textarea",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "© 2023 Your Store. All rights reserved."
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright text color",
      "default": "#777777"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    }
  ],
  "presets": [
    {
      "name": "Sitemap Footer"
    }
  ]
}
{% endschema %}