{% doc %}
  @prompt
    Create an interactive before/after image slider for dental results that allows users to drag a handle to reveal the "after" image over the "before" image. The slider should be touch-friendly for mobile devices and include a draggable divider line with a handle that visitors can move left and right to compare the two images seamlessly.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .before-after-slider-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;}

  .before-after-slider__container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: {{ block.settings.aspect_ratio }}%;
  }

  .before-after-slider__image-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;}

  .before-after-slider__image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .before-after-slider__image-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .before-after-slider__image-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
  }

  .before-after-slider__after-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
  }

  .before-after-slider__divider-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 50%;
    width: {{ block.settings.divider_width }}px;
    height: 100%;
    background-color: {{ block.settings.divider_color }};
    transform: translateX(-50%);
    z-index: 2;
  }

  .before-after-slider__handle-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: {{ block.settings.handle_size }}px;
    height: {{ block.settings.handle_size }}px;
    background-color: {{ block.settings.handle_color }};
    border: {{ block.settings.handle_border_width }}px solid {{ block.settings.handle_border_color }};
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 3;box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .before-after-slider__handle-icon-{{ ai_gen_id }} {
    width: 50%;
    height: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .before-after-slider__handle-icon-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    fill: {{ block.settings.handle_icon_color }};
  }

  .before-after-slider__label-{{ ai_gen_id }} {
    position: absolute;
    bottom: 10px;
    padding: 5px 10px;
    background-color: {{ block.settings.label_background_color }};
    color: {{ block.settings.label_text_color }};
    font-size: {{ block.settings.label_font_size }}px;
    border-radius: 4px;
    z-index: 2;
    pointer-events: none;
  }

  .before-after-slider__before-label-{{ ai_gen_id }} {
    left: 10px;
  }

  .before-after-slider__after-label-{{ ai_gen_id }} {
    right: 10px;
  }

  @media screen and (max-width: 749px) {
    .before-after-slider__handle-{{ ai_gen_id }} {
      width: {{ block.settings.handle_size | times: 0.8 }}px;
      height: {{ block.settings.handle_size | times: 0.8 }}px;
    }
    .before-after-slider__label-{{ ai_gen_id }} {
      font-size: {{ block.settings.label_font_size | times: 0.8 }}px;
    }
  }
{% endstyle %}

<dental-before-after-slider-{{ ai_gen_id }}
  class="before-after-slider-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="before-after-slider__container-{{ ai_gen_id }}">
    <div class="before-after-slider__image-container-{{ ai_gen_id }}">
      {% if block.settings.before_image %}
        <img 
          src="{{ block.settings.before_image | image_url: width: 2000 }}"
          alt="{{ block.settings.before_image.alt | escape | default: 'Before dental treatment' }}"
          loading="lazy"
          class="before-after-slider__image-{{ ai_gen_id }}"
        >
      {% else %}
        <div class="before-after-slider__image-placeholder-{{ ai_gen_id }}">
          {{ 'image' | placeholder_svg_tag }}
        </div>
      {% endif %}
    </div>
    <div class="before-after-slider__after-container-{{ ai_gen_id }}">
      {% if block.settings.after_image %}
        <img 
          src="{{ block.settings.after_image | image_url: width: 2000 }}"
          alt="{{ block.settings.after_image.alt | escape | default: 'After dental treatment' }}"
          loading="lazy"
          class="before-after-slider__image-{{ ai_gen_id }}"
        >
      {% else %}
        <div class="before-after-slider__image-placeholder-{{ ai_gen_id }}">
          {{ 'image' | placeholder_svg_tag }}
        </div>
      {% endif %}
    </div>
    
    <div class="before-after-slider__divider-{{ ai_gen_id }}"></div><div class="before-after-slider__handle-{{ ai_gen_id }}">
      <div class="before-after-slider__handle-icon-{{ ai_gen_id }}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
          <path d="M14.59 16.59L19.17 12 14.59 7.41 16 6l6 6-6 6-1.41-1.41z" style="transform: scaleX(-1); transform-origin: center;"/>
        </svg>
      </div>
    </div>
    
    {% if block.settings.show_labels %}
      <div class="before-after-slider__label-{{ ai_gen_id }} before-after-slider__before-label-{{ ai_gen_id }}">
        {{ block.settings.before_label }}
      </div>
      <div class="before-after-slider__label-{{ ai_gen_id }} before-after-slider__after-label-{{ ai_gen_id }}">
        {{ block.settings.after_label }}
      </div>
    {% endif %}
  </div>
</dental-before-after-slider-{{ ai_gen_id }}>

<script>
  (function() {
    class DentalBeforeAfterSlider extends HTMLElement {
      constructor() {
        super();
        this.slider = this;
        this.container = this.querySelector('.before-after-slider__container-{{ ai_gen_id }}');
        this.afterContainer = this.querySelector('.before-after-slider__after-container-{{ ai_gen_id }}');
        this.divider = this.querySelector('.before-after-slider__divider-{{ ai_gen_id }}');
        this.handle = this.querySelector('.before-after-slider__handle-{{ ai_gen_id }}');
        this.isDragging = false;
        this.startX = 0;
        this.startPosition = 0;}

      connectedCallback() {
        this.setupEventListeners();
      }

      setupEventListeners() {
        // Mouse events
        this.handle.addEventListener('mousedown', this.startDrag.bind(this));
        document.addEventListener('mousemove', this.drag.bind(this));
        document.addEventListener('mouseup', this.stopDrag.bind(this));
        
        // Touch events for mobile
        this.handle.addEventListener('touchstart', this.startDrag.bind(this), { passive: false });
        document.addEventListener('touchmove', this.drag.bind(this), { passive: false });
        document.addEventListener('touchend', this.stopDrag.bind(this));
        
        // Click anywhere on slider to move handle
        this.container.addEventListener('click', this.moveToClick.bind(this));
        // Prevent image dragging
        const images = this.querySelectorAll('img');
        images.forEach(img => {
          img.addEventListener('dragstart', e => e.preventDefault());
        });
        
        // Initial position
        this.setPosition(50);
      }

      startDrag(e) {
        e.preventDefault();
        this.isDragging = true;
        this.startX = e.clientX || (e.touches && e.touches[0].clientX);
        this.startPosition = parseFloat(this.afterContainer.style.width) || 50;
        
        this.handle.style.transition = 'none';
        this.divider.style.transition = 'none';
        this.afterContainer.style.transition = 'none';
      }

      drag(e) {
        if (!this.isDragging) return;
        e.preventDefault();
        
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        if (!clientX) return;
        
        const rect = this.slider.getBoundingClientRect();
        const offsetX = clientX - rect.left;
        const percentage = Math.min(Math.max(offsetX / rect.width * 100, 0), 100);
        
        this.setPosition(percentage);
      }

      stopDrag() {
        this.isDragging = false;
        this.handle.style.transition = 'transform 0.3s ease';
        this.divider.style.transition = 'left 0.3s ease';
        this.afterContainer.style.transition = 'width 0.3s ease';
      }

      moveToClick(e) {
        if (e.target === this.handle) return;
        
        const rect = this.slider.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const percentage = offsetX / rect.width * 100;
        
        this.setPosition(percentage);
      }

      setPosition(percentage) {
        this.afterContainer.style.width = `${percentage}%`;
        this.divider.style.left = `${percentage}%`;
        this.handle.style.left = `${percentage}%`;
      }
    }

    customElements.define('dental-before-after-slider-{{ ai_gen_id }}', DentalBeforeAfterSlider);
  })();
</script>

{% schema %}
{
  "name": "Dental Before/After",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "image_picker",
      "id": "before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image",
      "label": "After image"
    },
    {
      "type": "range",
      "id": "aspect_ratio",
      "min": 30,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Image aspect ratio",
      "default": 75
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Divider & Handle"
    },
    {
      "type": "range",
      "id": "divider_width",
      "min": 1,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Divider width",
      "default": 2
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "handle_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Handle size",
      "default": 40
    },
    {
      "type": "color",
      "id": "handle_color",
      "label": "Handle color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "handle_border_width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Handle border width",
      "default": 2
    },
    {
      "type": "color",
      "id": "handle_border_color",
      "label": "Handle border color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "handle_icon_color",
      "label": "Handle icon color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Labels"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "range",
      "id": "label_font_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Label font size",
      "default": 14
    },
    {
      "type": "color",
      "id": "label_background_color",
      "label": "Label background color",
      "default": "rgba(0, 0, 0, 0.7)"
    },
    {
      "type": "color",
      "id": "label_text_color",
      "label": "Label text color",
      "default": "#ffffff"
    }
  ],
  "presets": [
    {
      "name": "Dental Before/After"
    }
  ]
}
{% endschema %}