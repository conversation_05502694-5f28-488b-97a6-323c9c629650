{% doc %}
  @prompt
    Create three wedding page sections: 1) A full-width responsive video hero section that maintains proper aspect ratio without distortion, clean and minimal design with no additional elements. 2) A section with image on the right and text on the left, with a soft pink background decorated with beautiful flowers or petals. 3) A mirrored version of section 2 with image on the left and text on the right, same soft pink floral background. All sections should be elegant and wedding-themed with proper spacing and mobile responsiveness.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-hero-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100vh;
    min-height: 400px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-video-hero-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .ai-video-hero-video-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
  }

  .ai-video-hero-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f4f0 0%, #f0e6e0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-video-hero-placeholder-{{ ai_gen_id }} svg {
    width: 120px;
    height: 120px;
    opacity: 0.3;
    color: #d4a574;
  }

  @media screen and (max-width: 768px) {
    .ai-video-hero-{{ ai_gen_id }} {
      height: 60vh;
      min-height: 300px;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-video-hero-{{ ai_gen_id }} {
      height: 50vh;
      min-height: 250px;
    }
  }
{% endstyle %}

<div class="ai-video-hero-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-video-hero-container-{{ ai_gen_id }}">
    {% if block.settings.video_url != blank %}
      <video
        class="ai-video-hero-video-{{ ai_gen_id }}"
        autoplay
        muted
        loop
        playsinline
        preload="metadata"
      >
        <source src="{{ block.settings.video_url }}" type="video/mp4">
      </video>
    {% else %}
      <div class="ai-video-hero-placeholder-{{ ai_gen_id }}">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polygon points="5,3 19,12 5,21"></polygon>
        </svg>
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Wedding Video Hero",
  "settings": [
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "url",
      "id": "video_url",
      "label": "Video URL"
    }
  ],
  "presets": [
    {
      "name": "Wedding Video Hero"
    }
  ],
  "tag": null
}
{% endschema %}