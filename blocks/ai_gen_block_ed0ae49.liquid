{% doc %}
  @prompt
    Create floating social media icons for WhatsApp, Instagram, Facebook, and TikTok that stay fixed on the right side of the screen as users scroll down the page

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-social-icons-{{ ai_gen_id }} {
    position: fixed;
    right: {{ block.settings.distance_from_edge }}px;
    top:50%;
    transform: translateY(-50%);
    z-index: 999;
    display: flex;
    flex-direction: column;
    gap: {{ block.settings.spacing }}px;
  }

  .ai-social-icon-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    border-radius: {{ block.settings.border_radius }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.icon_color }};
    text-decoration: none;
    transition: transform 0.3s ease, background-color 0.3s ease;box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .ai-social-icon-{{ ai_gen_id }}:hover {
    transform: scale(1.1);
    background-color: {{ block.settings.hover_background_color }};
    color: {{ block.settings.hover_icon_color }};
  }

  .ai-social-icon-{{ ai_gen_id }} svg {
    width: calc({{ block.settings.icon_size }}px * 0.6);
    height: calc({{ block.settings.icon_size }}px * 0.6);
  }

  @media screen and (max-width: 749px) {
    .ai-social-icons-{{ ai_gen_id }} {
      right: {{ block.settings.mobile_distance_from_edge }}px;
    }
    
    .ai-social-icon-{{ ai_gen_id }} {
      width: {{ block.settings.mobile_icon_size }}px;
      height: {{ block.settings.mobile_icon_size }}px;
    }
    
    .ai-social-icon-{{ ai_gen_id }} svg {
      width: calc({{ block.settings.mobile_icon_size }}px * 0.6);
      height: calc({{ block.settings.mobile_icon_size }}px * 0.6);
    }
  }
{% endstyle %}

<div class="ai-social-icons-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.whatsapp_number != blank %}
    <a href="https://wa.me/{{ block.settings.whatsapp_number | remove: ' ' | remove: '-' | remove: '(' | remove: ')' }}" target="_blank" rel="noopener" class="ai-social-icon-{{ ai_gen_id }}" aria-label="WhatsApp"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor">
        <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
      </svg>
    </a>
  {% endif %}

  {% if block.settings.instagram_url != blank %}
    <a href="{{ block.settings.instagram_url }}" target="_blank" rel="noopener" class="ai-social-icon-{{ ai_gen_id }}" aria-label="Instagram">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor">
        <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/>
      </svg>
    </a>
  {% endif %}

  {% if block.settings.facebook_url != blank %}
    <a href="{{ block.settings.facebook_url }}" target="_blank" rel="noopener" class="ai-social-icon-{{ ai_gen_id }}" aria-label="Facebook">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
        <path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/>
      </svg>
    </a>
  {% endif %}

  {% if block.settings.tiktok_url != blank %}
    <a href="{{ block.settings.tiktok_url }}" target="_blank" rel="noopener" class="ai-social-icon-{{ ai_gen_id }}" aria-label="TikTok">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor">
        <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
      </svg>
    </a>
  {% endif %}
</div>

{% schema %}
{
  "name": "Floating Social Icons",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Social Media Links"
    },
    {
      "type": "text",
      "id": "whatsapp_number",
      "label": "WhatsApp Number",
      "info": "Include country code (e.g.,15551234567)"
    },
    {
      "type": "url",
      "id": "instagram_url",
      "label": "Instagram URL"
    },
    {
      "type": "url",
      "id": "facebook_url",
      "label": "Facebook URL"
    },
    {
      "type": "url",
      "id": "tiktok_url",
      "label": "TikTok URL"
    },
    {
      "type": "header",
      "content": "Appearance"
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 30,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Icon size",
      "default": 45
    },
    {
      "type": "range",
      "id": "mobile_icon_size",
      "min": 25,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Mobile icon size",
      "default": 35
    },
    {
      "type": "range",
      "id": "spacing",
      "min": 5,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Spacing between icons",
      "default": 10
    },
    {
      "type": "range",
      "id": "distance_from_edge",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Distance from edge",
      "default": 20
    },
    {
      "type": "range",
      "id": "mobile_distance_from_edge",
      "min": 5,
      "max": 30,
      "step": 5,
      "unit": "px",
      "label": "Mobile distance from edge",
      "default": 10
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 50
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "hover_background_color",
      "label": "Hover background color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "hover_icon_color",
      "label": "Hover icon color",
      "default": "#ffffff"
    }
  ],
  "presets": [
    {
      "name": "Floating Social Icons"
    }
  ]
}
{% endschema %}