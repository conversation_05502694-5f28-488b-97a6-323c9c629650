{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
.compact-sitemap-{{ ai_gen_id }} {
  width: 100%;
  padding: {{ block.settings.padding }}px 0;
  background-color: {{ block.settings.background_color }};
  color: {{ block.settings.text_color }};
  font-size: {{ block.settings.font_size }}px;
}

.compact-sitemap__container-{{ ai_gen_id }} {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.compact-sitemap__grid-{{ ai_gen_id }} {
  display: grid;
  grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
  gap: 20px;
}

.compact-sitemap__column-{{ ai_gen_id }} h3 {
  font-size: {{ block.settings.heading_size }}px;
  margin: 0 0 15px 0;
  color: {{ block.settings.heading_color }};
  font-weight: 600;
}

.compact-sitemap__links-{{ ai_gen_id }} {
  list-style: none;
  padding: 0;
  margin: 0;
}

.compact-sitemap__links-{{ ai_gen_id }} li {
  margin-bottom: 8px;
}

.compact-sitemap__links-{{ ai_gen_id }} a {
  text-decoration: none;
  color: {{ block.settings.link_color }};
  transition: color 0.2s ease;
}

.compact-sitemap__links-{{ ai_gen_id }} a:hover {
  color: {{ block.settings.link_hover_color }};
}

.compact-sitemap__copyright-{{ ai_gen_id }} {
  text-align: center;
  margin-top: 30px;
  padding-top: 15px;
  border-top: 1px solid {{ block.settings.divider_color }};
  font-size: {{ block.settings.copyright_size }}px;
  color: {{ block.settings.copyright_color }};
}

@media screen and (max-width: 749px) {
  .compact-sitemap__grid-{{ ai_gen_id }} {
    grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
  }
  
  .compact-sitemap__column-{{ ai_gen_id }} h3 {
    font-size: {{ block.settings.heading_size | minus: 2 }}px;
  }
}
{% endstyle %}

<div class="compact-sitemap-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="compact-sitemap__container-{{ ai_gen_id }}">
    <div class="compact-sitemap__grid-{{ ai_gen_id }}">
      {% if block.settings.show_pages %}
        <div class="compact-sitemap__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.pages_heading }}</h3>
          <ul class="compact-sitemap__links-{{ ai_gen_id }}">
            {% for link in linklists.main-menu.links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
      
      {% if block.settings.show_policies %}
        <div class="compact-sitemap__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.policies_heading }}</h3>
          <ul class="compact-sitemap__links-{{ ai_gen_id }}">
            {% if shop.privacy_policy %}
              <li><a href="{{ shop.privacy_policy.url }}">{{ shop.privacy_policy.title }}</a></li>
            {% endif %}
            {% if shop.refund_policy %}
              <li><a href="{{ shop.refund_policy.url }}">{{ shop.refund_policy.title }}</a></li>
            {% endif %}
            {% if shop.shipping_policy %}
              <li><a href="{{ shop.shipping_policy.url }}">{{ shop.shipping_policy.title }}</a></li>
            {% endif %}
            {% if shop.terms_of_service %}
              <li><a href="{{ shop.terms_of_service.url }}">{{ shop.terms_of_service.title }}</a></li>
            {% endif %}
          </ul>
        </div>
      {% endif %}
      
      {% if block.settings.show_collections %}
        <div class="compact-sitemap__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.collections_heading }}</h3>
          <ul class="compact-sitemap__links-{{ ai_gen_id }}">
            {% for collection in collections limit: block.settings.collections_limit %}
              <li><a href="{{ collection.url }}">{{ collection.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
      
      {% if block.settings.show_footer_menu and linklists[block.settings.footer_menu].links.size > 0 %}
        <div class="compact-sitemap__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.footer_menu_heading }}</h3>
          <ul class="compact-sitemap__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.footer_menu].links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>
    
    {% if block.settings.show_copyright %}
      <div class="compact-sitemap__copyright-{{ ai_gen_id }}">
        {{ block.settings.copyright_text | replace: '[year]', 'now' | date: '%Y' }}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 4,
      "step": 1,
      "label": "Columns on desktop",
      "default": 4
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "checkbox",
      "id": "show_pages",
      "label": "Show main menu links",
      "default": true
    },
    {
      "type": "text",
      "id": "pages_heading",
      "label": "Pages heading",
      "default": "Pages"
    },
    {
      "type": "checkbox",
      "id": "show_policies",
      "label": "Show policy pages",
      "default": true
    },
    {
      "type": "text",
      "id": "policies_heading",
      "label": "Policies heading",
      "default": "Policies"
    },
    {
      "type": "checkbox",
      "id": "show_collections",
      "label": "Show collections",
      "default": true
    },
    {
      "type": "text",
      "id": "collections_heading",
      "label": "Collections heading",
      "default": "Collections"
    },
    {
      "type": "range",
      "id": "collections_limit",
      "min": 3,
      "max": 10,
      "step": 1,
      "label": "Collections limit",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "show_footer_menu",
      "label": "Show footer menu",
      "default": true
    },
    {
      "type": "link_list",
      "id": "footer_menu",
      "label": "Footer menu",
      "default": "footer"
    },
    {
      "type": "text",
      "id": "footer_menu_heading",
      "label": "Footer menu heading",
      "default": "Quick links"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "checkbox",
      "id": "show_copyright",
      "label": "Show copyright",
      "default": true
    },
    {
      "type": "textarea",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "© [year] Your Store. All rights reserved.",
      "info": "Use [year] to display current year"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 14
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 14,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Heading size",
      "default": 16
    },
    {
      "type": "range",
      "id": "copyright_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Copyright size",
      "default": 12
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright color",
      "default": "#777777"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}