{% doc %}
  @prompt
    Create a VIP premium style hero section block for MICE tourism page with elegant dark color scheme (deep navy/black with gold accents), luxury typography, high-end spacing, and sophisticated layout. Include a compelling headline about exclusive corporate travel experiences and an elegant call-to-action button. The design should feel ultra-luxurious and professional for high-end business clients. Do not include any background image upload option - use only solid colors and gradients for the background. Make sure the description text color is white for good visibility on dark background.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .vip-hero-{{ ai_gen_id }} {
    position: relative;
    padding: {{ block.settings.section_padding }}px 0;
    background: {{ block.settings.background_color }};
    background-image: {{ block.settings.enable_gradient | default: false | json }} ? 
      linear-gradient({{ block.settings.gradient_angle }}deg, {{ block.settings.gradient_start_color }}, {{ block.settings.gradient_end_color }}) : 
      none;
    overflow: hidden;
  }

  .vip-hero__container-{{ ai_gen_id }} {
    max-width: {{ block.settings.content_width }}px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
  }

  .vip-hero__content-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    align-items: {{ block.settings.content_alignment }};
    text-align: {{ block.settings.text_alignment }};
  }

  .vip-hero__eyebrow-{{ ai_gen_id }} {
    font-size: {{ block.settings.eyebrow_size }}px;
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: {{ block.settings.accent_color }};
    margin-bottom: 16px;
    font-family: var(--font-heading-family);
  }

  .vip-hero__heading-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    line-height: 1.1;
    font-weight: 700;
    color: {{ block.settings.heading_color }};
    margin: 0 0 24px;
    max-width: {{ block.settings.heading_max_width }}px;
    font-family: var(--font-heading-family);
  }

  .vip-hero__description-{{ ai_gen_id }} {
    font-size: {{ block.settings.description_size }}px;
    line-height: 1.6;
    color: {{ block.settings.description_color }};
    margin: 0 0 40px;
    max-width: {{ block.settings.description_max_width }}px;
  }

  .vip-hero__cta-{{ ai_gen_id }} {
    display: inline-block;
    padding: {{ block.settings.button_padding_vertical }}px {{ block.settings.button_padding_horizontal }}px;
    background-color: {{ block.settings.button_background }};
    color: {{ block.settings.button_text_color }};
    font-size: {{ block.settings.button_text_size }}px;
    font-weight: 600;
    text-decoration: none;
    border-radius: {{ block.settings.button_border_radius }}px;
    border: 2px solid {{ block.settings.button_border_color }};
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
    text-transform: {{ block.settings.button_text_transform }};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .vip-hero__cta-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_background }};
    color: {{ block.settings.button_hover_text_color }};
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  }

  .vip-hero__accent-line-{{ ai_gen_id }} {
    height: 3px;
    width: 60px;
    background-color: {{ block.settings.accent_color }};
    margin: 0 0 30px;
    display: {{ block.settings.show_accent_line | default: true | json }} ? 'block' : 'none';
  }

  .vip-hero__decorative-element-{{ ai_gen_id }} {
    position: absolute;
    width: 300px;
    height: 300px;
    border: 1px solid {{ block.settings.accent_color }};
    opacity: 0.1;
    border-radius: 50%;
    display: {{ block.settings.show_decorative_elements | default: true | json }} ? 'block' : 'none';
  }

  .vip-hero__decorative-element-1-{{ ai_gen_id }} {
    top: -100px;
    right: -100px;
  }

  .vip-hero__decorative-element-2-{{ ai_gen_id }} {
    bottom: -150px;
    left: -150px;
    width: 400px;
    height: 400px;
  }

  @media screen and (max-width: 989px) {
    .vip-hero__heading-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.heading_size }}px * 0.8);
    }

    .vip-hero__description-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.description_size }}px * 0.9);
    }
  }

  @media screen and (max-width: 749px) {
    .vip-hero-{{ ai_gen_id }} {
      padding: calc({{ block.settings.section_padding }}px * 0.7) 0;
    }

    .vip-hero__heading-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.heading_size }}px * 0.6);
    }

    .vip-hero__description-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.description_size }}px * 0.8);
    }

    .vip-hero__eyebrow-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.eyebrow_size }}px * 0.9);
    }
  }
{% endstyle %}

<div class="vip-hero-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.show_decorative_elements %}
    <div class="vip-hero__decorative-element-{{ ai_gen_id }} vip-hero__decorative-element-1-{{ ai_gen_id }}"></div>
    <div class="vip-hero__decorative-element-{{ ai_gen_id }} vip-hero__decorative-element-2-{{ ai_gen_id }}"></div>
  {% endif %}
  
  <div class="vip-hero__container-{{ ai_gen_id }}">
    <div class="vip-hero__content-{{ ai_gen_id }}">
      {% if block.settings.eyebrow_text != blank %}
        <div class="vip-hero__eyebrow-{{ ai_gen_id }}">{{ block.settings.eyebrow_text }}</div>
      {% endif %}
      
      <h1 class="vip-hero__heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h1>
      
      {% if block.settings.show_accent_line %}
        <div class="vip-hero__accent-line-{{ ai_gen_id }}"></div>
      {% endif %}
      
      {% if block.settings.description != blank %}
        <div class="vip-hero__description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
      {% endif %}
      
      {% if block.settings.button_text != blank and block.settings.button_link != blank %}
        <a href="{{ block.settings.button_link }}" class="vip-hero__cta-{{ ai_gen_id }}">
          {{ block.settings.button_text }}
        </a>
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "VIP MICE Hero",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "eyebrow_text",
      "label": "Eyebrow text",
      "default": "LUXURY CORPORATE EVENTS"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Elevate Your Corporate Experience with Exclusive MICE Solutions"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Discover unparalleled corporate event experiences tailored for discerning business leaders. Our premium MICE services combine sophistication with meticulous attention to detail for unforgettable executive gatherings.</p>"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "Explore Premium Services"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "header",
      "content": "Style & Layout"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#0a0e17"
    },
    {
      "type": "checkbox",
      "id": "enable_gradient",
      "label": "Enable gradient background",
      "default": true
    },
    {
      "type": "color",
      "id": "gradient_start_color",
      "label": "Gradient start color",
      "default": "#0a0e17"
    },
    {
      "type": "color",
      "id": "gradient_end_color",
      "label": "Gradient end color",
      "default": "#131c2e"
    },
    {
      "type": "range",
      "id": "gradient_angle",
      "min": 0,
      "max": 360,
      "step": 5,
      "unit": "°",
      "label": "Gradient angle",
      "default": 135
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent color",
      "default": "#c9a55c"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#ffffff"
    },
    {
      "type": "select",
      "id": "content_alignment",
      "label": "Content alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 60,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Section padding",
      "default": 120
    },
    {
      "type": "range",
      "id": "content_width",
      "min": 600,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Content width",
      "default": 900
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "eyebrow_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Eyebrow text size",
      "default": 14
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 32,
      "max": 80,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 56
    },
    {
      "type": "range",
      "id": "heading_max_width",
      "min": 400,
      "max": 1000,
      "step": 50,
      "unit": "px",
      "label": "Heading max width",
      "default": 800
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 14,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 18
    },
    {
      "type": "range",
      "id": "description_max_width",
      "min": 400,
      "max": 1000,
      "step": 50,
      "unit": "px",
      "label": "Description max width",
      "default": 700
    },
    {
      "type": "header",
      "content": "Button Styling"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#c9a55c"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#0a0e17"
    },
    {
      "type": "color",
      "id": "button_hover_background",
      "label": "Button hover background",
      "default": "#d8b978"
    },
    {
      "type": "color",
      "id": "button_hover_text_color",
      "label": "Button hover text color",
      "default": "#0a0e17"
    },
    {
      "type": "color",
      "id": "button_border_color",
      "label": "Button border color",
      "default": "#c9a55c"
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Button border radius",
      "default": 2
    },
    {
      "type": "range",
      "id": "button_padding_vertical",
      "min": 10,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Button vertical padding",
      "default": 16
    },
    {
      "type": "range",
      "id": "button_padding_horizontal",
      "min": 15,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Button horizontal padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "button_text_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Button text size",
      "default": 15
    },
    {
      "type": "select",
      "id": "button_text_transform",
      "label": "Button text transform",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "uppercase",
          "label": "Uppercase"
        }
      ],
      "default": "uppercase"
    },
    {
      "type": "header",
      "content": "Decorative Elements"
    },
    {
      "type": "checkbox",
      "id": "show_accent_line",
      "label": "Show accent line",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_decorative_elements",
      "label": "Show decorative elements",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "VIP MICE Hero"
    }
  ],
  "tag": null
}
{% endschema %}