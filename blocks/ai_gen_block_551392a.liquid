{% doc %}
  @prompt
    Create an image gallery section with scroll reveal animation. Include two image picker settings that properly display uploaded images using Shopify's image_url filter. Add fallback placeholder images, proper image loading with alt text, and ensure images are visible immediately after upload. Use CSS transforms for smooth scroll-triggered transitions between the two images with proper responsive design.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-image-gallery-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-image-gallery-container-{{ ai_gen_id }} {
    position: relative;
    display: flex;
    flex-direction: {{ block.settings.layout }};
    gap: {{ block.settings.gap }}px;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: {{ block.settings.image_border_radius }}px;
    box-shadow: 0 {{ block.settings.shadow_y_offset }}px {{ block.settings.shadow_blur }}px rgba(0, 0, 0, {{ block.settings.shadow_opacity | divided_by: 100.0 }});
    opacity: 0;
    transform: translateY({{ block.settings.animation_distance }}px);
    transition: opacity {{ block.settings.animation_duration }}s ease, transform {{ block.settings.animation_duration }}s ease;
  }

  .ai-image-wrapper-{{ ai_gen_id }}.ai-revealed-{{ ai_gen_id }} {
    opacity: 1;
    transform: translateY(0);
  }

  .ai-image-wrapper-{{ ai_gen_id }}:nth-child(2) {
    transition-delay: {{ block.settings.animation_delay }}s;
  }

  .ai-image-{{ ai_gen_id }} {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    aspect-ratio: {{ block.settings.aspect_ratio }};
  }

  .ai-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
    aspect-ratio: {{ block.settings.aspect_ratio }};
  }

  .ai-image-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
  }

  @media screen and (max-width: 749px) {
    .ai-image-gallery-container-{{ ai_gen_id }} {
      flex-direction: column;
    }
  }
{% endstyle %}

<div class="ai-image-gallery-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-image-gallery-container-{{ ai_gen_id }}">
    <div class="ai-image-wrapper-{{ ai_gen_id }}">
      {% if block.settings.image_1 %}
        <img
          src="{{ block.settings.image_1 | image_url: width: 1500 }}"
          srcset="
            {{ block.settings.image_1 | image_url: width: 400 }} 400w,
            {{ block.settings.image_1 | image_url: width: 800 }} 800w,
            {{ block.settings.image_1 | image_url: width: 1200 }} 1200w,
            {{ block.settings.image_1 | image_url: width: 1500 }} 1500w
          "
          sizes="(min-width: 750px) 50vw, 100vw"
          loading="lazy"
          alt="{{ block.settings.image_1.alt | escape | default: 'Gallery image 1' }}"
          width="{{ block.settings.image_1.width }}"
          height="{{ block.settings.image_1.height }}"
          class="ai-image-{{ ai_gen_id }}"
        >
      {% else %}
        <div class="ai-image-placeholder-{{ ai_gen_id }}">
          {{ 'image' | placeholder_svg_tag }}
        </div>
      {% endif %}
    </div>

    <div class="ai-image-wrapper-{{ ai_gen_id }}">
      {% if block.settings.image_2 %}
        <img
          src="{{ block.settings.image_2 | image_url: width: 1500 }}"
          srcset="
            {{ block.settings.image_2 | image_url: width: 400 }} 400w,
            {{ block.settings.image_2 | image_url: width: 800 }} 800w,
            {{ block.settings.image_2 | image_url: width: 1200 }} 1200w,
            {{ block.settings.image_2 | image_url: width: 1500 }} 1500w
          "
          sizes="(min-width: 750px) 50vw, 100vw"
          loading="lazy"
          alt="{{ block.settings.image_2.alt | escape | default: 'Gallery image 2' }}"
          width="{{ block.settings.image_2.width }}"
          height="{{ block.settings.image_2.height }}"
          class="ai-image-{{ ai_gen_id }}"
        >
      {% else %}
        <div class="ai-image-placeholder-{{ ai_gen_id }}">
          {{ 'image' | placeholder_svg_tag }}
        </div>
      {% endif %}
    </div>
  </div>
</div>

<script>
  (function() {
    class ScrollRevealGallery extends HTMLElement {
      constructor() {
        super();
        this.imageWrappers = this.querySelectorAll('.ai-image-wrapper-{{ ai_gen_id }}');
        this.observer = null;
      }

      connectedCallback() {
        this.setupIntersectionObserver();
      }

      disconnectedCallback() {
        if (this.observer) {
          this.observer.disconnect();
        }
      }

      setupIntersectionObserver() {
        const options = {
          root: null,
          rootMargin: '0px',
          threshold: 0.1
        };

        this.observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('ai-revealed-{{ ai_gen_id }}');
              this.observer.unobserve(entry.target);
            }
          });
        }, options);

        this.imageWrappers.forEach(wrapper => {
          this.observer.observe(wrapper);
        });
      }
    }

    customElements.define('scroll-reveal-gallery-{{ ai_gen_id }}', ScrollRevealGallery);

    // Initialize all galleries on the page
    document.querySelectorAll('.ai-image-gallery-{{ ai_gen_id }}').forEach(gallery => {
      new ScrollRevealGallery(gallery);
    });
  })();
</script>

{% schema %}
{
  "name": "Scroll Reveal Gallery",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "First image"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Second image"
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Image aspect ratio",
      "options": [
        {
          "value": "1/1",
          "label": "1:1 (Square)"
        },
        {
          "value": "3/2",
          "label": "3:2"
        },
        {
          "value": "4/3",
          "label": "4:3"
        },
        {
          "value": "16/9",
          "label": "16:9"
        },
        {
          "value": "2/3",
          "label": "2:3"
        }
      ],
      "default": "3/2"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "row",
          "label": "Horizontal"
        },
        {
          "value": "column",
          "label": "Vertical"
        }
      ],
      "default": "row"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Gap between images",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Container padding",
      "default": 0
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Container border radius",
      "default": 0
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Shadow"
    },
    {
      "type": "range",
      "id": "shadow_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Shadow opacity",
      "default": 0
    },
    {
      "type": "range",
      "id": "shadow_y_offset",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Shadow vertical offset",
      "default": 4
    },
    {
      "type": "range",
      "id": "shadow_blur",
      "min": 0,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Shadow blur",
      "default": 10
    },
    {
      "type": "header",
      "content": "Animation"
    },
    {
      "type": "range",
      "id": "animation_distance",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Animation distance",
      "default": 30
    },
    {
      "type": "range",
      "id": "animation_duration",
      "min": 0.1,
      "max": 2,
      "step": 0.1,
      "unit": "s",
      "label": "Animation duration",
      "default": 0.8
    },
    {
      "type": "range",
      "id": "animation_delay",
      "min": 0,
      "max": 1,
      "step": 0.1,
      "unit": "s",
      "label": "Second image delay",
      "default": 0.2
    }
  ],
  "presets": [
    {
      "name": "Scroll Reveal Gallery"
    }
  ]
}
{% endschema %}