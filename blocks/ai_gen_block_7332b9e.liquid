{% doc %}
  @prompt
    Fix dropdown navigation menu overflow issue where menu items extend beyond window boundaries. Create CSS to properly size dropdown menus and handle text wrapping for long menu item names.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
.dropdown-menu-fix-{{ ai_gen_id }} {
  position: relative;
  display: inline-block;
}

.dropdown-menu-fix-{{ ai_gen_id }} .dropdown-content {
  max-width: {{ block.settings.max_dropdown_width }}px;
  min-width: {{ block.settings.min_dropdown_width }}px;
  max-height: {{ block.settings.max_dropdown_height }}px;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: {{ block.settings.dropdown_border_radius }}px;
  background-color: {{ block.settings.dropdown_background_color }};
  border: {{ block.settings.dropdown_border_width }}px solid {{ block.settings.dropdown_border_color }};
}

.dropdown-menu-fix-{{ ai_gen_id }} .dropdown-item {
  padding: {{ block.settings.item_padding }}px;
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.4;
  color: {{ block.settings.text_color }};
  font-size: {{ block.settings.font_size }}px;
  border-bottom: {{ block.settings.item_border_width }}px solid {{ block.settings.item_border_color }};
  transition: background-color 0.2s ease;
}

.dropdown-menu-fix-{{ ai_gen_id }} .dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-menu-fix-{{ ai_gen_id }} .dropdown-item:hover {
  background-color: {{ block.settings.item_hover_background }};
  color: {{ block.settings.text_hover_color }};
}

.dropdown-menu-fix-{{ ai_gen_id }} .dropdown-content.position-left {
  left: 0;
}

.dropdown-menu-fix-{{ ai_gen_id }} .dropdown-content.position-center {
  left: 50%;
  transform: translateX(-50%);
}

.dropdown-menu-fix-{{ ai_gen_id }} .dropdown-content.position-right {
  right: 0;
}

@media screen and (max-width: 749px) {
  .dropdown-menu-fix-{{ ai_gen_id }} .dropdown-content {
    max-width: calc(100vw - 30px);min-width: 200px;
  }
}
{% endstyle %}

<dropdown-menu-fixer-{{ ai_gen_id }} {{ block.shopify_attributes }}>
  <div class="dropdown-menu-fix-{{ ai_gen_id }}">
    <div class="dropdown-menu-fix-instructions">
      <p>This block applies styling to fix dropdown menu overflow issues. It doesn't create new menus but enhances existing ones.</p>
    </div></div>
</dropdown-menu-fixer-{{ ai_gen_id }}>

<script>
(function() {
  class DropdownMenuFixer{{ ai_gen_id }} extends HTMLElement {
    constructor() {
      super();
    }
    
    connectedCallback() {
      // Find all dropdown menus in the theme
      const dropdownMenus = document.querySelectorAll('header .dropdown, header .has-dropdown, nav .dropdown, nav .has-dropdown');
      
      dropdownMenus.forEach(menu => {
        // Find the dropdown content/submenu
        const dropdownContent = menu.querySelector('ul, .dropdown-menu, .submenu');
        
        if (dropdownContent) {
          // Add our custom classes
          dropdownContent.classList.add('dropdown-content');
          dropdownContent.classList.add('position-{{ block.settings.dropdown_position }}');
          
          // Add our custom classes to dropdown items
          const dropdownItems = dropdownContent.querySelectorAll('li, a, .dropdown-item');
          dropdownItems.forEach(item => {
            item.classList.add('dropdown-item');
          });
          
          // Handle positioning to prevent overflow
          this.setupPositionHandling(menu, dropdownContent);
        }
      });
    }
    
    setupPositionHandling(menu, dropdown) {
      const checkPosition = () => {
        const rect = dropdown.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        
        // If dropdown extends beyond right edge
        if (rect.right > viewportWidth) {
          dropdown.style.left = 'auto';
          dropdown.style.right = '0';
        }
        
        // If dropdown extends beyond left edge
        if (rect.left < 0) {
          dropdown.style.left = '0';
          dropdown.style.right = 'auto';
        }
      };
      
      // Check position when dropdown becomes visible
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.target === dropdown &&
              (mutation.attributeName === 'class' || 
               mutation.attributeName === 'style')) {
            
            const isVisible = window.getComputedStyle(dropdown).display !== 'none';
            if (isVisible) {
              checkPosition();}
          }
        });
      });
      
      observer.observe(dropdown, { attributes: true });
      
      // Also handle on hover for themes that use hover
      menu.addEventListener('mouseenter', () => {
        setTimeout(checkPosition, 10);
      });
    }
  }
  
  customElements.define('dropdown-menu-fixer-{{ ai_gen_id }}', DropdownMenuFixer{{ ai_gen_id }});
})();
</script>

{% schema %}
{
  "name": "Dropdown Menu Fix",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Dropdown Container"
    },
    {
      "type": "range",
      "id": "min_dropdown_width",
      "min": 150,
      "max": 400,
      "step": 10,
      "unit": "px",
      "label": "Minimum dropdown width",
      "default": 200
    },
    {
      "type": "range",
      "id": "max_dropdown_width",
      "min": 200,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Maximum dropdown width",
      "default": 300
    },
    {
      "type": "range",
      "id": "max_dropdown_height",
      "min": 100,
      "max": 800,
      "step": 50,
      "unit": "px",
      "label": "Maximum dropdown height",
      "default": 400
    },
    {
      "type": "select",
      "id": "dropdown_position",
      "label": "Dropdown alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "color",
      "id": "dropdown_background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "dropdown_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Border radius",
      "default": 4
    },
    {
      "type": "range",
      "id": "dropdown_border_width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Border width",
      "default": 1
    },
    {
      "type": "color",
      "id": "dropdown_border_color",
      "label": "Border color",
      "default": "#e8e8e8"
    },
    {
      "type": "header",
      "content": "Menu Items"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "text_hover_color",
      "label": "Text hover color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "item_padding",
      "min": 5,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Item padding",
      "default": 10
    },
    {
      "type": "range",
      "id": "item_border_width",
      "min": 0,
      "max": 3,
      "step": 1,
      "unit": "px",
      "label": "Item separator width",
      "default": 1
    },
    {
      "type": "color",
      "id": "item_border_color",
      "label": "Item separator color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "item_hover_background",
      "label": "Item hover background",
      "default": "#f9f9f9"
    }
  ],
  "presets": [
    {
      "name": "Dropdown Menu Fix"
    }
  ]
}
{% endschema %}