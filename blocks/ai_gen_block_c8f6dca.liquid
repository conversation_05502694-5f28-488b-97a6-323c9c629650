{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-sitemap-footer__columns-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.gap }}px;
    margin-bottom: {{ block.settings.gap }}px;
  }

  .ai-sitemap-footer__column-{{ ai_gen_id }} h3 {
    font-size: calc({{ block.settings.font_size }}px * 1.2);
    margin-top: 0;
    margin-bottom: 10px;
    color: {{ block.settings.heading_color }};
    font-weight: 600;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} li {
    margin-bottom: 6px;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} a {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
    display: inline-block;
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid {{ block.settings.divider_color }};
    font-size: calc({{ block.settings.font_size }}px * 0.9);
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__columns-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__columns-{{ ai_gen_id }}">
      {% if block.settings.show_pages %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.pages_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists.main-menu.links %}
              <li>
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_policies %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.policies_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for policy in shop.policies %}
              {% if policy != blank %}
                <li>
                  <a href="{{ policy.url }}">{{ policy.title }}</a>
                </li>
              {% endif %}
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_collections %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.collections_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for collection in collections limit: block.settings.collections_limit %}
              <li>
                <a href="{{ collection.url }}">{{ collection.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_blogs %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.blogs_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for blog in blogs %}
              <li>
                <a href="{{ blog.url }}">{{ blog.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.custom_links != blank %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3>{{ block.settings.custom_links_heading }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.custom_links].links %}
              <li>
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
      {% if block.settings.copyright_text != blank %}
        {{ block.settings.copyright_text }}
      {% else %}
        &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}. All rights reserved.
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "checkbox",
      "id": "show_pages",
      "label": "Show pages",
      "default": true
    },
    {
      "type": "text",
      "id": "pages_heading",
      "label": "Pages heading",
      "default": "Pages"
    },
    {
      "type": "checkbox",
      "id": "show_policies",
      "label": "Show policies",
      "default": true
    },
    {
      "type": "text",
      "id": "policies_heading",
      "label": "Policies heading",
      "default": "Policies"
    },
    {
      "type": "checkbox",
      "id": "show_collections",
      "label": "Show collections",
      "default": true
    },
    {
      "type": "text",
      "id": "collections_heading",
      "label": "Collections heading",
      "default": "Collections"
    },
    {
      "type": "range",
      "id": "collections_limit",
      "min": 3,
      "max": 12,
      "step": 1,
      "default": 6,
      "label": "Collections to show"
    },
    {
      "type": "checkbox",
      "id": "show_blogs",
      "label": "Show blogs",
      "default": true
    },
    {
      "type": "text",
      "id": "blogs_heading",
      "label": "Blogs heading",
      "default": "Blogs"
    },
    {
      "type": "link_list",
      "id": "custom_links",
      "label": "Custom menu",
      "info": "Optional additional menu"
    },
    {
      "type": "text",
      "id": "custom_links_heading",
      "label": "Custom menu heading",
      "default": "Links"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text",
      "info": "Leave blank to use default"
    },
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Desktop columns"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Mobile columns",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 50,
      "step": 5,
      "default": 20,
      "unit": "px",
      "label": "Padding"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 40,
      "step": 5,
      "default": 20,
      "unit": "px",
      "label": "Column gap"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "default": 14,
      "unit": "px",
      "label": "Font size"
    },
    {
      "type": "header",
      "content": "Color Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f3f3f3"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}