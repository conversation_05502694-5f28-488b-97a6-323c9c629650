{% doc %}
  @prompt
    Create CSS code to fix dropdown menu width overflow issues for longer text in different languages, ensuring proper text wrapping and adequate menu width for multilingual navigation

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-multilingual-dropdown-{{ ai_gen_id }} {
    position: relative;
    display: inline-block;
  }

  .ai-dropdown-trigger-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: {{ block.settings.trigger_padding }}px;
    background-color: {{ block.settings.trigger_bg_color }};
    color: {{ block.settings.trigger_text_color }};
    border: 1px solid {{ block.settings.border_color }};
    border-radius: {{ block.settings.border_radius }}px;
    cursor: pointer;
    font-size: {{ block.settings.font_size }}px;
    text-decoration: none;
    transition: all 0.3s ease;
    min-width: {{ block.settings.min_trigger_width }}px;
    justify-content: space-between;
  }

  .ai-dropdown-trigger-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.trigger_hover_bg_color }};
    color: {{ block.settings.trigger_hover_text_color }};
  }

  .ai-dropdown-trigger-{{ ai_gen_id }}[aria-expanded="true"] {
    background-color: {{ block.settings.trigger_active_bg_color }};
    color: {{ block.settings.trigger_active_text_color }};
  }

  .ai-dropdown-arrow-{{ ai_gen_id }} {
    width: 12px;
    height: 12px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
  }

  .ai-dropdown-trigger-{{ ai_gen_id }}[aria-expanded="true"] .ai-dropdown-arrow-{{ ai_gen_id }} {
    transform: rotate(180deg);
  }

  .ai-dropdown-menu-{{ ai_gen_id }} {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: {{ block.settings.menu_bg_color }};
    border: 1px solid {{ block.settings.border_color }};
    border-radius: {{ block.settings.border_radius }}px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    min-width: {{ block.settings.min_menu_width }}px;
    max-width: {{ block.settings.max_menu_width }}px;
    width: max-content;
  }

  .ai-dropdown-menu-{{ ai_gen_id }}.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .ai-dropdown-menu-{{ ai_gen_id }} ul {
    list-style: none;
    margin: 0;
    padding: 8px 0;
  }

  .ai-dropdown-item-{{ ai_gen_id }} {
    margin: 0;}

  .ai-dropdown-link-{{ ai_gen_id }} {
    display: block;
    padding: {{ block.settings.item_padding }}px;
    color: {{ block.settings.menu_text_color }};
    text-decoration: none;
    font-size: {{ block.settings.font_size }}px;
    line-height: 1.4;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    transition: all 0.3s ease;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .ai-dropdown-link-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.item_hover_bg_color }};
    color: {{ block.settings.item_hover_text_color }};
  }

  .ai-dropdown-link-{{ ai_gen_id }}:focus {
    background-color: {{ block.settings.item_hover_bg_color }};
    color: {{ block.settings.item_hover_text_color }};outline: 2px solid {{ block.settings.focus_color }};
    outline-offset: -2px;
  }

  @media screen and (max-width: 768px) {
    .ai-dropdown-menu-{{ ai_gen_id }} {
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: calc(100vw - 40px);
      max-width: 400px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .ai-dropdown-menu-{{ ai_gen_id }}.active {
      transform: translate(-50%, -50%);
    }

    .ai-dropdown-link-{{ ai_gen_id }} {
      padding: {{ block.settings.item_padding | times: 1.2 }}px;font-size: {{ block.settings.font_size | times: 1.1 }}px;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-dropdown-trigger-{{ ai_gen_id }} {
      min-width: auto;
      width: 100%;
    }

    .ai-dropdown-menu-{{ ai_gen_id }} {
      width: calc(100vw - 20px);
    }
  }

  .ai-dropdown-overlay-{{ ai_gen_id }} {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .ai-dropdown-overlay-{{ ai_gen_id }}.active {
    opacity: 1;
    visibility: visible;
  }

  @media screen and (min-width: 769px) {
    .ai-dropdown-overlay-{{ ai_gen_id }} {
      display: none;
    }
  }
{% endstyle %}

<multilingual-dropdown-{{ ai_gen_id }}
  class="ai-multilingual-dropdown-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <button
    class="ai-dropdown-trigger-{{ ai_gen_id }}"
    aria-expanded="false"
    aria-haspopup="true"
    aria-label="{{ block.settings.trigger_aria_label }}"
  >
    <span class="ai-dropdown-trigger-text-{{ ai_gen_id }}">
      {{ block.settings.trigger_text }}
    </span>
    <svg
      class="ai-dropdown-arrow-{{ ai_gen_id }}"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <polyline points="6,9 12,15 18,9"></polyline>
    </svg>
  </button>

  <div class="ai-dropdown-overlay-{{ ai_gen_id }}"></div>

  <nav class="ai-dropdown-menu-{{ ai_gen_id }}" role="menu">
    <ul>
      {% for i in (1..8) %}
        {% liquid
          assign item_text_key = 'item_' | append: i | append: '_text'
          assign item_url_key = 'item_' | append: i | append: '_url'
          assign item_text = block.settings[item_text_key]
          assign item_url = block.settings[item_url_key]
        %}
        {% if item_text != blank %}
          <li class="ai-dropdown-item-{{ ai_gen_id }}">
            {% if item_url != blank %}
              <a
                href="{{ item_url }}"
                class="ai-dropdown-link-{{ ai_gen_id }}"
                role="menuitem"
              >
                {{ item_text }}</a>
            {% else %}
              <span
                class="ai-dropdown-link-{{ ai_gen_id }}"
                role="menuitem"tabindex="0"
              >
                {{ item_text }}
              </span>
            {% endif %}
          </li>
        {% endif %}
      {% endfor %}
    </ul>
  </nav>
</multilingual-dropdown-{{ ai_gen_id }}><script>
(function() {
  class MultilingualDropdown{{ ai_gen_id }} extends HTMLElement {
    constructor() {
      super();
      this.trigger = null;
      this.menu = null;
      this.overlay = null;
      this.isOpen = false;}

    connectedCallback() {
      this.trigger = this.querySelector('.ai-dropdown-trigger-{{ ai_gen_id }}');
      this.menu = this.querySelector('.ai-dropdown-menu-{{ ai_gen_id }}');
      this.overlay = this.querySelector('.ai-dropdown-overlay-{{ ai_gen_id }}');
      
      this.setupEventListeners();this.adjustMenuWidth();
    }

    setupEventListeners() {
      this.trigger.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggle();
      });

      this.trigger.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.toggle();
        } else if (e.key === 'ArrowDown') {
          e.preventDefault();
          this.open();
          this.focusFirstItem();
        }
      });

      this.overlay.addEventListener('click', () => {
        this.close();
      });

      this.menu.addEventListener('keydown', (e) => {
        this.handleMenuKeydown(e);
      });document.addEventListener('click', (e) => {
        if (!this.contains(e.target)) {
          this.close();
        }
      });

      window.addEventListener('resize', () => {
        this.adjustMenuWidth();
      });
    }

    adjustMenuWidth() {
      const triggerWidth = this.trigger.offsetWidth;
      const menuMinWidth = parseInt(getComputedStyle(this.menu).minWidth);
      const menuMaxWidth = parseInt(getComputedStyle(this.menu).maxWidth);this.menu.style.minWidth = Math.max(triggerWidth, menuMinWidth) + 'px';
      if (window.innerWidth <= 768) {
        this.menu.style.width = 'calc(100vw - 40px)';
        this.menu.style.maxWidth = '400px';
      } else {
        this.menu.style.width = 'max-content';
        this.menu.style.maxWidth = menuMaxWidth + 'px';
      }
    }

    handleMenuKeydown(e) {
      const items = Array.from(this.menu.querySelectorAll('.ai-dropdown-link-{{ ai_gen_id }}'));
      const currentIndex = items.indexOf(document.activeElement);

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
          items[nextIndex].focus();
          break;
        case 'ArrowUp':
          e.preventDefault();
          const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
          items[prevIndex].focus();
          break;
        case 'Escape':
          e.preventDefault();
          this.close();
          this.trigger.focus();
          break;
        case 'Tab':
          this.close();
          break;
      }
    }

    focusFirstItem() {
      const firstItem = this.menu.querySelector('.ai-dropdown-link-{{ ai_gen_id }}');
      if (firstItem) {
        firstItem.focus();
      }
    }

    toggle() {
      if (this.isOpen) {
        this.close();
      } else {
        this.open();
      }
    }

    open() {
      this.isOpen = true;
      this.trigger.setAttribute('aria-expanded', 'true');
      this.menu.classList.add('active');
      this.overlay.classList.add('active');
      this.adjustMenuWidth();
    }

    close() {
      this.isOpen = false;
      this.trigger.setAttribute('aria-expanded', 'false');
      this.menu.classList.remove('active');
      this.overlay.classList.remove('active');
    }
  }

  customElements.define('multilingual-dropdown-{{ ai_gen_id }}', MultilingualDropdown{{ ai_gen_id }});
})();
</script>

{% schema %}
{
  "name": "Multilingual Dropdown",
  "settings": [
    {
      "type": "header",
      "content": "Trigger Button"
    },
    {
      "type": "text",
      "id": "trigger_text",
      "label": "Trigger text",
      "default": "Language"
    },
    {
      "type": "text",
      "id": "trigger_aria_label",
      "label": "Trigger aria label",
      "default": "Select language"
    },
    {
      "type": "range",
      "id": "min_trigger_width",
      "min": 80,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Minimum trigger width",
      "default": 120
    },
    {
      "type": "range",
      "id": "trigger_padding",
      "min": 8,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Trigger padding",
      "default": 12
    },
    {
      "type": "header",
      "content": "Menu Settings"
    },
    {
      "type": "range",
      "id": "min_menu_width",
      "min": 150,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Minimum menu width",
      "default": 200
    },
    {
      "type": "range",
      "id": "max_menu_width",
      "min": 250,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Maximum menu width",
      "default": 350
    },
    {
      "type": "range",
      "id": "item_padding",
      "min": 8,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Menu item padding",
      "default": 12
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 12,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 4
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "trigger_bg_color",
      "label": "Trigger background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "trigger_text_color",
      "label": "Trigger text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "trigger_hover_bg_color",
      "label": "Trigger hover background",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "trigger_hover_text_color",
      "label": "Trigger hover text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "trigger_active_bg_color",
      "label": "Trigger active background",
      "default": "#e5e5e5"
    },
    {
      "type": "color",
      "id": "trigger_active_text_color",
      "label": "Trigger active text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "menu_bg_color",
      "label": "Menu background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "menu_text_color",
      "label": "Menu text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "item_hover_bg_color",
      "label": "Item hover background",
      "default": "#f0f0f0"
    },
    {
      "type": "color",
      "id": "item_hover_text_color",
      "label": "Item hover text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#cccccc"
    },
    {
      "type": "color",
      "id": "focus_color",
      "label": "Focus outline color",
      "default": "#0066cc"
    },
    {
      "type": "header",
      "content": "Menu Items"
    },
    {
      "type": "text",
      "id": "item_1_text",
      "label": "Item 1 text",
      "default": "English"
    },
    {
      "type": "url",
      "id": "item_1_url",
      "label": "Item 1 URL"
    },
    {
      "type": "text",
      "id": "item_2_text",
      "label": "Item 2 text",
      "default": "Français"
    },
    {
      "type": "url",
      "id": "item_2_url",
      "label": "Item 2 URL"
    },
    {
      "type": "text",
      "id": "item_3_text",
      "label": "Item 3 text",
      "default": "Español"
    },
    {
      "type": "url",
      "id": "item_3_url",
      "label": "Item 3 URL"
    },
    {
      "type": "text",
      "id": "item_4_text",
      "label": "Item 4 text",
      "default": "Deutsch"
    },
    {
      "type": "url",
      "id": "item_4_url",
      "label": "Item 4 URL"
    },
    {
      "type": "text",
      "id": "item_5_text",
      "label": "Item 5 text"
    },
    {
      "type": "url",
      "id": "item_5_url",
      "label": "Item 5 URL"
    },
    {
      "type": "text",
      "id": "item_6_text",
      "label": "Item 6 text"
    },
    {
      "type": "url",
      "id": "item_6_url",
      "label": "Item 6 URL"
    },
    {
      "type": "text",
      "id": "item_7_text",
      "label": "Item 7 text"
    },
    {
      "type": "url",
      "id": "item_7_url",
      "label": "Item 7 URL"
    },
    {
      "type": "text",
      "id": "item_8_text",
      "label": "Item 8 text"
    },
    {
      "type": "url",
      "id": "item_8_url",
      "label": "Item 8 URL"
    }
  ],
  "presets": [
    {
      "name": "Multilingual Dropdown"
    }
  ]
}
{% endschema %}