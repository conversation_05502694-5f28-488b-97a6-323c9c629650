{% doc %}
  @prompt
    Create CSS styling to center tour product descriptions on the page instead of having them aligned to the right. The styling should make the text content appear centered horizontally on the page.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .centered-product-description-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: {{ block.settings.max_width }}px;
    margin: 0 auto;
    padding: {{ block.settings.padding }}px;
  }

  .centered-product-description-{{ ai_gen_id }} .product__description {
    text-align: center;
    margin: 0 auto;
  }

  .centered-product-description-{{ ai_gen_id }} .product-single__description {
    text-align: center;
    margin: 0 auto;
  }

  .centered-product-description-{{ ai_gen_id }} .product-description {
    text-align: center;
    margin: 0 auto;
  }

  /* Target common theme description selectors */
  .centered-product-description-{{ ai_gen_id }} [data-product-description],
  .centered-product-description-{{ ai_gen_id }} .product__description-container,
  .centered-product-description-{{ ai_gen_id }} .product-description-container,
  .centered-product-description-{{ ai_gen_id }} .product__text {
    text-align: center;
    margin: 0 auto;
  }

  .centered-product-description-{{ ai_gen_id }} p,
  .centered-product-description-{{ ai_gen_id }} ul,
  .centered-product-description-{{ ai_gen_id }} ol,
  .centered-product-description-{{ ai_gen_id }} h1,
  .centered-product-description-{{ ai_gen_id }} h2,
  .centered-product-description-{{ ai_gen_id }} h3,
  .centered-product-description-{{ ai_gen_id }} h4,
  .centered-product-description-{{ ai_gen_id }} h5,
  .centered-product-description-{{ ai_gen_id }} h6 {
    text-align: center;
  }
{% endstyle %}

<div class="centered-product-description-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {{ product.description }}
</div>

{% schema %}
{
  "name": "Centered Description",
  "tag": null,
  "class": "product-description-block",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 400,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Maximum width",
      "default": 800
    },
    {
      "type": "range",
      "id": "padding",
      "min": 0,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 20
    }
  ],
  "presets": [
    {
      "name": "Centered Description"
    }
  ]
}
{% endschema %}