{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer-container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-sitemap-footer-links-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    gap: {{ block.settings.gap }}px;
    margin-bottom: {{ block.settings.gap }}px;
  }

  .ai-sitemap-footer-column-{{ ai_gen_id }} {
    flex: 1;
    min-width: 150px;
  }

  .ai-sitemap-footer-title-{{ ai_gen_id }} {
    font-weight: bold;
    margin-top: 0;
    margin-bottom: {{ block.settings.gap | divided_by: 2 }}px;
    font-size: calc({{ block.settings.font_size }}px * 1.2);
  }

  .ai-sitemap-footer-list-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer-list-item-{{ ai_gen_id }} {
    margin-bottom: {{ block.settings.gap | divided_by: 3 }}px;
  }

  .ai-sitemap-footer-link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .ai-sitemap-footer-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-footer-copyright-{{ ai_gen_id }} {
    text-align: center;
    padding-top: {{ block.settings.gap | divided_by: 2 }}px;
    border-top: 1px solid {{ block.settings.divider_color }};
    font-size: calc({{ block.settings.font_size }}px * 0.9);
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer-links-{{ ai_gen_id }} {
      flex-direction: {% if block.settings.columns_mobile == 1 %}column{% else %}row{% endif %};
    }
    
    .ai-sitemap-footer-column-{{ ai_gen_id }} {
      flex: {% if block.settings.columns_mobile == 1 %}0 0 100%{% else %}0 0 calc(50% - {{ block.settings.gap | divided_by: 2 }}px){% endif %};
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer-container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer-links-{{ ai_gen_id }}">
      {% if linklists[block.settings.main_menu].links.size > 0 %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer-title-{{ ai_gen_id }}">{{ block.settings.main_menu_title }}</h3>
          <ul class="ai-sitemap-footer-list-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.main_menu].links %}
              <li class="ai-sitemap-footer-list-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer-link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.footer_menu].links.size > 0 %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer-title-{{ ai_gen_id }}">{{ block.settings.footer_menu_title }}</h3>
          <ul class="ai-sitemap-footer-list-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.footer_menu].links %}
              <li class="ai-sitemap-footer-list-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer-link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.additional_menu].links.size > 0 %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer-title-{{ ai_gen_id }}">{{ block.settings.additional_menu_title }}</h3>
          <ul class="ai-sitemap-footer-list-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.additional_menu].links %}
              <li class="ai-sitemap-footer-list-item-{{ ai_gen_id }}">
                <a href="{{ link.url }}" class="ai-sitemap-footer-link-{{ ai_gen_id }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if block.settings.show_policies %}
        <div class="ai-sitemap-footer-column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer-title-{{ ai_gen_id }}">{{ block.settings.policies_title }}</h3>
          <ul class="ai-sitemap-footer-list-{{ ai_gen_id }}">
            {% if shop.privacy_policy %}
              <li class="ai-sitemap-footer-list-item-{{ ai_gen_id }}">
                <a href="{{ shop.privacy_policy.url }}" class="ai-sitemap-footer-link-{{ ai_gen_id }}">{{ shop.privacy_policy.title }}</a>
              </li>
            {% endif %}
            {% if shop.terms_of_service %}
              <li class="ai-sitemap-footer-list-item-{{ ai_gen_id }}">
                <a href="{{ shop.terms_of_service.url }}" class="ai-sitemap-footer-link-{{ ai_gen_id }}">{{ shop.terms_of_service.title }}</a>
              </li>
            {% endif %}
            {% if shop.refund_policy %}
              <li class="ai-sitemap-footer-list-item-{{ ai_gen_id }}">
                <a href="{{ shop.refund_policy.url }}" class="ai-sitemap-footer-link-{{ ai_gen_id }}">{{ shop.refund_policy.title }}</a>
              </li>
            {% endif %}
            {% if shop.shipping_policy %}
              <li class="ai-sitemap-footer-list-item-{{ ai_gen_id }}">
                <a href="{{ shop.shipping_policy.url }}" class="ai-sitemap-footer-link-{{ ai_gen_id }}">{{ shop.shipping_policy.title }}</a>
              </li>
            {% endif %}
          </ul>
        </div>
      {% endif %}
    </div>

    <div class="ai-sitemap-footer-copyright-{{ ai_gen_id }}">
      {% if block.settings.copyright_text != blank %}
        {{ block.settings.copyright_text }}
      {% else %}
        &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}. All rights reserved.
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Menu Links"
    },
    {
      "type": "link_list",
      "id": "main_menu",
      "label": "Main menu",
      "default": "main-menu"
    },
    {
      "type": "text",
      "id": "main_menu_title",
      "label": "Main menu title",
      "default": "Main menu"
    },
    {
      "type": "link_list",
      "id": "footer_menu",
      "label": "Footer menu",
      "default": "footer"
    },
    {
      "type": "text",
      "id": "footer_menu_title",
      "label": "Footer menu title",
      "default": "Quick links"
    },
    {
      "type": "link_list",
      "id": "additional_menu",
      "label": "Additional menu"
    },
    {
      "type": "text",
      "id": "additional_menu_title",
      "label": "Additional menu title",
      "default": "More info"
    },
    {
      "type": "checkbox",
      "id": "show_policies",
      "label": "Show policy pages",
      "default": true
    },
    {
      "type": "text",
      "id": "policies_title",
      "label": "Policies title",
      "default": "Policies"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text"
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "gap",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Spacing",
      "default": 20
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {"value": "1", "label": "1"},
        {"value": "2", "label": "2"}
      ],
      "default": "1"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}