{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-{{ ai_gen_id }} {
    width: 100%;
    padding: {{ block.settings.padding }}px 0;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .ai-sitemap-links-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    gap: {{ block.settings.gap }}px;
    margin-bottom: {{ block.settings.gap }}px;
  }

  .ai-sitemap-link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    transition: color 0.3s ease;
    margin-right: {{ block.settings.gap }}px;
    white-space: nowrap;
  }

  .ai-sitemap-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
    text-decoration: underline;
  }

  .ai-sitemap-copyright-{{ ai_gen_id }} {
    text-align: {{ block.settings.copyright_alignment }};
    margin-top: {{ block.settings.gap }}px;
    font-size: {{ block.settings.copyright_font_size }}px;
    color: {{ block.settings.copyright_color }};
  }

  .ai-sitemap-divider-{{ ai_gen_id }} {
    width: 100%;
    height: 1px;
    background-color: {{ block.settings.divider_color }};
    margin: {{ block.settings.gap }}px 0;
    opacity: 0.5;
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-{{ ai_gen_id }} {
      padding: {{ block.settings.padding | divided_by: 2 }}px 0;
    }
    
    .ai-sitemap-links-{{ ai_gen_id }} {
      flex-direction: {{ block.settings.mobile_layout }};
      gap: {{ block.settings.gap | divided_by: 2 }}px;
    }
    
    .ai-sitemap-link-{{ ai_gen_id }} {
      margin-right: {{ block.settings.gap | divided_by: 2 }}px;
    }
  }
{% endstyle %}

<div class="ai-sitemap-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-container-{{ ai_gen_id }}">
    <div class="ai-sitemap-links-{{ ai_gen_id }}">
      {% if block.settings.show_home %}
        <a href="/" class="ai-sitemap-link-{{ ai_gen_id }}">{{ block.settings.home_label }}</a>
      {% endif %}
      
      {% for link in linklists[block.settings.menu].links %}
        <a href="{{ link.url }}" class="ai-sitemap-link-{{ ai_gen_id }}">{{ link.title }}</a>
      {% endfor %}
      
      {% if block.settings.show_blog and blog %}
        <a href="{{ blog.url }}" class="ai-sitemap-link-{{ ai_gen_id }}">{{ block.settings.blog_label }}</a>
      {% endif %}
      
      {% if block.settings.show_cart %}
        <a href="/cart" class="ai-sitemap-link-{{ ai_gen_id }}">{{ block.settings.cart_label }}</a>
      {% endif %}
      
      {% if block.settings.show_search %}
        <a href="/search" class="ai-sitemap-link-{{ ai_gen_id }}">{{ block.settings.search_label }}</a>
      {% endif %}
      
      {% if block.settings.show_account and shop.customer_accounts_enabled %}
        <a href="/account" class="ai-sitemap-link-{{ ai_gen_id }}">{{ block.settings.account_label }}</a>
      {% endif %}
    </div>
    
    {% if block.settings.show_divider %}
      <div class="ai-sitemap-divider-{{ ai_gen_id }}"></div>
    {% endif %}
    
    <div class="ai-sitemap-copyright-{{ ai_gen_id }}">
      {% if block.settings.copyright_text != blank %}
        {{ block.settings.copyright_text }}
      {% else %}
        &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Compact Sitemap",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Menu Links"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "Menu",
      "default": "main-menu"
    },
    {
      "type": "checkbox",
      "id": "show_home",
      "label": "Show home link",
      "default": true
    },
    {
      "type": "text",
      "id": "home_label",
      "label": "Home link label",
      "default": "Home"
    },
    {
      "type": "checkbox",
      "id": "show_blog",
      "label": "Show blog link",
      "default": true
    },
    {
      "type": "text",
      "id": "blog_label",
      "label": "Blog link label",
      "default": "Blog"
    },
    {
      "type": "checkbox",
      "id": "show_cart",
      "label": "Show cart link",
      "default": true
    },
    {
      "type": "text",
      "id": "cart_label",
      "label": "Cart link label",
      "default": "Cart"
    },
    {
      "type": "checkbox",
      "id": "show_search",
      "label": "Show search link",
      "default": true
    },
    {
      "type": "text",
      "id": "search_label",
      "label": "Search link label",
      "default": "Search"
    },
    {
      "type": "checkbox",
      "id": "show_account",
      "label": "Show account link",
      "default": true
    },
    {
      "type": "text",
      "id": "account_label",
      "label": "Account link label",
      "default": "Account"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text",
      "info": "Leave blank to use default copyright with current year and shop name"
    },
    {
      "type": "select",
      "id": "copyright_alignment",
      "label": "Copyright alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "checkbox",
      "id": "show_divider",
      "label": "Show divider above copyright",
      "default": true
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright text color",
      "default": "#777777"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#cccccc"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 30
    },
    {
      "type": "range",
      "id": "gap",
      "min": 5,
      "max": 30,
      "step": 5,
      "unit": "px",
      "label": "Spacing between elements",
      "default": 15
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 10,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "copyright_font_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Copyright font size",
      "default": 12
    },
    {
      "type": "select",
      "id": "mobile_layout",
      "label": "Mobile layout",
      "options": [
        {
          "value": "row",
          "label": "Horizontal"
        },
        {
          "value": "column",
          "label": "Vertical"
        }
      ],
      "default": "row"
    }
  ],
  "presets": [
    {
      "name": "Compact Sitemap"
    }
  ]
}
{% endschema %}