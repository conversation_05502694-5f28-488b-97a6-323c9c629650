{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;}

  .ai-sitemap-footer__columns-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns }}, 1fr);
    gap: {{ block.settings.column_gap }}px;}

  .ai-sitemap-footer__column-{{ ai_gen_id }} {
    margin-bottom: 20px;
  }

  .ai-sitemap-footer__heading-{{ ai_gen_id }} {
    font-size: calc({{ block.settings.font_size }}px * 1.2);
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 12px;
    color: {{ block.settings.heading_color }};
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    margin-bottom: 8px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} a {
    text-decoration: none;
    color: {{ block.settings.link_color }};
    transition: color 0.2s ease;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid {{ block.settings.divider_color }};
    text-align: center;
    font-size: calc({{ block.settings.font_size }}px * 0.9);
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__columns-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__columns-{{ ai_gen_id }}">
      {% if linklists[block.settings.menu_1].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ linklists[block.settings.menu_1].title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_1].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_2].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ linklists[block.settings.menu_2].title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_2].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_3].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ linklists[block.settings.menu_3].title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_3].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_4].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ linklists[block.settings.menu_4].title }}</h3>
          <ul class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_4].links %}
              <li class="ai-sitemap-footer__link-{{ ai_gen_id }}">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
    </div>

    <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
      {% if block.settings.copyright_text != blank %}
        {{ block.settings.copyright_text }}
      {% else %}
        &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Sitemap Footer",
  "settings": [
    {
      "type": "header",
      "content": "Menu Selection"
    },
    {
      "type": "link_list",
      "id": "menu_1",
      "label": "Menu 1",
      "default": "main-menu"
    },
    {
      "type": "link_list",
      "id": "menu_2",
      "label": "Menu 2",
      "info": "Optional"
    },
    {
      "type": "link_list",
      "id": "menu_3",
      "label": "Menu 3",
      "info": "Optional"
    },
    {
      "type": "link_list",
      "id": "menu_4",
      "label": "Menu 4",
      "info": "Optional"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "columns",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 4,
      "label": "Number of columns (desktop)"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Number of columns (mobile)",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "column_gap",
      "min": 10,
      "max": 60,
      "step": 5,
      "default": 30,
      "unit": "px",
      "label": "Column spacing"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 60,
      "step": 5,
      "default": 30,
      "unit": "px",
      "label": "Padding"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "copyright_text",
      "label": "Copyright text",
      "info": "Leave blank to use default"
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "default": 14,
      "unit": "px",
      "label": "Font size"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f3f3f3"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#e1e1e1"
    }
  ],
  "presets": [
    {
      "name": "Sitemap Footer"
    }
  ],
  "tag": null
}
{% endschema %}