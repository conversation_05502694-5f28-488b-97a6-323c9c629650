{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, responsive design for all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    width: 100%;
    padding: {{ block.settings.padding }}px 0;
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.font_size }}px;
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .ai-sitemap-footer__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: 20px;
  }

  .ai-sitemap-footer__column-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
  }

  .ai-sitemap-footer__title-{{ ai_gen_id }} {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: calc({{ block.settings.font_size }}px + 2px);
    color: {{ block.settings.title_color }};
  }

  .ai-sitemap-footer__links-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    text-decoration: none;
    color: {{ block.settings.link_color }};
    transition: color 0.2s ease;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    margin-top: {{ block.settings.copyright_margin }}px;
    padding-top: {{ block.settings.copyright_padding }}px;
    border-top: 1px solid {{ block.settings.divider_color }};
    text-align: center;
    font-size: calc({{ block.settings.font_size }}px - 1px);
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
    }
    
    .ai-sitemap-footer__title-{{ ai_gen_id }} {
      font-size: calc({{ block.settings.font_size }}px + 1px);
    }
  }
{% endstyle %}

<div class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__grid-{{ ai_gen_id }}">
      {% if linklists[block.settings.menu_1].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ linklists[block.settings.menu_1].title }}</h3>
          <div class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_1].links %}
              <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
            {% endfor %}
          </div>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_2].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ linklists[block.settings.menu_2].title }}</h3>
          <div class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_2].links %}
              <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
            {% endfor %}
          </div>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_3].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ linklists[block.settings.menu_3].title }}</h3>
          <div class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_3].links %}
              <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
            {% endfor %}
          </div>
        </div>
      {% endif %}

      {% if linklists[block.settings.menu_4].links.size > 0 %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__title-{{ ai_gen_id }}">{{ linklists[block.settings.menu_4].title }}</h3>
          <div class="ai-sitemap-footer__links-{{ ai_gen_id }}">
            {% for link in linklists[block.settings.menu_4].links %}
              <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
            {% endfor %}
          </div>
        </div>
      {% endif %}
    </div>
    
    {% if block.settings.show_copyright %}
      <div class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">
        {% if block.settings.copyright_text != blank %}
          {{ block.settings.copyright_text }}
        {% else %}
          &copy; {{ 'now' | date: '%Y' }} {{ shop.name }}. All rights reserved.
        {% endif %}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Sitemap Footer",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Menu Selection"
    },
    {
      "type": "link_list",
      "id": "menu_1",
      "label": "Menu 1",
      "default": "main-menu"
    },
    {
      "type": "link_list",
      "id": "menu_2",
      "label": "Menu 2",
      "info": "Add a second menu to display"
    },
    {
      "type": "link_list",
      "id": "menu_3",
      "label": "Menu 3",
      "info": "Add a third menu to display"
    },
    {
      "type": "link_list",
      "id": "menu_4",
      "label": "Menu 4",
      "info": "Add a fourth menu to display"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 4,
      "label": "Number of columns on desktop"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Number of columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "padding",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider color",
      "default": "#dddddd"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "checkbox",
      "id": "show_copyright",
      "label": "Show copyright",
      "default": true
    },
    {
      "type": "textarea",
      "id": "copyright_text",
      "label": "Custom copyright text",
      "info": "Leave blank to use default copyright text"
    },
    {
      "type": "range",
      "id": "copyright_margin",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Copyright top margin",
      "default": 20
    },
    {
      "type": "range",
      "id": "copyright_padding",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Copyright top padding",
      "default": 20
    }
  ],
  "presets": [
    {
      "name": "Sitemap Footer"
    }
  ]
}
{% endschema %}