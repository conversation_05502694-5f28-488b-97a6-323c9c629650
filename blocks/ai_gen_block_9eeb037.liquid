{% doc %}
  @prompt
    Create a compact sitemap footer section with links to all current website pages, featuring a responsive design that works on all devices, small and compact layout, and editable copyright information at the bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-sitemap-footer-{{ ai_gen_id }} {
    background-color: {{ block.settings.background_color }};
    color: {{ block.settings.text_color }};
    padding: {{ block.settings.padding_top }}px {{ block.settings.padding_horizontal }}px {{ block.settings.padding_bottom }}px;
    border-top: {{ block.settings.border_width }}px solid {{ block.settings.border_color }};
  }

  .ai-sitemap-footer__container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-sitemap-footer__grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: {{ block.settings.column_gap }}px {{ block.settings.row_gap }}px;margin-bottom: {{ block.settings.section_spacing }}px;
  }

  .ai-sitemap-footer__column-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: {{ block.settings.link_spacing }}px;
  }

  .ai-sitemap-footer__heading-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    font-weight: 600;
    margin: 0 0 {{ block.settings.heading_margin }}px 0;
    color: {{ block.settings.heading_color }};
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }} {
    color: {{ block.settings.link_color }};
    text-decoration: none;
    font-size: {{ block.settings.link_size }}px;
    transition: color 0.2s ease;
    line-height: 1.4;
  }

  .ai-sitemap-footer__link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-sitemap-footer__copyright-{{ ai_gen_id }} {
    text-align: {{ block.settings.copyright_alignment }};
    font-size: {{ block.settings.copyright_size }}px;
    color: {{ block.settings.copyright_color }};
    padding-top: {{ block.settings.section_spacing }}px;
    border-top: 1px solid {{ block.settings.border_color }};margin: 0;
  }

  @media screen and (max-width: 749px) {
    .ai-sitemap-footer__grid-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
      gap: {{ block.settings.column_gap | times: 0.8 }}px {{ block.settings.row_gap | times: 0.8 }}px;
    }

    .ai-sitemap-footer-{{ ai_gen_id }} {
      padding: {{ block.settings.padding_top | times: 0.8 }}px {{ block.settings.padding_horizontal | times: 0.8 }}px {{ block.settings.padding_bottom | times: 0.8 }}px;
    }

    .ai-sitemap-footer__heading-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | times: 0.9 }}px;
    }

    .ai-sitemap-footer__link-{{ ai_gen_id }} {
      font-size: {{ block.settings.link_size | times: 0.9 }}px;
    }
  }
{% endstyle %}<footer class="ai-sitemap-footer-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-sitemap-footer__container-{{ ai_gen_id }}">
    <div class="ai-sitemap-footer__grid-{{ ai_gen_id }}">
      
      {% if block.settings.show_shop_section %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.shop_heading }}</h3>
          {% for link in linklists[block.settings.shop_menu].links %}
            <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_info_section %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.info_heading }}</h3>
          {% for link in linklists[block.settings.info_menu].links %}
            <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_support_section %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.support_heading }}</h3>
          {% for link in linklists[block.settings.support_menu].links %}
            <a href="{{ link.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ link.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_pages_section %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.pages_heading }}</h3>
          {% for page in pages %}
            {% if page.published %}
              <a href="{{ page.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ page.title }}</a>
            {% endif %}
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_collections_section %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.collections_heading }}</h3>
          {% for collection in collections %}
            {% unless collection.handle == 'frontpage' %}
              <a href="{{ collection.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ collection.title }}</a>
            {% endunless %}
          {% endfor %}
        </div>
      {% endif %}

      {% if block.settings.show_blogs_section %}
        <div class="ai-sitemap-footer__column-{{ ai_gen_id }}">
          <h3 class="ai-sitemap-footer__heading-{{ ai_gen_id }}">{{ block.settings.blogs_heading }}</h3>
          {% for blog in blogs %}
            <a href="{{ blog.url }}" class="ai-sitemap-footer__link-{{ ai_gen_id }}">{{ blog.title }}</a>
          {% endfor %}
        </div>
      {% endif %}

    </div>

    {% if block.settings.copyright_text != blank %}
      <p class="ai-sitemap-footer__copyright-{{ ai_gen_id }}">{{ block.settings.copyright_text }}</p>
    {% endif %}
  </div>
</footer>

{% schema %}
{
  "name": "Sitemap Footer",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {"value": "1", "label": "1"},
        {"value": "2", "label": "2"}
      ],
      "default": "2"
    },
    {
      "type": "range",
      "id": "column_gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Column spacing",
      "default": 30
    },
    {
      "type": "range",
      "id": "row_gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Row spacing",
      "default": 30
    },
    {
      "type": "range",
      "id": "section_spacing",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Section spacing",
      "default": 20
    },
    {
      "type": "header",
      "content": "Shop Section"
    },
    {
      "type": "checkbox",
      "id": "show_shop_section",
      "label": "Show shop section",
      "default": true
    },
    {
      "type": "text",
      "id": "shop_heading",
      "label": "Shop heading",
      "default": "Shop"
    },
    {
      "type": "link_list",
      "id": "shop_menu",
      "label": "Shop menu"
    },
    {
      "type": "header",
      "content": "Information Section"
    },
    {
      "type": "checkbox",
      "id": "show_info_section",
      "label": "Show information section",
      "default": true
    },
    {
      "type": "text",
      "id": "info_heading",
      "label": "Information heading",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "info_menu",
      "label": "Information menu"
    },
    {
      "type": "header",
      "content": "Support Section"
    },
    {
      "type": "checkbox",
      "id": "show_support_section",
      "label": "Show support section",
      "default": true
    },
    {
      "type": "text",
      "id": "support_heading",
      "label": "Support heading",
      "default": "Support"
    },
    {
      "type": "link_list",
      "id": "support_menu",
      "label": "Support menu"
    },
    {
      "type": "header",
      "content": "Pages Section"
    },
    {
      "type": "checkbox",
      "id": "show_pages_section",
      "label": "Show pages section",
      "default": false
    },
    {
      "type": "text",
      "id": "pages_heading",
      "label": "Pages heading",
      "default": "Pages"
    },
    {
      "type": "header",
      "content": "Collections Section"
    },
    {
      "type": "checkbox",
      "id": "show_collections_section",
      "label": "Show collections section",
      "default": false
    },
    {
      "type": "text",
      "id": "collections_heading",
      "label": "Collections heading",
      "default": "Collections"
    },
    {
      "type": "header",
      "content": "Blogs Section"
    },
    {
      "type": "checkbox",
      "id": "show_blogs_section",
      "label": "Show blogs section",
      "default": false
    },
    {
      "type": "text",
      "id": "blogs_heading",
      "label": "Blogs heading",
      "default": "Blogs"
    },
    {
      "type": "header",
      "content": "Copyright"
    },
    {
      "type": "text",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "© 2024 Your Store Name. All rights reserved."
    },
    {
      "type": "select",
      "id": "copyright_alignment",
      "label": "Copyright alignment",
      "options": [
        {"value": "left", "label": "Left"},
        {"value": "center", "label": "Center"},
        {"value": "right", "label": "Right"}
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f8f8f8"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "copyright_color",
      "label": "Copyright color",
      "default": "#999999"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#e0e0e0"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Heading size",
      "default": 16
    },
    {
      "type": "range",
      "id": "link_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Link size",
      "default": 14
    },
    {
      "type": "range",
      "id": "copyright_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Copyright size",
      "default": 12
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Top padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Bottom padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Horizontal padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "heading_margin",
      "min": 5,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Heading bottom margin",
      "default": 10
    },
    {
      "type": "range",
      "id": "link_spacing",
      "min": 2,
      "max": 15,
      "step": 1,
      "unit": "px",
      "label": "Link spacing",
      "default": 6
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Top border width",
      "default": 1
    }
  ],
  "presets": [
    {
      "name": "Sitemap Footer"
    }
  ]
}
{% endschema %}