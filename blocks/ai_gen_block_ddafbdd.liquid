{% doc %}
  @prompt
    Create a tour itinerary section with day tabs for 6-day tour. Include 6 tabs labeled Day 1, Day 2, Day 3, Day 4, Day 5, Day 6. Each tab shows different content when clicked. Each day should have a text field for description and an image field. Make it responsive and clean looking for a travel website.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-tour-itinerary-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-tour-itinerary__header-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 40px;
  }

  .ai-tour-itinerary__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.title_color }};
    margin: 0 0 16px;
    font-weight: 600;
  }

  .ai-tour-itinerary__subtitle-{{ ai_gen_id }} {
    font-size: {{ block.settings.subtitle_size }}px;
    color: {{ block.settings.subtitle_color }};
    margin: 0;
    opacity: 0.8;
  }

  .ai-tour-itinerary__tabs-{{ ai_gen_id }} {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 32px;
    border-bottom: 2px solid {{ block.settings.tab_border_color }};
    padding-bottom: 0;
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }} {
    flex: 1;
    min-width: 120px;
    padding: 16px 20px;
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: {{ block.settings.tab_text_color }};
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.tab_hover_color }};
    color: {{ block.settings.tab_hover_text_color }};
  }

  .ai-tour-itinerary__tab-{{ ai_gen_id }}.active {
    color: {{ block.settings.tab_active_text_color }};
    border-bottom-color: {{ block.settings.tab_active_border_color }};
    background-color: {{ block.settings.tab_active_bg_color }};
  }

  .ai-tour-itinerary__content-{{ ai_gen_id }} {
    min-height: 400px;
  }

  .ai-tour-itinerary__day-{{ ai_gen_id }} {
    display: none;
    animation: ai-tour-fadeIn-{{ ai_gen_id }} 0.4s ease-in-out;
  }

  .ai-tour-itinerary__day-{{ ai_gen_id }}.active {
    display: block;
  }

  @keyframes ai-tour-fadeIn-{{ ai_gen_id }} {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
  }

  .ai-tour-itinerary__day-text-{{ ai_gen_id }} {
    order: 1;
  }

  .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
    order: 2;
  }

  .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-text-{{ ai_gen_id }} {
    order: 2;
  }

  .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
    order: 1;
  }

  .ai-tour-itinerary__day-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.day_title_size }}px;
    color: {{ block.settings.day_title_color }};
    margin: 0 0 20px;
    font-weight: 600;
  }

  .ai-tour-itinerary__day-description-{{ ai_gen_id }} {
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.description_color }};
    line-height: 1.6;
    margin: 0;
  }

  .ai-tour-itinerary__image-wrapper-{{ ai_gen_id }} {
    width: 100%;
    height: 300px;
    border-radius: {{ block.settings.image_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  .ai-tour-itinerary__image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-tour-itinerary__image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
  }

  .ai-tour-itinerary__image-placeholder-{{ ai_gen_id }} svg {
    width: 80px;
    height: 80px;
    opacity: 0.5;
  }

  @media screen and (max-width: 768px) {
    .ai-tour-itinerary-{{ ai_gen_id }} {
      padding: 24px 16px;
    }

    .ai-tour-itinerary__tabs-{{ ai_gen_id }} {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 4px;
    }

    .ai-tour-itinerary__tab-{{ ai_gen_id }} {
      min-width: auto;
      padding: 12px 8px;
      font-size: 14px;
    }

    .ai-tour-itinerary__day-content-{{ ai_gen_id }} {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .ai-tour-itinerary__day-text-{{ ai_gen_id }},
    .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-text-{{ ai_gen_id }} {
      order: 2;
    }

    .ai-tour-itinerary__day-image-{{ ai_gen_id }},
    .ai-tour-itinerary__day-content-{{ ai_gen_id }}.reverse .ai-tour-itinerary__day-image-{{ ai_gen_id }} {
      order: 1;
    }

    .ai-tour-itinerary__image-wrapper-{{ ai_gen_id }} {
      height: 200px;
    }
  }
{% endstyle %}

<tour-itinerary-{{ ai_gen_id }}
  class="ai-tour-itinerary-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-tour-itinerary__header-{{ ai_gen_id }}">
    {% if block.settings.title != blank %}
      <h2 class="ai-tour-itinerary__title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
    {% endif %}
    {% if block.settings.subtitle != blank %}
      <p class="ai-tour-itinerary__subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</p>
    {% endif %}
  </div>

  <div class="ai-tour-itinerary__tabs-{{ ai_gen_id }}" role="tablist">
    {% for i in (1..6) %}
      <button
        class="ai-tour-itinerary__tab-{{ ai_gen_id }}{% if i == 1 %} active{% endif %}"
        role="tab"
        aria-selected="{% if i == 1 %}true{% else %}false{% endif %}"
        aria-controls="day-{{ i }}-{{ ai_gen_id }}"
        data-day="{{ i }}"
      >
        Day {{ i }}
      </button>
    {% endfor %}
  </div>

  <div class="ai-tour-itinerary__content-{{ ai_gen_id }}">
    {% for i in (1..6) %}
      {% liquid
        assign day_title_key = 'day_' | append: i | append: '_title'
        assign day_description_key = 'day_' | append: i | append: '_description'
        assign day_image_key = 'day_' | append: i | append: '_image'
        
        assign day_title = block.settings[day_title_key]
        assign day_description = block.settings[day_description_key]
        assign day_image = block.settings[day_image_key]
        
        assign is_even = i | modulo: 2
        assign reverse_class = ''
        if is_even == 0
          assign reverse_class = ' reverse'
        endif
      %}

      <div
        class="ai-tour-itinerary__day-{{ ai_gen_id }}{% if i == 1 %} active{% endif %}"
        id="day-{{ i }}-{{ ai_gen_id }}"
        role="tabpanel"
        aria-labelledby="tab-day-{{ i }}-{{ ai_gen_id }}"
      >
        <div class="ai-tour-itinerary__day-content-{{ ai_gen_id }}{{ reverse_class }}">
          <div class="ai-tour-itinerary__day-text-{{ ai_gen_id }}">
            {% if day_title != blank %}
              <h3 class="ai-tour-itinerary__day-title-{{ ai_gen_id }}">{{ day_title }}</h3>
            {% endif %}
            {% if day_description != blank %}
              <div class="ai-tour-itinerary__day-description-{{ ai_gen_id }}">{{ day_description }}</div>
            {% endif %}
          </div>
          <div class="ai-tour-itinerary__day-image-{{ ai_gen_id }}">
            <div class="ai-tour-itinerary__image-wrapper-{{ ai_gen_id }}">
              {% if day_image %}
                <img
                  src="{{ day_image | image_url: width: 600 }}"
                  alt="{{ day_image.alt | escape }}"
                  class="ai-tour-itinerary__image-{{ ai_gen_id }}"
                  loading="lazy"
                  width="600"
                  height="300"
                >
              {% else %}
                <div class="ai-tour-itinerary__image-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    {% endfor %}
  </div>
</tour-itinerary-{{ ai_gen_id }}>

<script>
  (function() {
    class TourItinerary{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.tabs = this.querySelectorAll('.ai-tour-itinerary__tab-{{ ai_gen_id }}');
        this.days = this.querySelectorAll('.ai-tour-itinerary__day-{{ ai_gen_id }}');
        
        this.setupEventListeners();
      }

      setupEventListeners() {
        this.tabs.forEach((tab) => {
          tab.addEventListener('click', (e) => {
            const dayNumber = e.target.dataset.day;
            this.switchToDay(dayNumber);
          });

          tab.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              const dayNumber = e.target.dataset.day;
              this.switchToDay(dayNumber);
            }
          });
        });
      }

      switchToDay(dayNumber) {
        this.tabs.forEach((tab) => {
          tab.classList.remove('active');
          tab.setAttribute('aria-selected', 'false');
        });

        this.days.forEach((day) => {
          day.classList.remove('active');
        });

        const activeTab = this.querySelector(`[data-day="${dayNumber}"]`);
        const activeDay = this.querySelector(`#day-${dayNumber}-{{ ai_gen_id }}`);

        if (activeTab && activeDay) {
          activeTab.classList.add('active');
          activeTab.setAttribute('aria-selected', 'true');
          activeDay.classList.add('active');
        }
      }
    }

    customElements.define('tour-itinerary-{{ ai_gen_id }}', TourItinerary{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Tour Itinerary",
  "settings": [
    {
      "type": "header",
      "content": "Section Header"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "6-Day Adventure Tour"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Discover amazing destinations on this carefully crafted journey"
    },
    {
      "type": "header",
      "content": "Day 1"
    },
    {
      "type": "text",
      "id": "day_1_title",
      "label": "Day 1 title",
      "default": "Arrival & City Exploration"
    },
    {
      "type": "richtext",
      "id": "day_1_description",
      "label": "Day 1 description",
      "default": "<p>Welcome to your adventure! Begin with a guided city tour, visit local landmarks, and enjoy a welcome dinner at a traditional restaurant. Get acquainted with the local culture and meet your fellow travelers.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_1_image",
      "label": "Day 1 image"
    },
    {
      "type": "header",
      "content": "Day 2"
    },
    {
      "type": "text",
      "id": "day_2_title",
      "label": "Day 2 title",
      "default": "Historical Sites & Museums"
    },
    {
      "type": "richtext",
      "id": "day_2_description",
      "label": "Day 2 description",
      "default": "<p>Dive deep into history with visits to ancient monuments, museums, and archaeological sites. Learn about the rich heritage of the region from expert local guides and enjoy authentic local cuisine for lunch.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_2_image",
      "label": "Day 2 image"
    },
    {
      "type": "header",
      "content": "Day 3"
    },
    {
      "type": "text",
      "id": "day_3_title",
      "label": "Day 3 title",
      "default": "Nature & Adventure"
    },
    {
      "type": "richtext",
      "id": "day_3_description",
      "label": "Day 3 description",
      "default": "<p>Experience the great outdoors with hiking, wildlife spotting, and scenic viewpoints. Enjoy a picnic lunch surrounded by nature and capture stunning photographs of the landscape.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_3_image",
      "label": "Day 3 image"
    },
    {
      "type": "header",
      "content": "Day 4"
    },
    {
      "type": "text",
      "id": "day_4_title",
      "label": "Day 4 title",
      "default": "Cultural Immersion"
    },
    {
      "type": "richtext",
      "id": "day_4_description",
      "label": "Day 4 description",
      "default": "<p>Immerse yourself in local traditions with hands-on workshops, traditional craft demonstrations, and cultural performances. Visit local markets and interact with artisans to understand their way of life.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_4_image",
      "label": "Day 4 image"
    },
    {
      "type": "header",
      "content": "Day 5"
    },
    {
      "type": "text",
      "id": "day_5_title",
      "label": "Day 5 title",
      "default": "Scenic Journey & Relaxation"
    },
    {
      "type": "richtext",
      "id": "day_5_description",
      "label": "Day 5 description",
      "default": "<p>Take a scenic journey through beautiful landscapes, with stops at viewpoints and photo opportunities. Enjoy a relaxing afternoon at a spa or leisure facility, followed by a special farewell dinner.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_5_image",
      "label": "Day 5 image"
    },
    {
      "type": "header",
      "content": "Day 6"
    },
    {
      "type": "text",
      "id": "day_6_title",
      "label": "Day 6 title",
      "default": "Departure & Last Memories"
    },
    {
      "type": "richtext",
      "id": "day_6_description",
      "label": "Day 6 description",
      "default": "<p>Your final day includes last-minute shopping for souvenirs, a leisurely breakfast, and airport transfer. Take home wonderful memories and new friendships from this incredible journey.</p>"
    },
    {
      "type": "image_picker",
      "id": "day_6_image",
      "label": "Day 6 image"
    },
    {
      "type": "header",
      "content": "Colors & Style"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle color",
      "default": "#7f8c8d"
    },
    {
      "type": "color",
      "id": "tab_text_color",
      "label": "Tab text color",
      "default": "#7f8c8d"
    },
    {
      "type": "color",
      "id": "tab_hover_color",
      "label": "Tab hover background",
      "default": "#f8f9fa"
    },
    {
      "type": "color",
      "id": "tab_hover_text_color",
      "label": "Tab hover text color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "tab_active_text_color",
      "label": "Active tab text color",
      "default": "#3498db"
    },
    {
      "type": "color",
      "id": "tab_active_border_color",
      "label": "Active tab border color",
      "default": "#3498db"
    },
    {
      "type": "color",
      "id": "tab_active_bg_color",
      "label": "Active tab background",
      "default": "#f8f9fa"
    },
    {
      "type": "color",
      "id": "tab_border_color",
      "label": "Tab border color",
      "default": "#e9ecef"
    },
    {
      "type": "color",
      "id": "day_title_color",
      "label": "Day title color",
      "default": "#2c3e50"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#5a6c7d"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 20,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 32
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 14,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 18
    },
    {
      "type": "range",
      "id": "day_title_size",
      "min": 18,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Day title size",
      "default": 24
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 14,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Section border radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    }
  ],
  "presets": [
    {
      "name": "Tour Itinerary"
    }
  ]
}
{% endschema %}