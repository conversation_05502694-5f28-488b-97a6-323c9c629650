{% doc %}
  @prompt
    Create a classic before and after image comparison gallery with a draggable slider. The gallery should show two images side by side with a vertical slider handle that users can drag left and right to reveal more of the before or after image. Include customizable settings for before and after images, labels, and styling options.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-before-after-{{ ai_gen_id }} {
    position: relative;
    max-width: 100%;
    margin: 0 auto;
    border-radius: {{ block.settings.border_radius }}px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .ai-before-after-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: {{ block.settings.height }}px;
    overflow: hidden;
    cursor: col-resize;
    user-select: none;
  }

  .ai-before-after-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-before-after-after-{{ ai_gen_id }} {
    clip-path: inset(0 0 0 var(--ai-slider-position-{{ ai_gen_id }}));
  }

  .ai-before-after-slider-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: var(--ai-slider-position-{{ ai_gen_id }});
    width: 4px;
    height: 100%;
    background-color: {{ block.settings.slider_color }};
    cursor: col-resize;
    transform: translateX(-2px);
    z-index: 3;
  }

  .ai-before-after-handle-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    width: {{ block.settings.handle_size }}px;
    height: {{ block.settings.handle_size }}px;
    background-color: {{ block.settings.slider_color }};
    border: 3px solid {{ block.settings.handle_border_color }};
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: col-resize;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-before-after-handle-{{ ai_gen_id }}::before,
  .ai-before-after-handle-{{ ai_gen_id }}::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
  }

  .ai-before-after-handle-{{ ai_gen_id }}::before {
    border-width: 6px 8px 6px 0;
    border-color: transparent {{ block.settings.handle_border_color }} transparent transparent;
    left: 8px;
  }

  .ai-before-after-handle-{{ ai_gen_id }}::after {
    border-width: 6px 0 6px 8px;
    border-color: transparent transparent transparent {{ block.settings.handle_border_color }};
    right: 8px;
  }

  .ai-before-after-label-{{ ai_gen_id }} {
    position: absolute;
    top: {{ block.settings.label_position_y }}px;
    padding: 8px 12px;
    background-color: {{ block.settings.label_bg_color }};
    color: {{ block.settings.label_text_color }};
    font-size: {{ block.settings.label_font_size }}px;
    font-weight: 600;
    border-radius: 4px;
    z-index: 2;
    pointer-events: none;
  }

  .ai-before-after-label-before-{{ ai_gen_id }} {
    left: {{ block.settings.label_position_x }}px;
  }

  .ai-before-after-label-after-{{ ai_gen_id }} {
    right: {{ block.settings.label_position_x }}px;
  }

  .ai-before-after-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: {{ block.settings.height }}px;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 16px;
  }

  .ai-before-after-placeholder-{{ ai_gen_id }} svg {
    width: 100px;
    height: 100px;
    opacity: 0.3;
  }

  @media screen and (max-width: 749px) {
    .ai-before-after-container-{{ ai_gen_id }} {
      height: {{ block.settings.height | times: 0.7 }}px;
    }

    .ai-before-after-placeholder-{{ ai_gen_id }} {
      height: {{ block.settings.height | times: 0.7 }}px;
    }

    .ai-before-after-handle-{{ ai_gen_id }} {
      width: {{ block.settings.handle_size | times: 0.8 }}px;
      height: {{ block.settings.handle_size | times: 0.8 }}px;
    }

    .ai-before-after-label-{{ ai_gen_id }} {
      font-size: {{ block.settings.label_font_size | times: 0.9 }}px;
      padding: 6px 10px;
    }
  }
{% endstyle %}

<before-after-slider-{{ ai_gen_id }}
  class="ai-before-after-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  {% if block.settings.before_image and block.settings.after_image %}
    <div
      class="ai-before-after-container-{{ ai_gen_id }}"
      style="--ai-slider-position-{{ ai_gen_id }}: 50%;"
    >
      <img
        src="{{ block.settings.before_image | image_url: width: 2000 }}"
        alt="{{ block.settings.before_image.alt | escape }}"
        class="ai-before-after-image-{{ ai_gen_id }} ai-before-after-before-{{ ai_gen_id }}"
        loading="lazy"
        width="{{ block.settings.before_image.width }}"
        height="{{ block.settings.before_image.height }}"
      >
      
      <img
        src="{{ block.settings.after_image | image_url: width: 2000 }}"
        alt="{{ block.settings.after_image.alt | escape }}"
        class="ai-before-after-image-{{ ai_gen_id }} ai-before-after-after-{{ ai_gen_id }}"
        loading="lazy"
        width="{{ block.settings.after_image.width }}"
        height="{{ block.settings.after_image.height }}"
      >

      <div class="ai-before-after-slider-{{ ai_gen_id }}">
        <div class="ai-before-after-handle-{{ ai_gen_id }}"></div>
      </div>

      {% if block.settings.show_labels %}
        {% if block.settings.before_label != blank %}
          <div class="ai-before-after-label-{{ ai_gen_id }} ai-before-after-label-before-{{ ai_gen_id }}">
            {{ block.settings.before_label }}
          </div>
        {% endif %}
        
        {% if block.settings.after_label != blank %}
          <div class="ai-before-after-label-{{ ai_gen_id }} ai-before-after-label-after-{{ ai_gen_id }}">
            {{ block.settings.after_label }}
          </div>
        {% endif %}
      {% endif %}
    </div>
  {% else %}
    <div class="ai-before-after-placeholder-{{ ai_gen_id }}">
      {{ 'image' | placeholder_svg_tag }}
    </div>
  {% endif %}
</before-after-slider-{{ ai_gen_id }}>

<script>
  (function() {
    class BeforeAfterSlider{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.isDragging = false;
        this.startX = 0;
        this.currentPosition = 50;
      }

      connectedCallback() {
        this.container = this.querySelector('.ai-before-after-container-{{ ai_gen_id }}');
        this.slider = this.querySelector('.ai-before-after-slider-{{ ai_gen_id }}');
        this.afterImage = this.querySelector('.ai-before-after-after-{{ ai_gen_id }}');
        
        if (!this.container || !this.slider || !this.afterImage) return;

        this.setupEventListeners();
        this.updateSliderPosition(this.currentPosition);
      }

      setupEventListeners() {
        this.container.addEventListener('mousedown', this.handleStart.bind(this));
        this.container.addEventListener('touchstart', this.handleStart.bind(this), { passive: false });
        
        document.addEventListener('mousemove', this.handleMove.bind(this));
        document.addEventListener('touchmove', this.handleMove.bind(this), { passive: false });
        
        document.addEventListener('mouseup', this.handleEnd.bind(this));
        document.addEventListener('touchend', this.handleEnd.bind(this));
        
        this.container.addEventListener('click', this.handleClick.bind(this));
      }

      handleStart(e) {
        e.preventDefault();
        this.isDragging = true;
        this.container.style.cursor = 'col-resize';
        
        const clientX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
        this.startX = clientX;
      }

      handleMove(e) {
        if (!this.isDragging) return;
        
        e.preventDefault();
        const clientX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
        
        const rect = this.container.getBoundingClientRect();
        const x = clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
        
        this.updateSliderPosition(percentage);
      }

      handleEnd() {
        this.isDragging = false;
        this.container.style.cursor = 'col-resize';
      }

      handleClick(e) {
        if (this.isDragging) return;
        
        const rect = this.container.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
        
        this.updateSliderPosition(percentage);
      }

      updateSliderPosition(percentage) {
        this.currentPosition = percentage;
        const position = percentage + '%';
        
        this.container.style.setProperty('--ai-slider-position-{{ ai_gen_id }}', position);
        this.afterImage.style.clipPath = `inset(0 0 0 ${position})`;
      }
    }

    customElements.define('before-after-slider-{{ ai_gen_id }}', BeforeAfterSlider{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Before After Slider",
  "settings": [
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "image_picker",
      "id": "before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image",
      "label": "After image"
    },
    {
      "type": "range",
      "id": "height",
      "min": 200,
      "max": 800,
      "step": 50,
      "unit": "px",
      "label": "Height",
      "default": 400
    },
    {
      "type": "header",
      "content": "Labels"
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show labels",
      "default": true
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "range",
      "id": "label_font_size",
      "min": 10,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Label font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "label_position_x",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Label horizontal position",
      "default": 20
    },
    {
      "type": "range",
      "id": "label_position_y",
      "min": 10,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Label vertical position",
      "default": 20
    },
    {
      "type": "color",
      "id": "label_bg_color",
      "label": "Label background color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "label_text_color",
      "label": "Label text color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Slider Style"
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider line color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "handle_size",
      "min": 30,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Handle size",
      "default": 40
    },
    {
      "type": "color",
      "id": "handle_border_color",
      "label": "Handle border color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    }
  ],
  "presets": [
    {
      "name": "Before After Slider"
    }
  ]
}
{% endschema %}