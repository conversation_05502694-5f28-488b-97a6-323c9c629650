{{ 'component-slideshow.css' | asset_url | stylesheet_tag }}

<div class="elegant-slideshow-section">
  <slideshow-component class="slider-mobile-gutter" role="region" aria-roledescription="carousel">
    <div class="slideshow banner grid grid--1-col slider slider--everywhere" id="ElegantSlider-{{ section.id }}" aria-live="polite" aria-atomic="true">
      {%- for block in section.blocks -%}
        <div class="slideshow__slide grid__item grid--1-col slider__slide" id="ElegantSlide-{{ section.id }}-{{ forloop.index }}" {{ block.shopify_attributes }} role="group" aria-roledescription="slide" aria-label="{{ forloop.index }} of {{ forloop.length }}" tabindex="-1">
          <div class="slideshow__media banner__media media">
            {%- if block.settings.video_url != blank -%}
              <video src="{{ block.settings.video_url }}" controls autoplay muted loop playsinline preload="metadata" style="width:100%;height:auto;object-fit:cover;display:block;min-height:200px;background:#000;"></video>
            {%- elsif block.settings.image != blank -%}
              {{ block.settings.image | image_url: width: 2000 | image_tag: loading: 'lazy', sizes: '100vw', widths: '400, 800, 1200, 1600, 2000' }}
            {%- else -%}
              <div style="background:#eee;width:100%;height:200px;display:flex;align-items:center;justify-content:center;">No media</div>
            {%- endif -%}
          </div>
          <div class="slideshow__text-wrapper banner__content page-width">
            <div class="slideshow__text banner__box content-container content-container--full-width-mobile gradient" style="backdrop-filter: blur(6px); background: rgba(255,255,255,0.75); border-radius: 1.2rem; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
              {%- if block.settings.heading != blank -%}
                <h2 class="banner__heading inline-richtext {{ block.settings.heading_size }}" style="font-weight:700;letter-spacing:-0.01em;">{{ block.settings.heading }}</h2>
              {%- endif -%}
              {%- if block.settings.text != blank -%}
                <div class="banner__text rte" style="font-size:1.15em;line-height:1.5;color:#222;">{{ block.settings.text }}</div>
              {%- endif -%}
              {%- if block.settings.button_label != blank -%}
                <div class="banner__buttons" style="margin-top:1.5em;">
                  <a href="{{ block.settings.button_link }}" class="button button--primary" style="padding:0.9em 2.2em;font-size:1.1em;border-radius:2em;box-shadow:0 2px 8px rgba(0,0,0,0.08);background:#111;color:#fff;transition:background 0.2s;">{{ block.settings.button_label }}</a>
                </div>
              {%- endif -%}
            </div>
          </div>
        </div>
      {%- endfor -%}
    </div>
    <div class="elegant-slideshow-pagination" style="display:flex;justify-content:center;align-items:center;margin-top:1.5em;gap:0.5em;">
      {%- for block in section.blocks -%}
        <button class="elegant-dot" style="width:14px;height:14px;border-radius:50%;background:#ddd;border:none;transition:background 0.2s;cursor:pointer;"></button>
      {%- endfor -%}
    </div>
  </slideshow-component>
</div>

{% schema %}
{
  "name": "Elegant Slideshow",
  "class": "section",
  "settings": [],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "settings": [
        { "type": "image_picker", "id": "image", "label": "Image" },
        { "type": "text", "id": "video_url", "label": "Video URL (optional)" },
        { "type": "inline_richtext", "id": "heading", "label": "Heading", "default": "<p>Slide headline</p>" },
        { "type": "richtext", "id": "text", "label": "Text", "default": "<p>Slide description goes here.</p>" },
        { "type": "text", "id": "button_label", "label": "Button label", "default": "Learn more" },
        { "type": "url", "id": "button_link", "label": "Button link" },
        { "type": "select", "id": "heading_size", "label": "Heading size", "options": [ { "value": "h2", "label": "H2" }, { "value": "h1", "label": "H1" }, { "value": "h0", "label": "Hero" } ], "default": "h1" }
      ]
    }
  ],
  "presets": [
    { "name": "Elegant Slideshow", "blocks": [ { "type": "slide" }, { "type": "slide" } ] }
  ]
}
{% endschema %} 