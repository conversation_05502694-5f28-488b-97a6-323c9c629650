{{ 'section-about-us.css' | asset_url | stylesheet_tag }}

<script>
// Professional About Us Page Enhancement
document.addEventListener('DOMContentLoaded', function() {
  // Add classes to existing rich text sections for better styling
  const richTextSections = document.querySelectorAll('.rich-text');

  richTextSections.forEach(function(section, index) {
    if (index === 0) {
      section.classList.add('about-hero', 'about-content');

      // Create professional layout structure
      const textBlock = section.querySelector('.rte');
      if (textBlock) {
        textBlock.classList.add('professional-content');

        // Style the subtitle
        const subtitle = textBlock.querySelector('em');
        if (subtitle) {
          subtitle.parentElement.classList.add('hero-subtitle');
        }

        // Style all h2 headings
        const h2Elements = textBlock.querySelectorAll('h2');
        h2Elements.forEach(function(h2) {
          h2.classList.add('section-heading');
        });

        // Style all h3 headings (services)
        const h3Elements = textBlock.querySelectorAll('h3');
        h3Elements.forEach(function(h3, i) {
          h3.classList.add('service-heading');

          // Wrap service content
          const nextP = h3.nextElementSibling;
          if (nextP && nextP.tagName === 'P') {
            const serviceCard = document.createElement('div');
            serviceCard.classList.add('service-card');

            h3.parentNode.insertBefore(serviceCard, h3);
            serviceCard.appendChild(h3);
            serviceCard.appendChild(nextP);
          }
        });

        // Style all regular paragraphs
        const paragraphs = textBlock.querySelectorAll('p');
        paragraphs.forEach(function(p) {
          if (!p.querySelector('strong') && !p.querySelector('em') && !p.classList.contains('hero-subtitle')) {
            p.classList.add('about-text');
          }
        });

        // Style the stats paragraph
        const statsP = Array.from(paragraphs).find(p => p.innerHTML.includes('Happy Travelers'));
        if (statsP) {
          const statsData = [
            { number: '500+', label: 'Happy Travelers' },
            { number: '50+', label: 'Destinations' },
            { number: '5+', label: 'Years Experience' },
            { number: '98%', label: 'Satisfaction Rate' }
          ];

          const statsGrid = document.createElement('div');
          statsGrid.classList.add('stats-grid');

          statsData.forEach(function(stat) {
            const statItem = document.createElement('div');
            statItem.classList.add('stat-item');
            statItem.innerHTML = `
              <div class="stat-number">${stat.number}</div>
              <div class="stat-label">${stat.label}</div>
            `;
            statsGrid.appendChild(statItem);
          });

          statsP.parentNode.replaceChild(statsGrid, statsP);
        }

        // Style CTA
        const ctaP = Array.from(paragraphs).find(p => {
          const strong = p.querySelector('strong');
          return strong && strong.textContent.includes('Your journey of discovery');
        });
        if (ctaP) {
          ctaP.classList.add('cta-section');

          // Add CTA button
          const ctaButton = document.createElement('a');
          ctaButton.href = '/pages/contact';
          ctaButton.classList.add('cta-button');
          ctaButton.textContent = 'Start Planning Your Journey';
          ctaP.appendChild(ctaButton);
        }
      }
    }
  });

  // Add professional styling to contact form section
  const contactSection = document.querySelector('.contact-form');
  if (contactSection) {
    contactSection.classList.add('contact-form-section');
  }

  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        entry.target.classList.add('revealed');
      }
    });
  }, observerOptions);

  // Add scroll reveal to elements
  const elementsToReveal = document.querySelectorAll('.about-content h1, .about-content h2, .about-content p, .services-grid, .service-card, .stats-grid');
  elementsToReveal.forEach(function(el) {
    el.classList.add('scroll-reveal');
    observer.observe(el);
  });

  // Animate statistics numbers when they come into view
  const statNumbers = document.querySelectorAll('.stat-number');
  statNumbers.forEach(function(statEl) {
    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(function(entry) {
        if (entry.isIntersecting) {
          animateNumber(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });

    observer.observe(statEl);
  });

  // Number animation function
  function animateNumber(element) {
    const finalNumber = element.textContent;
    const isPercentage = finalNumber.includes('%');
    const isPlusSign = finalNumber.includes('+');
    const numericValue = parseInt(finalNumber.replace(/[^\d]/g, ''));

    let currentNumber = 0;
    const increment = numericValue / 50; // 50 steps for smooth animation

    const timer = setInterval(function() {
      currentNumber += increment;
      if (currentNumber >= numericValue) {
        currentNumber = numericValue;
        clearInterval(timer);
      }

      let displayValue = Math.floor(currentNumber).toString();
      if (isPlusSign) displayValue += '+';
      if (isPercentage) displayValue += '%';

      element.textContent = displayValue;
    }, 30);
  }

  // Add click handlers to service cards for better UX
  const serviceCards = document.querySelectorAll('.service-card');
  serviceCards.forEach(function(card) {
    card.addEventListener('click', function() {
      // Add a subtle click effect
      this.style.transform = 'scale(0.98)';
      setTimeout(() => {
        this.style.transform = '';
      }, 150);
    });

    // Add keyboard accessibility
    card.setAttribute('tabindex', '0');
    card.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.click();
      }
    });
  });

  // Smooth scrolling for any internal links
  const internalLinks = document.querySelectorAll('a[href^="#"]');
  internalLinks.forEach(function(link) {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});
</script>

{% schema %}
{
  "name": "About Us Styles",
  "settings": []
}
{% endschema %}
