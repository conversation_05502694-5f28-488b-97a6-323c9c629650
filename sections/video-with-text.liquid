{{ 'component-image-with-text.css' | asset_url | stylesheet_tag }}
{{ 'video-with-text-mobile.js' | asset_url | script_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  /* --- Mobile order override for video-with-text --- */
  @media screen and (max-width: 749px) {
    .section-{{ section.id }}-padding .image-with-text__grid {
      display: flex;
      flex-direction: column;
    }
    .section-{{ section.id }}-padding .image-with-text__text-item {
      order: 1;
    }
    .section-{{ section.id }}-padding .image-with-text__media-item {
      order: 3;
    }
    .section-{{ section.id }}-padding .image-with-text__content {
      display: flex;
      flex-direction: column;
    }
    .section-{{ section.id }}-padding .image-with-text__heading {
      order: 1;
    }
    .section-{{ section.id }}-padding .image-with-text__text {
      order: 2;
    }
    .section-{{ section.id }}-padding .button {
      order: 4;
      align-self: flex-start;
    }
  }
  @media screen and (max-width: 749px) {
    .image-with-text__grid {
      display: block !important;
      width: 100% !important;
      padding: 0 !important;
      margin: 0 !important;
    }
    .image-with-text__text-item { display: none !important; }
    .image-with-text__text-item--mobile {
      display: block !important;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
    }
    .image-with-text__media-item {
      display: block !important;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      float: none !important;
      clear: both !important;
    }
    */
    .image-with-text__button-item--mobile {
      display: block;
      width: 100% ;
      margin: 0 ;
      padding: 0 ;
      text-align: center ;
    }
    /* Hide all other grid__item except mobile text, video, and mobile button */
    .image-with-text__grid > .grid__item:not(.image-with-text__media-item):not(.image-with-text__text-item--mobile) {
      display: none !important;
    }
  }
  @media screen and (min-width: 750px) {
    .image-with-text__text-item--mobile { display: none !important; }
    .image-with-text__text-item--desktop { display: block !important; }
    .image-with-text__button-item--mobile { display: none !important; }
  }
{%- endstyle -%}

{%- liquid
  assign fetch_priority = 'auto'
  if section.index == 1
    assign fetch_priority = 'high'
  endif
  if section.settings.color_scheme == section.settings.section_color_scheme and section.settings.content_layout == 'no-overlap'
    assign remove_color_classes = true
  endif
-%}

<div class="section-{{ section.id }}-padding gradient color-{{ section.settings.section_color_scheme }}">
  <div class="page-width">
    <div class="image-with-text image-with-text--{{ section.settings.content_layout }} isolate{% if settings.text_boxes_border_thickness > 0 and settings.text_boxes_border_opacity > 0 and settings.media_border_thickness > 0 and settings.media_border_opacity > 0 %} collapse-borders{% endif %}{% unless section.settings.color_scheme == section.settings.section_color_scheme and settings.media_border_thickness > 0 and settings.text_boxes_shadow_opacity == 0 and settings.text_boxes_border_thickness == 0 or settings.text_boxes_border_opacity == 0 %} collapse-corners{% endunless %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      <div class="image-with-text__grid grid grid--gapless grid--1-col grid--{% if section.settings.desktop_image_width == 'medium' %}2-col-tablet{% else %}3-col-tablet{% endif %}{% if section.settings.layout == 'text_first' %} image-with-text__grid--reverse{% endif %}">
        
        <!-- MOBILE: Text/Heading block (visible only on mobile, button removed) -->
        <div class="image-with-text__text-item image-with-text__text-item--mobile" style="display:none;">
          <div
            id="VideoWithText--mobile-{{ section.id }}"
            class="image-with-text__content image-with-text__content--{{ section.settings.desktop_content_position }} image-with-text__content--desktop-{{ section.settings.desktop_content_alignment }} image-with-text__content--mobile-{{ section.settings.mobile_content_alignment }} image-with-text__content--{{ section.settings.height }} content-container{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme }}{% else %} background-transparent{% endunless %}"
          >
            {%- for block in section.blocks -%}
              {% case block.type %}
                {%- when 'heading' -%}
                  <h2
                    class="image-with-text__heading inline-richtext {{ block.settings.heading_size }}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.heading }}
                  </h2>
                {%- when 'caption' -%}
                  <p
                    class="image-with-text__text image-with-text__text--caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.caption | escape }}
                  </p>
                {%- when 'text' -%}
                  <div class="image-with-text__text rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                    {{ block.settings.text }}
                  </div>
                {%- when 'button' -%}

              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
        <!-- END MOBILE block -->

        <div class="image-with-text__media-item image-with-text__media-item--{{ section.settings.desktop_image_width }} image-with-text__media-item--{{ section.settings.desktop_content_position }} grid__item">
          <div
            class="image-with-text__media image-with-text__media--{{ section.settings.height }} global-media-settings{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme }}{% else %} background-transparent{% endunless %} media"
          >
            {%- if section.settings.video_url != blank -%}
              <video 
                src="{{ section.settings.video_url }}" 
                controls 
                autoplay 
                muted 
                loop 
                playsinline 
                preload="metadata"
                style="width:100%;height:auto;object-fit:cover;display:block;min-height:200px;background:#000;"
                onloadstart="this.style.display='block';"
                onerror="this.style.display='none';this.nextElementSibling.style.display='block';"
              ></video>
              <div style="display:none;background:#000;width:100%;height:200px;color:#fff;display:flex;align-items:center;justify-content:center;text-align:center;padding:20px;">
                <p>Video loading...<br>If video doesn't load, please check the URL.</p>
              </div>
            {%- else -%}
              <div style="background:#000;width:100%;height:200px;color:#fff;display:flex;align-items:center;justify-content:center;text-align:center;padding:20px;">
                <p>Please add a video URL in the section settings</p>
              </div>
            {%- endif -%}
          </div>
        </div>
        
        <!-- MOBILE: Button block (only after video, only on mobile) -->
        <div class="image-with-text__button-item--mobile" style="display:none; width:100%; text-align:center; margin-top: 1.5rem;">
          {%- for block in section.blocks -%}
            {% if block.type == 'button' and block.settings.button_label != blank %}
              <a
                {% if block.settings.button_link == blank %}
                  role="link" aria-disabled="true"
                {% else %}
                  href="{{ block.settings.button_link }}"
                {% endif %}
                class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                {{ block.shopify_attributes }}
              >
                {{ block.settings.button_label | escape }}
              </a>
            {% endif %}
          {%- endfor -%}
        </div>
        <!-- END MOBILE BUTTON block -->

        <!-- DESKTOP: Text/Heading/Button block (hidden on mobile) -->
        <div class="image-with-text__text-item image-with-text__text-item--desktop grid__item">
          <div
            id="VideoWithText--{{ section.id }}"
            class="image-with-text__content image-with-text__content--{{ section.settings.desktop_content_position }} image-with-text__content--desktop-{{ section.settings.desktop_content_alignment }} image-with-text__content--mobile-{{ section.settings.mobile_content_alignment }} image-with-text__content--{{ section.settings.height }} content-container{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme }}{% else %} background-transparent{% endunless %}"
          >
            {%- for block in section.blocks -%}
              {% case block.type %}
                {%- when 'heading' -%}
                  <h2
                    class="image-with-text__heading inline-richtext {{ block.settings.heading_size }}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.heading }}
                  </h2>
                {%- when 'caption' -%}
                  <p
                    class="image-with-text__text image-with-text__text--caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.caption | escape }}
                  </p>
                {%- when 'text' -%}
                  <div class="image-with-text__text rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                    {{ block.settings.text }}
                  </div>
                {%- when 'button' -%}
                  {%- if block.settings.button_label != blank -%}
                    <a
                      {% if block.settings.button_link == blank %}
                        role="link" aria-disabled="true"
                      {% else %}
                        href="{{ block.settings.button_link }}"
                      {% endif %}
                      class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block.settings.button_label | escape }}
                    </a>
                  {%- endif -%}
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
        <!-- END DESKTOP block -->

      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Video with text",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "video_url",
      "label": "Video URL (e.g. https://cdn.shopify.com/videos/c/o/v/yourvideo.mp4)"
    },
    {
      "type": "select",
      "id": "height",
      "options": [
        { "value": "adapt", "label": "Adapt" },
        { "value": "small", "label": "Small" },
        { "value": "medium", "label": "Medium" },
        { "value": "large", "label": "Large" }
      ],
      "default": "adapt",
      "label": "Section height"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        { "value": "small", "label": "Small" },
        { "value": "medium", "label": "Medium" },
        { "value": "large", "label": "Large" }
      ],
      "default": "medium",
      "label": "Desktop media width"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        { "value": "image_first", "label": "Media first" },
        { "value": "text_first", "label": "Text first" }
      ],
      "default": "image_first",
      "label": "Layout"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        { "value": "top", "label": "Top" },
        { "value": "middle", "label": "Middle" },
        { "value": "bottom", "label": "Bottom" }
      ],
      "default": "top",
      "label": "Desktop content position"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        { "value": "left", "label": "Left" },
        { "value": "center", "label": "Center" },
        { "value": "right", "label": "Right" }
      ],
      "default": "left",
      "label": "Desktop content alignment"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        { "value": "left", "label": "Left" },
        { "value": "center", "label": "Center" },
        { "value": "right", "label": "Right" }
      ],
      "default": "left",
      "label": "Mobile content alignment"
    },
    {
      "type": "color_scheme",
      "id": "section_color_scheme",
      "label": "Section background color scheme",
      "default": "scheme-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Content color scheme",
      "default": "scheme-1"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "Your headline here",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            { "value": "h2", "label": "H2" },
            { "value": "h1", "label": "H1" },
            { "value": "h0", "label": "Hero" },
            { "value": "hxl", "label": "XL" },
            { "value": "hxxl", "label": "XXL" }
          ],
          "default": "h1",
          "label": "Heading size"
        }
      ]
    },
    {
      "type": "caption",
      "name": "Caption",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Your caption here",
          "label": "Caption"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            { "value": "subtitle", "label": "Subtitle" },
            { "value": "caption-with-letter-spacing", "label": "Caption with letter spacing" }
          ],
          "default": "caption-with-letter-spacing",
          "label": "Text style"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            { "value": "small", "label": "Small" },
            { "value": "medium", "label": "Medium" },
            { "value": "large", "label": "Large" }
          ],
          "default": "medium",
          "label": "Caption size"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Your text here</p>",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            { "value": "body", "label": "Body" },
            { "value": "subtitle", "label": "Subtitle" }
          ],
          "default": "body",
          "label": "Text style"
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Click me",
          "label": "Button label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "default": false,
          "label": "Outline button"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Video with text",
      "blocks": [
        { "type": "heading" },
        { "type": "text" },
        { "type": "button" }
      ]
    }
  ]
}
{% endschema %} 