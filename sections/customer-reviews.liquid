{{ 'section-customer-reviews.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="customer-reviews page-width section-{{ section.id }}-padding">
    {%- if section.settings.heading != blank -%}
      <h2 class="title title-wrapper--no-top-margin inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        {{ section.settings.heading }}
      </h2>
    {%- endif -%}
    
    <div class="reviews-grid{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'review' -%}
            <div class="review-card" {{ block.shopify_attributes }}>
              <div class="review-header">
                {%- if block.settings.customer_image != blank -%}
                  <div class="review-avatar">
                    <img
                      src="{{ block.settings.customer_image | image_url: width: 80 }}"
                      alt="{{ block.settings.customer_name }}"
                      width="60"
                      height="60"
                      loading="lazy"
                    >
                  </div>
                {%- else -%}
                  <div class="review-avatar review-avatar--placeholder">
                    <span>{{ block.settings.customer_name | slice: 0 }}</span>
                  </div>
                {%- endif -%}
                <div class="review-info">
                  <h3 class="review-name">{{ block.settings.customer_name }}</h3>
                  <div class="review-rating">
                    {%- for i in (1..5) -%}
                      {%- if i <= block.settings.rating -%}
                        <span class="star star--filled">★</span>
                      {%- else -%}
                        <span class="star">☆</span>
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
              </div>
              <div class="review-content">
                <p>{{ block.settings.review_text }}</p>
              </div>
            </div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Customer Reviews",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "Latest Reviews",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "review",
      "name": "Review",
      "settings": [
        {
          "type": "text",
          "id": "customer_name",
          "default": "Customer Name",
          "label": "Customer name"
        },
        {
          "type": "image_picker",
          "id": "customer_image",
          "label": "Customer photo (optional)"
        },
        {
          "type": "range",
          "id": "rating",
          "min": 1,
          "max": 5,
          "step": 1,
          "default": 5,
          "label": "Rating (stars)"
        },
        {
          "type": "textarea",
          "id": "review_text",
          "default": "This is an amazing service! Highly recommended.",
          "label": "Review text"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Customer Reviews",
      "blocks": [
        {
          "type": "review",
          "settings": {
            "customer_name": "Tatiana Nikulina",
            "rating": 5,
            "review_text": "Many thanks to the Riviera Group employees for a wonderful vacation in Georgia. Thank you for the excellent organization, for the quality work, for the warmth and understanding, to the general director, our David (yes, he is now our dear). We traveled in a large group. It was an amazing time spent in beautiful places. We learned history, legends, customs of the people, and a wonderful guide, our Katya, helped us. She could answer any question."
          }
        },
        {
          "type": "review",
          "settings": {
            "customer_name": "Iveta Rankevitsa",
            "rating": 5,
            "review_text": "Our trip to Georgia lasted 11 days and throughout this time our group (31 people from Klaipeda, Lithuania) could not be happier! The director of the company David became our guardian angel! The highest professionalism and charm of the people who accompanied us on our trips helped us to learn and truly love the culture and history of this wonderful country."
          }
        },
        {
          "type": "review",
          "settings": {
            "customer_name": "Polina Gusakova",
            "rating": 5,
            "review_text": "Thank you for a wonderful holiday, a rich program, excellent support and a wonderful team of your company! Special thanks to the group leader Artur, driver Kakha (for our group he became a national hero, virtuoso driving!), managers Suzy Victoria, charming and possessing unique information about this beautiful sunny country of Georgia!"
          }
        },
        {
          "type": "review",
          "settings": {
            "customer_name": "Marina Kowalski",
            "rating": 5,
            "review_text": "Exceptional service from start to finish! The team at Foxy Travel made our Georgian adventure unforgettable. Every detail was perfectly planned, from accommodation to guided tours. Our guide was knowledgeable and passionate about sharing Georgian culture. I can't wait to book another trip with them!"
          }
        },
        {
          "type": "review",
          "settings": {
            "customer_name": "Alexander Petrov",
            "rating": 5,
            "review_text": "Outstanding experience! The professionalism and attention to detail exceeded all expectations. Our driver was skilled and friendly, making us feel safe throughout the mountain roads. The itinerary was perfectly balanced between sightseeing and relaxation. Highly recommend Foxy Travel for anyone visiting Georgia!"
          }
        },
        {
          "type": "review",
          "settings": {
            "customer_name": "Sofia Dimitrova",
            "rating": 5,
            "review_text": "What an incredible journey! Foxy Travel organized everything flawlessly. The local cuisine experiences, wine tastings, and cultural sites were absolutely amazing. Our group felt like VIPs throughout the entire trip. The memories we made will last a lifetime. Thank you for such wonderful hospitality!"
          }
        }
      ]
    }
  ]
}
{% endschema %}
