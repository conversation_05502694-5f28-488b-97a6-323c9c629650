{"settings_schema": {"colors": {"name": "<PERSON><PERSON>", "settings": {"background": {"label": "Hi<PERSON>grund"}, "background_gradient": {"label": "Hintergrundfarbverlauf", "info": "<PERSON><PERSON> m<PERSON><PERSON>, ersetzt der Hintergrundfarbverlauf den Hintergrund."}, "text": {"label": "Text"}, "button_background": {"label": "Hintergrund für durchgehende Schaltfläche"}, "button_label": {"label": "Beschriftung für durchgehende Schaltfläche"}, "secondary_button_label": {"label": "Umriss-Schaltfläche"}, "shadow": {"label": "<PERSON><PERSON><PERSON>"}}}, "typography": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"type_header_font": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"content": "Überschriften"}, "header__2": {"content": "Nachricht"}, "type_body_font": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "heading_scale": {"label": "Maßstab"}, "body_scale": {"label": "Maßstab"}}}, "social-media": {"name": "Social Media", "settings": {"social_twitter_link": {"label": "X / Twitter", "info": "https://x.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://vimeo.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Social-Media-Konten"}}}, "currency_format": {"name": "Währungsformat", "settings": {"currency_code_enabled": {"label": "Währungscodes"}, "paragraph": "Warenkorb- und Checkout-Preise zeigen immer Währungscodes an"}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Seitenbreite"}, "spacing_sections": {"label": "Platz zwischen Vorlagenabschnitten"}, "header__grid": {"content": "<PERSON><PERSON>"}, "paragraph__grid": {"content": "Wirkt sich auf Bereiche mit mehreren Spalten oder Reihen aus"}, "spacing_grid_horizontal": {"label": "Horizontaler Abstand"}, "spacing_grid_vertical": {"label": "<PERSON><PERSON><PERSON><PERSON> Abstand"}}}, "search_input": {"name": "Suchverhalten", "settings": {"predictive_search_enabled": {"label": "Suchvorschläge"}, "predictive_search_show_vendor": {"label": "Produktanbieter", "info": "Wird angeze<PERSON>t, wenn Suchvorschläge aktiviert sind"}, "predictive_search_show_price": {"label": "Produktpreis", "info": "Wird angeze<PERSON>t, wenn Suchvorschläge aktiviert sind"}}}, "global": {"settings": {"header__border": {"content": "Rand"}, "header__shadow": {"content": "<PERSON><PERSON><PERSON>"}, "blur": {"label": "Weichzeichnen"}, "corner_radius": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "horizontal_offset": {"label": "Horizontaler Offset"}, "vertical_offset": {"label": "<PERSON><PERSON><PERSON><PERSON> Offset"}, "thickness": {"label": "<PERSON><PERSON>"}, "opacity": {"label": "Opazität"}, "image_padding": {"label": "Bild-Padding"}, "text_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Textausrichtung"}}}, "badges": {"name": "Badges", "settings": {"position": {"options__1": {"label": "Unten links"}, "options__2": {"label": "Unten rechts"}, "options__3": {"label": "Oben links"}, "options__4": {"label": "<PERSON><PERSON> rechts"}, "label": "Position auf Karten"}, "sale_badge_color_scheme": {"label": "Farbschema für Sale-Badges"}, "sold_out_badge_color_scheme": {"label": "Farbschema für Ausverkauft-Badges"}}}, "buttons": {"name": "Schaltflächen"}, "variant_pills": {"name": "Varianten-Kapseln", "paragraph": "Varianten-Kapseln sind eine Möglichkeit, deine [Produktvarianten](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block) zu präsentieren"}, "inputs": {"name": "Eingaben"}, "content_containers": {"name": "Inhalts-Container"}, "popups": {"name": "Dropdown-Listen und Pop-ups", "paragraph": "Wirkt sich auf Bereiche wie das Dropdown-Menü für die Navigation, modale Pop-ups und Warenkorb-Pop-ups aus"}, "media": {"name": "Medien"}, "drawers": {"name": "Einschübe"}, "cart": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "Art", "drawer": {"label": "<PERSON><PERSON><PERSON>"}, "page": {"label": "Seite"}, "notification": {"label": "Pop-up-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "show_cart_note": {"label": "Warenkorbanmerkung"}, "cart_drawer": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collection": {"label": "Kollektion", "info": "Wird angeze<PERSON>t, wenn der Warenkorbeinschub leer ist"}}}}, "cards": {"name": "Produktkarten", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Optik"}}}, "collection_cards": {"name": "Kollektionskarten", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Optik"}}}, "blog_cards": {"name": "Blog-Karten", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Optik"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Breite"}, "favicon": {"label": "Favicon", "info": "Mit 32 px x 32 px angezeigt"}}}, "brand_information": {"name": "Markeninformationen", "settings": {"brand_headline": {"label": "Überschrift"}, "brand_description": {"label": "Beschreibung"}, "brand_image": {"label": "Bild"}, "brand_image_width": {"label": "Bildbreite"}, "paragraph": {"content": "Wird in der Fußzeile des Markeninfoblocks angezeigt"}}}, "animations": {"name": "<PERSON><PERSON>", "settings": {"animations_reveal_on_scroll": {"label": "<PERSON><PERSON> Abschnitte e<PERSON>nden"}, "animations_hover_elements": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Vertikal-Lift"}, "label": "Hover-Effekt", "info": "Wirkt sich auf Karten und Schaltflächen aus", "options__3": {"label": "3D-Lift"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Padding", "padding_top": "<PERSON><PERSON>", "padding_bottom": "Unten"}, "spacing": "Abstand", "colors": {"label": "Farbschema", "has_cards_info": "Aktualisiere deine Theme-Einstellungen, um das Farbschema der Karte zu ändern."}, "heading_size": {"label": "Größe der Überschrift", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Extra groß"}, "options__5": {"label": "Extra, extra groß"}}, "image_shape": {"options__1": {"label": "Standard"}, "options__2": {"label": "Bogen"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Chevron nach links"}, "options__5": {"label": "Chevron nach rechts"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Parallelogramm"}, "options__8": {"label": "Rund"}, "label": "Bildform"}, "animation": {"content": "<PERSON><PERSON>", "image_behavior": {"options__1": {"label": "<PERSON><PERSON>(r)"}, "options__2": {"label": "Atmosphärische Bewegung"}, "label": "Animation", "options__3": {"label": "Feste Hintergrundposition"}, "options__4": {"label": "<PERSON><PERSON> her<PERSON>zo<PERSON>n"}}}}, "announcement-bar": {"name": "Ankündigungsleiste", "blocks": {"announcement": {"name": "Ankündigung", "settings": {"text": {"label": "Text", "default": "<PERSON><PERSON><PERSON><PERSON> in unserem Shop"}, "link": {"label": "Link"}, "text_alignment": {"label": "Textausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "settings": {"auto_rotate": {"label": "Automatisch rotierende Ankündigungen"}, "change_slides_speed": {"label": "Ändern alle"}, "show_social": {"label": "Social Media-Symbole", "info": "[Social-Media-<PERSON><PERSON><PERSON> verwal<PERSON>](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Auswahl für Land/Region", "info": "[<PERSON>änder/Regionen verwalten](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "[<PERSON><PERSON><PERSON> verwalten](/admin/settings/languages)"}, "heading_utilities": {"content": "Versorgungsunternehmen"}, "paragraph": {"content": "Wird nur auf großen Bildschirmen angezeigt"}}, "presets": {"name": "Ankündigungsleiste"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Überschrift", "default": "Multimedia-Collage"}, "desktop_layout": {"label": "Layout", "options__1": {"label": "Großer Block zuerst"}, "options__2": {"label": "Großer Block zuletzt"}}, "mobile_layout": {"label": "Mobiles Layout", "options__1": {"label": "Collage"}, "options__2": {"label": "<PERSON>lt<PERSON>"}}, "card_styles": {"label": "Kartendesign", "info": "Individuelle Kartendesigns verwalten [Theme-Einstellungen](/editor?context=theme&category=product%20cards)", "options__1": {"label": "Individuelle Kartendesigns verwenden"}, "options__2": {"label": "Alle als Produktkarten gestalten"}}, "header_layout": {"content": "Layout"}}, "blocks": {"image": {"name": "Bild", "settings": {"image": {"label": "Bild"}}}, "product": {"name": "Produkt", "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "second_image": {"label": "Hover-Effekt mit zweitem Bild"}}}, "collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "info": "Video wird in einem Pop-up abgespielt, wenn der Abschnitt andere Blöcke enthält.", "placeholder": "YouTube- oder Vimeo-URL verwenden"}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "default": "Beschreibe das Video"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Kollektionsliste", "settings": {"title": {"label": "Überschrift", "default": "Kollektionen"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "Quadrat"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "Schaltfläche \"Alle anzeigen\"", "info": "<PERSON><PERSON><PERSON>, wenn die Liste mehr als die angezeigten Kollektionen enthält"}, "columns_desktop": {"label": "Spalten"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Spalten", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_layout": {"content": "Layout"}}, "blocks": {"featured_collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Kollektionsliste"}}, "contact-form": {"name": "Kontaktformular", "presets": {"name": "Kontaktformular"}, "settings": {"title": {"default": "Kontaktformular", "label": "Titel"}}}, "custom-liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Liquid-Code", "info": "Füge App-Snippets oder anderen Code hinzu, um fortgeschrittene Anpassungen zu erstellen. [Mehr Informationen](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Benutzerdefiniertes Liquid"}}, "featured-blog": {"name": "Blog-Beiträge", "settings": {"heading": {"label": "Überschrift", "default": "Blog-Beiträge"}, "blog": {"label": "Blog"}, "post_limit": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "Schaltfläche \"Alle anzeigen\"", "info": "<PERSON><PERSON><PERSON>, wenn der Blog mehr als die angezeigten Beiträge enthält"}, "show_image": {"label": "Feature-Bild"}, "show_date": {"label": "Datum"}, "show_author": {"label": "Autor"}, "columns_desktop": {"label": "Spalten"}, "layout_header": {"content": "Layout"}, "text_header": {"content": "Text"}}, "presets": {"name": "Blog-Beiträge"}}, "featured-collection": {"name": "Vorgestellte Kollektion", "settings": {"title": {"label": "Überschrift", "default": "Vorgestellte Kollektion"}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "Schaltfläche \"Alle anzeigen\"", "info": "<PERSON><PERSON><PERSON>, wenn die Kollektion mehr als die angezeigten Produkte enthält"}, "header": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "Quadrat"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "show_rating": {"label": "Produktbewertung", "info": "<PERSON><PERSON>r Bewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Schnelles Hinzufügen"}, "columns_desktop": {"label": "Spalten"}, "description": {"label": "Beschreibung"}, "show_description": {"label": "Kollektionsbeschreibung im Admin-Panel anzeigen"}, "description_style": {"label": "Beschreibungsstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "view_all_style": {"options__1": {"label": "Link"}, "options__2": {"label": "Umriss-Schaltfläche"}, "options__3": {"label": "Durchgehende Schaltfläche"}, "label": "Stil \"Alles anzeigen\""}, "enable_desktop_slider": {"label": "<PERSON><PERSON><PERSON>"}, "full_width": {"label": "Produkte mit voller Breite"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Spalten", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "header_text": {"content": "Text"}, "header_collection": {"content": "Kollektions-Layout"}}, "presets": {"name": "Vorgestellte Kollektion"}}, "footer": {"name": "Fußzeile", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Überschrift", "default": "Quick-Links"}, "menu": {"label": "<PERSON><PERSON>"}}}, "text": {"name": "Text", "settings": {"heading": {"label": "Überschrift", "default": "Titel"}, "subtext": {"label": "Subtext", "default": "<p><PERSON><PERSON>ktinformationen, Shop-Details und Markeninhalte mit deinen Kunden.</p>"}}}, "brand_information": {"name": "Markeninformationen", "settings": {"paragraph": {"content": "Markeninfos verwalten in [Theme-Einstellungen](/editor?context=theme&category=brand%20information)"}, "show_social": {"label": "Social Media-Symbole", "info": "[Social-Media-<PERSON><PERSON><PERSON> verwal<PERSON>](/editor?context=theme&category=social%20media)"}}}}, "settings": {"newsletter_enable": {"label": "E-Mail-Anmeldung"}, "newsletter_heading": {"label": "Überschrift", "default": "Abonniere unsere E-Mails"}, "header__1": {"content": "E-Mail-Anmeldung", "info": "Registrierungen hinzufügen [Kundenprofile](https://help.shopify.com/manual/customers/manage-customers)"}, "show_social": {"label": "Social Media-Symbole", "info": "[Social-Media-<PERSON><PERSON><PERSON> verwal<PERSON>](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Auswahl für Land/Region", "info": "[<PERSON>änder/Regionen verwalten](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "[<PERSON><PERSON><PERSON> verwalten](/admin/settings/languages)"}, "payment_enable": {"label": "Symbole für Zahlungsmethode"}, "margin_top": {"label": "<PERSON>berer Rand"}, "show_policy": {"label": "<PERSON><PERSON> <PERSON> Richtlinien", "info": "[<PERSON><PERSON><PERSON><PERSON> verwalten](/admin/settings/legal)"}, "header__9": {"content": "Versorgungsunternehmen"}, "enable_follow_on_shop": {"label": "In Shop folgen", "info": "Shop Pay muss aktiviert sein. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}}}, "header": {"name": "Header", "settings": {"logo_position": {"label": "Logo-Position", "options__1": {"label": "Mitte links"}, "options__2": {"label": "Oben links"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON>"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Trennlinie"}, "margin_bottom": {"label": "Un<PERSON>er Rand"}, "menu_type_desktop": {"label": "Menütyp", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Mega-Menü"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "mobile_logo_position": {"label": "Logo-Position für mobile Darstellung", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Links"}}, "logo_help": {"content": "Bearbeite dein Logo in den [Theme-Einstellungen](/editor?context=theme&category=logo)"}, "sticky_header_type": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Immer"}, "options__4": {"label": "Immer, Größe des Logos reduzieren"}}, "enable_country_selector": {"label": "Auswahl für Land/Region", "info": "[<PERSON>änder/Regionen verwalten](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "[<PERSON><PERSON><PERSON> verwalten](/admin/settings/languages)"}, "header__1": {"content": "Farbe"}, "menu_color_scheme": {"label": "Menü-Farbschema"}, "enable_customer_avatar": {"label": "Avatar für Kundenkonto", "info": "<PERSON><PERSON> sicht<PERSON>, wenn Kunden mit Shop angemeldet sind. [Kundenkonten verwalten](/admin/settings/customer_accounts)"}, "header__utilities": {"content": "Versorgungsunternehmen"}}}, "image-banner": {"name": "Bild-Banner", "settings": {"image": {"label": "Bild 1"}, "image_2": {"label": "Bild 2"}, "stack_images_on_mobile": {"label": "Gestapelte Bilder"}, "show_text_box": {"label": "Container"}, "image_overlay_opacity": {"label": "Überlagerungsdeckkraft"}, "show_text_below": {"label": "Container"}, "image_height": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "An erstes Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_content_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "<PERSON>ten zent<PERSON>t"}, "options__9": {"label": "Unten rechts"}, "label": "Position"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung"}, "mobile": {"content": "Mobiles Layout"}, "content": {"content": "Inhalt"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift", "default": "Bild-Banner"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text", "default": "Stelle Kunden Details zu Banner-Bildern oder Inhalt auf der Vorlage zur Verfügung."}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}, "label": "Optik"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Etikett", "info": "<PERSON><PERSON> Ausblenden leer lassen", "default": "Schaltflächenbeschriftung"}, "button_link_1": {"label": "Link"}, "button_style_secondary_1": {"label": "Umriss-Stil"}, "button_label_2": {"label": "Etikett", "info": "<PERSON><PERSON> Ausblenden leer lassen", "default": "Schaltflächenbeschriftung"}, "button_link_2": {"label": "Link"}, "button_style_secondary_2": {"label": "Umriss-Stil"}, "header_1": {"content": "Schaltfläche 1"}, "header_2": {"content": "Schaltfläche 2"}}}}, "presets": {"name": "Bild-Banner"}}, "image-with-text": {"name": "Bild mit Text", "settings": {"image": {"label": "Bild"}, "height": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON>", "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Zweites Bild"}, "label": "Platzierung"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Breite"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Position"}, "content_layout": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Überlappung"}, "label": "Layout"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung Mobilgerät"}, "header": {"content": "Inhalt"}, "header_colors": {"content": "<PERSON><PERSON>"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift", "default": "Bild mit Text"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text", "default": "<p>Kombiniere Text mit einem Bild, um den Fokus auf dein Produkt, deine Kollektion oder deinen Blog-Beitrag zu richten. Du kannst außerdem weitere Details über die Verfügbarkeit oder den Stil und sogar eine Bewertung hinzufügen.</p>"}, "text_style": {"label": "Optik", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Etikett", "info": "<PERSON><PERSON> Ausblenden leer lassen", "default": "Schaltflächenbeschriftung"}, "button_link": {"label": "Link"}, "outline_button": {"label": "Umriss-Stil"}}}, "caption": {"name": "Bildtext", "settings": {"text": {"label": "Text", "default": "Tagline hinzufügen"}, "text_style": {"label": "Optik", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Größe", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Bild mit Text"}}, "main-article": {"name": "Blog-Beitrag", "blocks": {"featured_image": {"name": "Feature-Bild", "settings": {"image_height": {"label": "Bildhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titel", "settings": {"blog_show_date": {"label": "Datum"}, "blog_show_author": {"label": "Autor"}}}, "content": {"name": "Inhalt"}, "share": {"name": "Teilen", "settings": {"text": {"label": "Text", "default": "Teilen"}}}}}, "main-blog": {"name": "Blog-Beiträge", "settings": {"show_image": {"label": "Feature-Bild"}, "show_date": {"label": "Datum"}, "show_author": {"label": "Autor"}, "layout": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Collage"}}, "image_height": {"label": "Bildhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}}}, "main-cart-footer": {"name": "Zwischensumme", "blocks": {"subtotal": {"name": "Zwischensumme"}, "buttons": {"name": "Checkout-Schaltfläche"}}}, "main-cart-items": {"name": "Artikel"}, "main-collection-banner": {"name": "Kollektionsbanner", "settings": {"paragraph": {"content": "Kollektionsdetails werden [in deinem Admin-Panel verwaltet](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Beschreibung"}, "show_collection_image": {"label": "Bild"}}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"products_per_page": {"label": "Produkte pro Seite"}, "enable_filtering": {"label": "Filter", "info": "Individualisiere Filter mit der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Sortierung"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "Quadrat"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "header__1": {"content": "Filtern und Sortieren"}, "header__3": {"content": "Produktkarte"}, "enable_tags": {"label": "Filter", "info": "Individualisiere Filter mit der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "show_rating": {"label": "Produktbewertung", "info": "<PERSON>ür Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"}, "columns_desktop": {"label": "Spalten"}, "columns_mobile": {"label": "Spalten Mobilgeräte", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "filter_type": {"label": "Layout filtern", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "quick_add": {"label": "Schnelles Hinzufügen", "options": {"option_1": "<PERSON><PERSON>", "option_2": "Standard", "option_3": "Sammelaktion"}}}}, "main-list-collections": {"name": "Listenseite für Kollektionen", "settings": {"title": {"label": "Überschrift", "default": "Kollektionen"}, "sort": {"label": "Kollektionen sortieren", "options__1": {"label": "Alphabetisch, A-Z"}, "options__2": {"label": "Alphabetisch, Z-A"}, "options__3": {"label": "<PERSON><PERSON>, neu zu alt"}, "options__4": {"label": "<PERSON><PERSON>, alt zu neu"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoch zu niedrig"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ni<PERSON><PERSON> zu hoch"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "Quadrat"}}, "columns_desktop": {"label": "Spalten"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Spalten Mobilgeräte", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "main-page": {"name": "Seite"}, "main-password-footer": {"name": "Passwort-Fußzeile"}, "main-password-header": {"name": "Passwort-Header", "settings": {"logo_help": {"content": "Bearbeite dein Logo in den [Theme-Einstellungen](/editor?context=theme&category=logo)"}}}, "main-product": {"blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text", "default": "Textblock"}, "text_style": {"label": "Optik", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Optik", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>ln"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Mehr Informationen zu [<PERSON><PERSON><PERSON><PERSON> ](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) für Produktoptionen", "options__1": {"label": "Kreis"}, "options__2": {"label": "Quadrat"}, "options__3": {"label": "<PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamische Checkout-Buttons", "info": "Kunden wird die bevorzugte Zahlungsmethode angezeigt. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": " Optionen für Gutscheinversand", "info": "Kunden können eine persönliche Nachricht hinzufügen und den Versand planen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Verfügbarkeit von Abholungen"}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"text": {"label": "Text", "default": "Teilen"}}}, "collapsible_tab": {"name": "Einklappbare Reihe", "settings": {"heading": {"label": "Überschrift", "default": "Einklappbare Reihe"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "Reiheninhalt der Seite"}, "icon": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Flasche"}, "options__5": {"label": "Box"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__7": {"label": "Chat-Blase"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "Laktosef<PERSON>i"}, "options__12": {"label": "<PERSON><PERSON><PERSON>"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Glutenfrei"}, "options__16": {"label": "<PERSON><PERSON>"}, "options__17": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Blitz"}, "options__21": {"label": "Lippenstift"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "Pinnnadel"}, "options__24": {"label": "<PERSON><PERSON>"}, "label": "Symbol", "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Pfotenabdruck"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Flugzeug"}, "options__30": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "Fragezeichen"}, "options__33": {"label": "Recyclen"}, "options__34": {"label": "Rückgabe"}, "options__35": {"label": "Lineal"}, "options__36": {"label": "Servierteller"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__41": {"label": "Stern"}, "options__42": {"label": "Stoppuhr"}, "options__43": {"label": "Lieferwagen"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Link-Label", "default": "Pop-up-Linktext"}, "page": {"label": "Seite"}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "<PERSON>ür Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"}}}, "complementary_products": {"name": "Ergänzende Produkte", "settings": {"paragraph": {"content": "Verwalte ergänzende Produkte in der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Überschrift", "default": "<PERSON>t gut zu"}, "make_collapsible_row": {"label": "Einklappbare Reihe"}, "icon": {"info": "<PERSON><PERSON><PERSON><PERSON>, wenn einklappbare Reihe ausgewählt ist"}, "product_list_limit": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "products_per_page": {"label": "Produkte pro Seite"}, "pagination_style": {"label": "Seitennummerierung", "options": {"option_1": "Punkte", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "<PERSON><PERSON><PERSON>"}}, "product_card": {"heading": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options": {"option_1": "Hochformat", "option_2": "Quadrat"}}, "enable_quick_add": {"label": "Schnelles Hinzufügen"}}}, "icon_with_text": {"name": "Symbol mit Text", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}}, "heading": {"info": "<PERSON>um Ausblenden dieser <PERSON><PERSON><PERSON> leer lassen"}, "icon_1": {"label": "Symbol"}, "image_1": {"label": "Bild"}, "heading_1": {"label": "Überschrift", "default": "Titel"}, "icon_2": {"label": "Symbol"}, "image_2": {"label": "Bild"}, "heading_2": {"label": "Überschrift", "default": "Titel"}, "icon_3": {"label": "Symbol"}, "image_3": {"label": "Bild"}, "heading_3": {"label": "Überschrift", "default": "Titel"}, "pairing_1": {"label": "Kombination 1", "info": "<PERSON><PERSON><PERSON>e für jede Kombination ein Symbol oder ein Bild aus"}, "pairing_2": {"label": "Kombination 2"}, "pairing_3": {"label": "Kombination 3"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "inventory": {"name": "Inventarstatus", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "inventory_threshold": {"label": "Geringer Inventarschwellenwert"}, "show_inventory_quantity": {"label": "Inventarbestand"}}}}, "settings": {"header": {"content": "Medien"}, "enable_video_looping": {"label": "Video in Dauerschleife"}, "enable_sticky_info": {"label": "Fixierter Inhalt"}, "hide_variants": {"label": "Andere Variantmedien ausblenden, nachdem eine ausgewählt wurde."}, "gallery_layout": {"label": "Layout", "options__1": {"label": "Gestapelt"}, "options__2": {"label": "2 Spalten"}, "options__3": {"label": "Vorschaubilder"}, "options__4": {"label": "Karussell mit Vorschaubildern"}}, "media_size": {"label": "Breite", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "mobile_thumbnails": {"label": "Mobiles Layout", "options__1": {"label": "2 Spalten"}, "options__2": {"label": "Vorschaubilder anzeigen"}, "options__3": {"label": "Vorschaubilder ausblenden"}}, "media_position": {"label": "Position", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Zoom", "options__1": {"label": "Lightbox öffnen"}, "options__2": {"label": "<PERSON><PERSON><PERSON> und mit der Maus darüber fahren"}, "options__3": {"label": "Nicht zoomen"}}, "constrain_to_viewport": {"label": "Auf Bildschirmhöhe beschränken"}, "media_fit": {"label": "Passform", "options__1": {"label": "Original"}, "options__2": {"label": "Füllung"}}}, "name": "Produktinformationen"}, "main-search": {"name": "Suchergebnisse", "settings": {"image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "Quadrat"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "header__1": {"content": "Produktkarte"}, "header__2": {"content": "Blog-Karte"}, "article_show_date": {"label": "Datum"}, "article_show_author": {"label": "Autor"}, "show_rating": {"label": "Produktbewertung", "info": "<PERSON>ür Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"}, "columns_desktop": {"label": "Spalten"}, "columns_mobile": {"label": "Spalten Mobilgeräte", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multicolumn": {"name": "<PERSON>t mehreren <PERSON>", "settings": {"title": {"label": "Überschrift", "default": "<PERSON>t mehreren <PERSON>"}, "image_width": {"label": "Breite", "options__1": {"label": "Drittelbreite der Spalte"}, "options__2": {"label": "Halbe Breite der Spalte"}, "options__3": {"label": "Ganze Breite der Spalte"}}, "image_ratio": {"label": "Verhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "Quadrat"}, "options__4": {"label": "Kreis"}}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "background_style": {"label": "Sekundärer Hintergrund", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Als Spaltenhintergrund anzeigen"}}, "button_label": {"label": "Etikett", "default": "Schaltflächenbeschriftung", "info": "<PERSON><PERSON> Ausblenden leer lassen"}, "button_link": {"label": "Link"}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "columns_desktop": {"label": "Spalten"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Spalten", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_text": {"content": "Überschrift"}, "header_image": {"content": "Bild"}, "header_layout": {"content": "Layout"}, "header_button": {"content": "Schaltfläche"}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "title": {"label": "Überschrift", "default": "<PERSON>lt<PERSON>"}, "text": {"label": "Beschreibung", "default": "<p>Kombiniere Text mit einem Bild, um den Fokus auf dein Produkt, deine Kollektion oder deinen Blog-Beitrag zu richten. Du kannst außerdem weitere Details über die Verfügbarkeit oder den Stil und sogar eine Bewertung hinzufügen.</p>"}, "link_label": {"label": "Link-Label", "info": "<PERSON><PERSON> Ausblenden leer lassen"}, "link": {"label": "Link"}}}}, "presets": {"name": "<PERSON>t mehreren <PERSON>"}}, "newsletter": {"name": "E-Mail-Anmeldung", "settings": {"full_width": {"label": "Volle Breite"}, "paragraph": {"content": "Registrierungen hinzufügen [Kundenprofile](https://help.shopify.com/manual/customers/manage-customers)"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift", "default": "Abonniere unsere E-Mails"}}}, "paragraph": {"name": "Text", "settings": {"paragraph": {"label": "Text", "default": "<p><PERSON><PERSON><PERSON><PERSON> als Erster von neuen Kollektionen und exklusiven Angeboten.</p>"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "E-Mail-Anmeldung"}}, "page": {"name": "Seite", "settings": {"page": {"label": "Seite"}}, "presets": {"name": "Seite"}}, "rich-text": {"name": "Rich Text", "settings": {"full_width": {"label": "Volle Breite"}, "desktop_content_position": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Inhaltsposition"}, "content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Inhaltsausrichtung"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON> etwas über deine Marke"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text", "default": "<p><PERSON><PERSON> Infos über deine Marke mit deinen Kunden. Beschreibe ein Produkt, kündige etwas an oder heiße Kunden willkommen.</p>"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Etikett", "info": "<PERSON><PERSON> Ausblenden leer lassen", "default": "Schaltflächenbeschriftung"}, "button_link_1": {"label": "Link"}, "button_style_secondary_1": {"label": "Umriss-Stil"}, "button_label_2": {"label": "Etikett", "info": "<PERSON><PERSON>, um Etikett auszublenden"}, "button_link_2": {"label": "Link"}, "button_style_secondary_2": {"label": "Umriss-Stil"}, "header_button1": {"content": "Schaltfläche 1"}, "header_button2": {"content": "Schaltfläche 2"}}}, "caption": {"name": "Bildtext", "settings": {"text": {"label": "Text", "default": "Tagline hinzufügen"}, "text_style": {"label": "Optik", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Größe", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Rich Text"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> so gestalten wie das Theme"}}, "presets": {"name": "Apps"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Titel", "default": "Video"}, "cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "info": "YouTube- oder Vimeo-URL verwenden"}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video für Nutzer von Bildschirmlesegeräten"}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON>e Bild-Padding aus, wenn du nicht möchtest, dass dein Titelbild abgeschnitten wird."}, "full_width": {"label": "Volle Breite"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "Video in Dauerschleife"}, "header__1": {"content": "Von Shopify gehostetes Video"}, "header__2": {"content": "<PERSON>der Video von URL einbetten"}, "header__3": {"content": "Layout"}, "paragraph": {"content": "Wird an<PERSON><PERSON>, wenn kein von Shopify gehostetes Video ausgewählt wurde"}}, "presets": {"name": "Video"}}, "featured-product": {"name": "Vorgestelltes Produkt", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text", "default": "Textblock"}, "text_style": {"label": "Optik", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Optik", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>ln"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Mehr Informationen zu [<PERSON><PERSON><PERSON><PERSON> ](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) für Produktoptionen", "options__1": {"label": "Kreis"}, "options__2": {"label": "Quadrat"}, "options__3": {"label": "<PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamische Checkout-Buttons", "info": "Kunden wird die bevorzugte Zahlungsmethode angezeigt. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Text", "default": "Teilen"}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "<PERSON>ür Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}}, "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundärer Hintergrund"}, "header": {"content": "Medien"}, "enable_video_looping": {"label": "Video in Dauerschleife"}, "hide_variants": {"label": "Medien von nicht ausgewählten Varianten auf dem Desktop ausblenden"}, "media_position": {"label": "Position", "info": "Positionen werden automatisch für die mobile Nutzung optimiert.", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Vorgestelltes Produkt"}}, "email-signup-banner": {"name": "Banner für E-Mail-Anmeldung", "settings": {"paragraph": {"content": "Registrierungen hinzufügen [Kundenprofile](https://help.shopify.com/manual/customers/manage-customers)"}, "image": {"label": "Hintergrundbild"}, "show_background_image": {"label": "Hintergrundbild anzeigen"}, "show_text_box": {"label": "Container"}, "image_overlay_opacity": {"label": "Überlagerungsdeckkraft"}, "show_text_below": {"label": "Text unter Bild stapeln"}, "image_height": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_content_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "<PERSON>ten zent<PERSON>t"}, "options__9": {"label": "Unten rechts"}, "label": "Position"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung"}, "header": {"content": "Mobiles Layout"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn Container angezeigt wird."}, "content_header": {"content": "Inhalt"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Titel", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "Text", "settings": {"paragraph": {"label": "Text", "default": "<p><PERSON><PERSON><PERSON><PERSON> als Erster von unserem Launch.</p>"}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "label": "Optik"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "Banner für E-Mail-Anmeldung"}}, "slideshow": {"name": "Slideshow", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Volle Breite"}, "options__2": {"label": "Seite"}}, "slide_height": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "An erstes Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "slider_visual": {"label": "Seitennummerierung", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Punkte"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Autorotieren der Slides"}, "change_slides_speed": {"label": "Anzeige der nächsten Folie alle"}, "show_text_below": {"label": "Text unter Bild stapeln"}, "mobile": {"content": "Mobiles Layout"}, "accessibility": {"content": "Barrierefreiheit", "label": "Slideshow-Beschreibung", "info": "Beschreibe die Slideshow für Nutzer von Bildschirmlesegeräten", "default": "Slideshow zu deiner Marke"}}, "blocks": {"slide": {"name": "Folie", "settings": {"image": {"label": "Bild"}, "heading": {"label": "Titel", "default": "Slideshow"}, "subheading": {"label": "Unter-Überschrift", "default": "Erzähle deine Geschichte mit Fotos"}, "button_label": {"label": "Etikett", "info": "<PERSON><PERSON> Ausblenden leer lassen", "default": "Schaltflächenbeschriftung"}, "link": {"label": "Link"}, "secondary_style": {"label": "Umriss-Stil"}, "box_align": {"label": "Inhaltsposition", "options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON> mittig"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "Unten mittig"}, "options__9": {"label": "Unten rechts"}}, "show_text_box": {"label": "Container"}, "text_alignment": {"label": "Inhaltsausrichtung", "option_1": {"label": "Links"}, "option_2": {"label": "<PERSON><PERSON><PERSON>"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Überlagerungsdeckkraft"}, "text_alignment_mobile": {"label": "Mobile Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "header_button": {"content": "Schaltfläche"}, "header_layout": {"content": "Layout"}, "header_text": {"content": "Text"}, "header_colors": {"content": "<PERSON><PERSON>"}}}}, "presets": {"name": "Slideshow"}}, "collapsible_content": {"name": "Einklappbarer Inhalt", "settings": {"caption": {"label": "Bildtext"}, "heading": {"label": "Überschrift", "default": "Einklappbarer Inhalt"}, "heading_alignment": {"label": "Ausrichtung der Überschrift", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Container", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Reihencontainer"}, "options__3": {"label": "Abschnittscontainer"}}, "open_first_collapsible_row": {"label": "<PERSON><PERSON><PERSON>"}, "header": {"content": "Bild"}, "image": {"label": "Bild"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_layout": {"label": "Platzierung", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Bild an zweiter Stelle"}}, "container_color_scheme": {"label": "Farbschema für Container"}, "layout_header": {"content": "Layout"}, "section_color_scheme": {"label": "Farbschema des Abschnitts"}}, "blocks": {"collapsible_row": {"name": "Einklappbare Reihe", "settings": {"heading": {"label": "Überschrift", "default": "Einklappbare Reihe"}, "row_content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "Reiheninhalt der Seite"}, "icon": {"label": "Symbol", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Flasche"}, "options__5": {"label": "Box"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__7": {"label": "Chat-Blase"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "Laktosef<PERSON>i"}, "options__12": {"label": "<PERSON><PERSON><PERSON>"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Glutenfrei"}, "options__16": {"label": "<PERSON><PERSON>"}, "options__17": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Blitz"}, "options__21": {"label": "Lippenstift"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "Pinnnadel"}, "options__24": {"label": "<PERSON><PERSON>"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Pfotenabdruck"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Flugzeug"}, "options__30": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "Fragezeichen"}, "options__33": {"label": "Recyclen"}, "options__34": {"label": "Rückgabe"}, "options__35": {"label": "Lineal"}, "options__36": {"label": "Servierteller"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__41": {"label": "Stern"}, "options__42": {"label": "Stoppuhr"}, "options__43": {"label": "Lieferwagen"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Einklappbarer Inhalt"}}, "main-account": {"name": "Ko<PERSON>"}, "main-activate-account": {"name": "Kontoaktivierung"}, "main-addresses": {"name": "<PERSON><PERSON><PERSON>"}, "main-login": {"name": "<PERSON><PERSON>", "shop_login_button": {"enable": "\"Mit Shop anmelden\" aktivieren"}}, "main-order": {"name": "Bestellung"}, "main-register": {"name": "Registrierung"}, "main-reset-password": {"name": "Passwort zurücksetzen"}, "related-products": {"name": "Ähnliche Produkte", "settings": {"heading": {"label": "Überschrift"}, "products_to_show": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "columns_desktop": {"label": "Spalten"}, "paragraph__1": {"content": "Ergänzende Produkte können in der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations) verwaltet werden", "default": "Das könnte dir auch gefallen"}, "header__2": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "Quadrat"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "show_rating": {"label": "Produktbewertung", "info": "<PERSON>ür Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"}, "columns_mobile": {"label": "Spalten Mobilgeräte", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Bild"}, "image_height": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON>"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Breite"}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "label": "Textstil"}, "button_style": {"options__1": {"label": "Durchgehende Schaltfläche"}, "options__2": {"label": "Umriss-Schaltfläche"}, "label": "Schaltflächenstil"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Position"}, "image_layout": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>s"}, "options__3": {"label": "Linksbündig"}, "options__4": {"label": "Rechtsbündig"}, "label": "Platzierung"}, "container_color_scheme": {"label": "Farbschema für Container"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ausrichtung Mobilgerät"}, "header": {"content": "Bild"}, "header_2": {"content": "Inhalt"}, "header_3": {"content": "<PERSON><PERSON>"}}, "blocks": {"row": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Bildtext", "default": "Bildtext"}, "heading": {"label": "Überschrift", "default": "<PERSON><PERSON><PERSON>"}, "text": {"label": "Text", "default": "<p>Kombiniere Text mit einem Bild, um den Fokus auf dein Produkt, deine Kollektion oder deinen Blog-Beitrag zu richten. Du kannst außerdem weitere Details über die Verfügbarkeit oder den Stil und sogar eine Bewertung hinzufügen.</p>"}, "button_label": {"label": "Schaltflächenbeschriftung", "default": "Schaltflächenbeschriftung", "info": "<PERSON><PERSON> Ausblenden leer lassen"}, "button_link": {"label": "Schaltflächenlink"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "quick-order-list": {"name": "<PERSON><PERSON><PERSON>", "settings": {"show_image": {"label": "Bilder"}, "show_sku": {"label": "SKUs"}, "variants_per_page": {"label": "<PERSON><PERSON><PERSON> pro Seite"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}}}