{"general": {"password_page": {"login_form_heading": "Enter store using password:", "login_password_button": "Enter using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_error": "Wrong password!", "login_form_submit": "Enter", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"link underlined-link\">Log in here</a>", "powered_by_shopify_html": "This shop will be powered by {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Share on Facebook", "share_on_twitter": "Share on X", "share_on_pinterest": "Pin on Pinterest"}, "links": {"twitter": "X (Twitter)", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Continue shopping", "pagination": {"label": "Pagination", "page": "Page {{ number }}", "next": "Next page", "previous": "Previous page"}, "search": {"search": "Search", "reset": "Clear search term"}, "cart": {"view": "View cart ({{ count }})", "view_empty_cart": "View cart", "item_added": "Item added to your cart"}, "share": {"close": "Close share", "copy_to_clipboard": "Copy link", "share_url": "Link", "success_message": "Link copied to clipboard"}, "slider": {"of": "of", "next_slide": "Slide right", "previous_slide": "Slide left", "name": "Slide<PERSON>"}}, "newsletter": {"label": "Email", "success": "Thanks for subscribing", "button_label": "Subscribe"}, "accessibility": {"skip_to_text": "Skip to content", "skip_to_product_info": "Skip to product information", "close": "Close", "unit_price_separator": "per", "vendor": "Vendor:", "error": "Error", "refresh_page": "Choosing a selection results in a full page refresh.", "link_messages": {"new_window": "Opens in a new window.", "external": "Opens external website."}, "loading": "Loading...", "total_reviews": "total reviews", "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars", "collapsible_content_title": "Collapsible content", "complementary_products": "Complementary products"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "Read more: {{ title }}", "comments": {"one": "{{ count }} comment", "other": "{{ count }} comments"}, "moderated": "Please note, comments need to be approved before they are published.", "comment_form_title": "Leave a comment", "name": "Name", "email": "Email", "message": "Comment", "post": "Post comment", "back_to_blog": "Back to blog", "share": "Share this article", "success": "Your comment was posted successfully! Thank you!", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated."}}, "onboarding": {"product_title": "Example product title", "collection_title": "Your collection's name"}, "products": {"product": {"add_to_cart": "Add to cart", "choose_options": "Choose options", "choose_product_options": "Choose options for {{ product_name }}", "description": "Description", "inventory_in_stock": "In stock", "inventory_in_stock_show_count": "{{ quantity }} in stock", "inventory_low_stock": "Low stock", "inventory_low_stock_show_count": "Low stock: {{ quantity }} left", "inventory_out_of_stock": "Out of stock", "inventory_out_of_stock_continue_selling": "In stock", "sku": "SKU", "on_sale": "Sale", "product_variants": "Product variants", "media": {"gallery_viewer": "Gallery Viewer", "load_image": "Load image {{ index }} in gallery view", "load_model": "Load 3D Model {{ index }} in gallery view", "load_video": "Play video {{ index }} in gallery view", "image_available": "Image {{ index }} is now available in gallery view", "open_media": "Open media {{ index }} in modal", "play_model": "Play 3D Viewer", "play_video": "Play video"}, "quantity": {"label": "Quantity", "input_label": "Quantity for {{ product }}", "increase": "Increase quantity for {{ product }}", "decrease": "Decrease quantity for {{ product }}", "minimum_of": "Minimum of {{ quantity }}", "maximum_of": "Maximum of {{ quantity }}", "multiples_of": "Increments of {{ quantity }}", "min_of": "Min {{ quantity }}", "max_of": "Max {{ quantity }}", "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> in cart", "note": "View quantity rules"}, "volume_pricing": {"title": "Volume Pricing", "note": "Volume pricing available", "minimum": "{{ quantity }}+", "price_at_each_html": "at {{ price }}/ea", "price_range": "{{ minimum }} - {{ maximum }}"}, "pickup_availability": {"view_store_info": "View store information", "check_other_stores": "Check availability at other stores", "pick_up_available": "Pickup available", "pick_up_available_at_html": "Pickup available at <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Pickup currently unavailable at <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "Couldn't load pickup availability", "refresh": "Refresh"}, "price": {"from_price_html": "From {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}, "share": "Share this product", "sold_out": "Sold out", "unavailable": "Unavailable", "vendor": "<PERSON><PERSON><PERSON>", "value_unavailable": "{{ option_value }} - Unavailable", "variant_sold_out_or_unavailable": "Variant sold out or unavailable", "video_exit_message": "{{ title }} opens full screen video in same window.", "view_full_details": "View full details", "xr_button": "View in your space", "xr_button_label": "View in your space, loads item in augmented reality window", "taxes_included": "Taxes included.", "duties_included": "Duties included.", "duties_and_taxes_included": "Duties and taxes included.", "shipping_policy_html": "<a href=\"{{ link }}\">Shipping</a> calculated at checkout."}, "booking": {"from": "from", "date": "Date", "adults": "Adults", "children": "Children", "infants": "Infants", "age_18_plus": "Age 18+", "age_6_17": "Age 6-17", "age_0_5": "Age 0-5", "guest_names": "Guest names", "mr": "Mr", "ms": "Ms", "mrs": "Mrs", "guest_name_placeholder": "Guest name", "package": "Package", "standard_package": "Standard Package", "premium_package": "Premium Package", "luxury_package": "Luxury Package", "book_now": "Book Now", "organized_by": "Organized by", "booking_submitted": "Booking request submitted! We will contact you soon.", "please_complete_contact": "Please complete your contact information below to finalize your booking.", "complete_booking": "Complete Your Booking", "contact_description": "Please provide your contact information to complete the booking request.", "additional_requests": "Additional requests or questions", "send_booking_request": "Send Booking Request", "booking_details": "BOOKING DETAILS", "product": "Product", "total_guests": "Total Guests", "booking_time": "Booking Time", "contact_form_not_found": "Contact form not found. Please navigate to the contact page to complete your booking."}, "tour": {"highlights_title": "Tour Highlights", "highlight_1_title": "Professional Guide", "highlight_1_desc": "Expert local guide with extensive knowledge", "highlight_2_title": "All Inclusive", "highlight_2_desc": "Transportation, meals, and entrance fees included", "highlight_3_title": "Small Groups", "highlight_3_desc": "Maximum 12 people for personalized experience", "highlight_4_title": "Flexible Timing", "highlight_4_desc": "Multiple departure times available daily", "safety_title": "Safety & Comfort", "safety_1": "Insured", "safety_2": "Licensed", "safety_3": "5-Star Rated"}, "modal": {"label": "Media gallery"}, "facets": {"filter_and_operator_subtitle": "Match all", "apply": "Apply", "clear": "Clear", "clear_all": "Remove all", "from": "From", "filter_and_sort": "Filter and sort", "filter_by_label": "Filter:", "filter_button": "Filter", "filters_selected": {"one": "{{ count }} selected", "other": "{{ count }} selected"}, "filter_selected_accessibility": "{{ type }} ({{ count }} filters selected)", "show_more": "Show more", "show_less": "Show less", "max_price": "The highest price is {{ price }}", "product_count": {"one": "{{ product_count }} of {{ count }} product", "other": "{{ product_count }} of {{ count }} products"}, "product_count_simple": {"one": "{{ count }} product", "other": "{{ count }} products"}, "reset": "Reset", "sort_button": "Sort", "sort_by_label": "Sort by:", "to": "To", "clear_filter": "Remove filter"}}, "templates": {"search": {"no_results": "No results found for “{{ terms }}”. Check the spelling or use a different word or phrase.", "page": "Page", "products": "Products", "results_pages_with_count": {"one": "{{ count }} page", "other": "{{ count }} pages"}, "results_suggestions_with_count": {"one": "{{ count }} suggestion", "other": "{{ count }} suggestions"}, "results_products_with_count": {"one": "{{ count }} product", "other": "{{ count }} products"}, "results_with_count": {"one": "{{ count }} result", "other": "{{ count }} results"}, "results_with_count_and_term": {"one": "{{ count }} result found for “{{ terms }}”", "other": "{{ count }} results found for “{{ terms }}”"}, "title": "Search results", "search_for": "Search for “{{ terms }}”", "suggestions": "Suggestions", "pages": "Pages"}, "cart": {"cart": "<PERSON><PERSON>"}, "contact": {"form": {"title": "Contact form", "name": "Name", "email": "Email", "phone": "Phone number", "comment": "Comment", "send": "Send", "post_success": "Thanks for contacting us. We'll get back to you as soon as possible.", "error_heading": "Please adjust the following:"}}, "404": {"title": "Page not found", "subtext": "404"}}, "sections": {"announcements": {"previous_announcement": "Previous announcement", "next_announcement": "Next announcement", "carousel": "Carousel", "announcement": "Announcement", "announcement_bar": "Announcement bar"}, "header": {"announcement": "Announcement", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} item", "other": "{{ count }} items"}}, "cart": {"title": "Your cart", "caption": "Cart items", "remove_title": "Remove {{ title }}", "estimated_total": "Estimated total", "new_estimated_total": "New estimated total", "note": "Order special instructions", "checkout": "Check out", "empty": "Your cart is empty", "cart_error": "There was an error while updating your cart. Please try again.", "cart_quantity_error_html": "You can only add {{ quantity }} of this item to your cart.", "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Duties and taxes included. Discounts and <a href=\"{{ link }}\">shipping</a> calculated at checkout.", "duties_and_taxes_included_shipping_at_checkout_without_policy": "Duties and taxes included. Discounts and shipping calculated at checkout.", "taxes_included_shipping_at_checkout_with_policy_html": "Taxes included. Discounts and <a href=\"{{ link }}\">shipping</a> calculated at checkout.", "taxes_included_shipping_at_checkout_without_policy": "Taxes included. Discounts and shipping calculated at checkout.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Duties included. Taxes, discounts and <a href=\"{{ link }}\">shipping</a> calculated at checkout.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Duties included. Taxes, discounts and shipping calculated at checkout.", "taxes_at_checkout_shipping_at_checkout_with_policy_html": "Taxes, discounts and <a href=\"{{ link }}\">shipping</a> calculated at checkout.", "taxes_at_checkout_shipping_at_checkout_without_policy": "Taxes, discounts and shipping calculated at checkout.", "headings": {"product": "Product", "price": "Price", "total": "Total", "quantity": "Quantity", "image": "Product image"}, "update": "Update", "login": {"title": "Have an account?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Log in</a> to check out faster."}}, "footer": {"payment": "Payment methods"}, "featured_blog": {"view_all": "View all", "onboarding_title": "Blog post", "onboarding_content": "Give your customers a summary of your blog post"}, "featured_collection": {"view_all": "View all", "view_all_label": "View all products in the {{ collection_name }} collection"}, "collection_list": {"view_all": "View all"}, "collection_template": {"empty": "No products found", "title": "Collection", "use_fewer_filters_html": "Use fewer filters or <a class=\"{{ class }}\" href=\"{{ link }}\">remove all</a>"}, "video": {"load_video": "Load video: {{ description }}"}, "slideshow": {"load_slide": "Load slide", "previous_slideshow": "Previous slide", "next_slideshow": "Next slide", "pause_slideshow": "Pause slideshow", "play_slideshow": "Play slideshow", "carousel": "Carousel", "slide": "Slide"}, "page": {"title": "Page title"}, "quick_order_list": {"product_total": "Product subtotal", "view_cart": "View cart", "each": "{{ money }}/ea", "product": "Product", "variant": "<PERSON><PERSON><PERSON>", "variant_total": "Variant total", "items_added": {"one": "{{ quantity }} item added", "other": "{{ quantity }} items added"}, "items_removed": {"one": "{{ quantity }} item removed", "other": "{{ quantity }} items removed"}, "product_variants": "Product variants", "total_items": "Total items", "remove_all_single_item_confirmation": "Remove 1 item from your cart?", "remove_all_items_confirmation": "Remove all {{ quantity }} items from your cart?", "remove_all": "Remove all", "cancel": "Cancel", "min_error": "This item has a minimum of {{ min }}", "max_error": "This item has a maximum of {{ max }}", "step_error": "You can only add this item in increments of {{ step }}"}}, "localization": {"country_label": "Country/region", "language_label": "Language", "update_language": "Update language", "update_country": "Update country/region", "search": "Search", "popular_countries_regions": "Popular countries/regions", "country_results_count": "{{ count }} countries/regions found"}, "customer": {"account": {"title": "Account", "details": "Account details", "view_addresses": "View addresses", "return": "Return to Account details"}, "account_fallback": "Account", "activate_account": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate account", "cancel": "Decline invitation"}, "addresses": {"title": "Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add a new address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address 1", "address2": "Address 2", "city": "City", "country": "Country/region", "province": "Province", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "add": "Add address", "update": "Update address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?"}, "log_in": "Log in", "log_out": "Log out", "login_page": {"cancel": "Cancel", "create_account": "Create account", "email": "Email", "forgot_password": "Forgot your password?", "guest_continue": "Continue", "guest_title": "Continue as a guest", "password": "Password", "title": "<PERSON><PERSON>", "sign_in": "Sign in", "submit": "Submit", "alternate_provider_separator": "or"}, "order": {"title": "Order {{ name }}", "date_html": "Placed on {{ date }}", "cancelled_html": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "billing_address": "Billing Address", "payment_status": "Payment Status", "shipping_address": "Shipping Address", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "total_refunded": "Refunded", "fulfilled_at_html": "Fulfilled {{ date }}", "track_shipment": "Track shipment", "tracking_url": "Tracking link", "tracking_company": "Carrier", "tracking_number": "Tracking number", "subtotal": "Subtotal", "total_duties": "Duties"}, "orders": {"title": "Order history", "order_number": "Order", "order_number_link": "Order number {{ number }}", "date": "Date", "payment_status": "Payment status", "fulfillment_status": "Fulfillment status", "total": "Total", "none": "You haven't placed any orders yet."}, "recover_password": {"title": "Reset your password", "subtext": "We will send you an email to reset your password", "success": "We've sent you an email with a link to update your password."}, "register": {"title": "Create account", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "submit": "Create"}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset password"}}, "gift_cards": {"issued": {"how_to_use_gift_card": "Use the gift card code online or QR code in-store", "title": "Here's your {{ value }} gift card balance for {{ shop }}!", "subtext": "Your gift card", "gift_card_code": "Gift card code", "shop_link": "Visit online store", "add_to_apple_wallet": "Add to Apple Wallet", "qr_image_alt": "QR code — scan to redeem gift card", "copy_code": "Copy gift card code", "expiration_date": "Expires {{ expires_on }}", "copy_code_success": "Code copied successfully", "expired": "Expired"}}, "recipient": {"form": {"checkbox": "I want to send this as a gift", "expanded": "Gift card recipient form expanded", "collapsed": "Gift card recipient form collapsed", "email_label": "Recipient email", "email_label_optional_for_no_js_behavior": "Recipient email (optional)", "email": "Email", "name_label": "Recipient name (optional)", "name": "Name", "message_label": "Message (optional)", "message": "Message", "max_characters": "{{ max_chars }} characters max", "send_on": "YYYY-MM-DD", "send_on_label": "Send on (optional)"}}}