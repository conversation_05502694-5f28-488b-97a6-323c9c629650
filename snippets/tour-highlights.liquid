{% comment %}
  Renders tour highlights section to fill empty space
  
  Accepts:
  - product: {Object} Product object
  
  Usage:
  {% render 'tour-highlights', product: product %}
{% endcomment %}

<div class="tour-highlights">
  <div class="tour-highlights__content">
    <h3 class="tour-highlights__title">{{ 'products.tour.highlights_title' | t | default: 'Tour Highlights' }}</h3>
    
    <div class="highlights-list">
      <div class="highlight-item">
        <div class="highlight-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="highlight-content">
          <h4>{{ 'products.tour.highlight_1_title' | t | default: 'Professional Guide' }}</h4>
          <p>{{ 'products.tour.highlight_1_desc' | t | default: 'Expert local guide with extensive knowledge' }}</p>
        </div>
      </div>

      <div class="highlight-item">
        <div class="highlight-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="highlight-content">
          <h4>{{ 'products.tour.highlight_2_title' | t | default: 'All Inclusive' }}</h4>
          <p>{{ 'products.tour.highlight_2_desc' | t | default: 'Transportation, meals, and entrance fees included' }}</p>
        </div>
      </div>

      <div class="highlight-item">
        <div class="highlight-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 8L21 12L17 16M7 16L3 12L7 8M14 4L10 20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="highlight-content">
          <h4>{{ 'products.tour.highlight_3_title' | t | default: 'Small Groups' }}</h4>
          <p>{{ 'products.tour.highlight_3_desc' | t | default: 'Maximum 12 people for personalized experience' }}</p>
        </div>
      </div>

      <div class="highlight-item">
        <div class="highlight-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
            <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="highlight-content">
          <h4>{{ 'products.tour.highlight_4_title' | t | default: 'Flexible Timing' }}</h4>
          <p>{{ 'products.tour.highlight_4_desc' | t | default: 'Multiple departure times available daily' }}</p>
        </div>
      </div>
    </div>

    <!-- Safety & Comfort Features -->
    <div class="safety-features">
      <h4 class="safety-title">{{ 'products.tour.safety_title' | t | default: 'Safety & Comfort' }}</h4>
      <div class="safety-badges">
        <span class="safety-badge">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22S2 16 2 8C2 5.79086 3.79086 4 6 4C8.20914 4 10 5.79086 10 8H14C14 5.79086 15.7909 4 18 4C20.2091 4 22 5.79086 22 8C22 16 12 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          {{ 'products.tour.safety_1' | t | default: 'Insured' }}
        </span>
        <span class="safety-badge">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          {{ 'products.tour.safety_2' | t | default: 'Licensed' }}
        </span>
        <span class="safety-badge">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L15.09 8.26L22 9L16 14.74L17.18 21.02L12 18.77L6.82 21.02L8 14.74L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
          </svg>
          {{ 'products.tour.safety_3' | t | default: '5-Star Rated' }}
        </span>
      </div>
    </div>
  </div>
</div>

<style>
.tour-highlights {
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
  width:100%;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.tour-highlights__title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

.highlights-list {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 2rem;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.highlight-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.highlight-icon {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  padding: 0.5rem;
  border-radius: 8px;
  flex-shrink: 0;
}

.highlight-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.highlight-content p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

.safety-features {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.2);
}

.safety-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.safety-badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.safety-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

@media screen and (max-width: 749px) {
  .tour-highlights {
    margin-top: 1.5rem;
    padding: 1.5rem;
  }
  
  .highlights-list {
    gap: 1rem;
  }
  
  .highlight-item {
    padding: 0.875rem;
  }
  
  .safety-badges {
    gap: 0.75rem;
  }
  
  .safety-badge {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
}
</style>
