<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Test</title>
</head>
<body>
    <h1>Contact Form Test</h1>
    <p>ეს არის test გვერდი რომ ვნახოთ როგორ მუშაობს contact form hidden fields.</p>
    
    <h2>Hidden Fields რომლებიც გაიგზავნება:</h2>
    <ul>
        <li><strong>source_page:</strong> გვერდის handle (მაგ: "about-us", "dental-tour", "hair-surgery")</li>
        <li><strong>page_url:</strong> სრული URL მისამართი</li>
        <li><strong>page_name:</strong> გვერდის სახელი/title</li>
    </ul>
    
    <h2>მაგალითები სხვადასხვა გვერდებისთვის:</h2>
    <ul>
        <li><strong>About Us გვერდი:</strong> source_page = "about-us", page_name = "About Us"</li>
        <li><strong>Dental Tour გვერდი:</strong> source_page = "dental-tour", page_name = "Dental Tour"</li>
        <li><strong>Hair Surgery გვერდი:</strong> source_page = "hair-surgery", page_name = "Hair Surgery"</li>
        <li><strong>Contact გვერდი:</strong> source_page = "contact", page_name = "Contact"</li>
        <li><strong>Homepage:</strong> source_page = "homepage", page_name = "Foxy Travel - Homepage"</li>
        <li><strong>Product გვერდი:</strong> source_page = product handle, page_name = product title</li>
    </ul>
    
    <p>ახლა როცა ვინმე გაგზავნის contact form-ს, თქვენ მიიღებთ email-ს რომელშიც იქნება:</p>
    <ul>
        <li>მომხმარებლის სახელი</li>
        <li>მომხმარებლის email</li>
        <li>მომხმარებლის ტელეფონი</li>
        <li>მომხმარებლის შეტყობინება</li>
        <li><strong>რომელი გვერდიდან არის გაგზავნილი (source_page)</strong></li>
        <li><strong>გვერდის სრული URL (page_url)</strong></li>
        <li><strong>გვერდის სახელი (page_name)</strong></li>
    </ul>
</body>
</html>
