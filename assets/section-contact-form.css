.contact img {
  max-width: 100%;
}

.contact .form__message {
  align-items: flex-start;
}

.contact .icon-success {
  margin-top: 0.2rem;
}

.contact .field {
  margin-bottom: 1.5rem;
}

@media screen and (min-width: 750px) {
  .contact .field {
    margin-bottom: 2rem;
  }
}

.contact__button {
  margin-top: 3rem;
}

@media screen and (min-width: 750px) {
  .contact__button {
    margin-top: 4rem;
  }
}

@media screen and (min-width: 750px) {
  .contact__fields {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 2rem;
  }
}

/* Hair Surgery and Dental Tour Page Specific Styles */
body.template-page.page-hair-surgery .contact,
body.template-page.page-dental-tour .contact {
  background: transparent !important;
}

body.template-page.page-hair-surgery .contact .gradient,
body.template-page.page-dental-tour .contact .gradient {
  background: transparent !important;
}

body.template-page.page-hair-surgery .contact h2,
body.template-page.page-hair-surgery .contact h3,
body.template-page.page-dental-tour .contact h2,
body.template-page.page-dental-tour .contact h3 {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-hair-surgery .contact p,
body.template-page.page-hair-surgery .contact label,
body.template-page.page-dental-tour .contact p,
body.template-page.page-dental-tour .contact label {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
