document.addEventListener('DOMContentLoaded', function() {
  if (window.innerWidth < 750) {
    var sections = document.querySelectorAll('.image-with-text__grid');
    sections.forEach(function(section) {
      var desktopTextBlock = section.querySelector('.image-with-text__text-item--desktop');
      var button = desktopTextBlock ? desktopTextBlock.querySelector('.button') : null;
      var videoBlock = section.querySelector('.image-with-text__media-item');
      if (button && videoBlock) {
        // თუ უკვე არსებობს კლონირებული ღილაკი, აღარ დაამატო
        if (videoBlock.nextSibling && videoBlock.nextSibling.classList && videoBlock.nextSibling.classList.contains('video-with-text-mobile-btn')) {
          button.style.display = 'none';
          return;
        }
        var buttonClone = button.cloneNode(true);
        buttonClone.classList.add('video-with-text-mobile-btn');
        buttonClone.style.margin = '2rem auto 0 auto';
        buttonClone.style.display = 'block';
        buttonClone.style.maxWidth = '350px';
        buttonClone.style.width = '100%';
        buttonClone.style.borderRadius = '8px';
        buttonClone.style.fontSize = '1.15rem';
        buttonClone.style.fontWeight = '600';
        buttonClone.style.letterSpacing = '0.02em';
        buttonClone.style.boxShadow = '0 2px 12px 0 rgba(0,0,0,0.07)';
        buttonClone.style.textAlign = 'center';
        buttonClone.style.padding = '1.1em 0';
        buttonClone.style.lineHeight = '1.2';
        videoBlock.parentNode.insertBefore(buttonClone, videoBlock.nextSibling);
        button.style.display = 'none';
      }
    });
  }
}); 