document.addEventListener('DOMContentLoaded', function() {
  // Mobile video functionality
  function initializeMobileVideo() {
    if (window.innerWidth < 750) {
      // Handle button repositioning for mobile
      var sections = document.querySelectorAll('.image-with-text__grid');
      sections.forEach(function(section) {
        var desktopTextBlock = section.querySelector('.image-with-text__text-item--desktop');
        var button = desktopTextBlock ? desktopTextBlock.querySelector('.button') : null;
        var videoBlock = section.querySelector('.image-with-text__media-item');

        if (button && videoBlock) {
          // Check if button clone already exists
          if (videoBlock.nextSibling && videoBlock.nextSibling.classList && videoBlock.nextSibling.classList.contains('video-with-text-mobile-btn')) {
            button.style.display = 'none';
            return;
          }

          var buttonClone = button.cloneNode(true);
          buttonClone.classList.add('video-with-text-mobile-btn');
          buttonClone.style.margin = '2rem auto 0 auto';
          buttonClone.style.display = 'block';
          buttonClone.style.maxWidth = '350px';
          buttonClone.style.width = '100%';
          buttonClone.style.borderRadius = '8px';
          buttonClone.style.fontSize = '1.15rem';
          buttonClone.style.fontWeight = '600';
          buttonClone.style.letterSpacing = '0.02em';
          buttonClone.style.boxShadow = '0 2px 12px 0 rgba(0,0,0,0.07)';
          buttonClone.style.textAlign = 'center';
          buttonClone.style.padding = '1.1em 0';
          buttonClone.style.lineHeight = '1.2';
          videoBlock.parentNode.insertBefore(buttonClone, videoBlock.nextSibling);
          button.style.display = 'none';
        }
      });

      // Enhanced mobile video handling
      var videos = document.querySelectorAll('.image-with-text__media video, .ai-video-player, video');
      videos.forEach(function(video) {
        // Ensure video is visible and properly sized on mobile
        video.style.display = 'block';
        video.style.width = '100%';
        video.style.height = 'auto';
        video.style.minHeight = '200px';
        video.style.objectFit = 'cover';

        // Add mobile-specific attributes
        video.setAttribute('playsinline', '');
        video.setAttribute('webkit-playsinline', '');

        // Mobile video tap handling for fullscreen and unmute
        video.addEventListener('click', function(e) {
          e.preventDefault();

          // Unmute the video if it's muted
          if (video.muted) {
            video.muted = false;
          }

          // Try to enter fullscreen
          if (video.requestFullscreen) {
            video.requestFullscreen();
          } else if (video.webkitRequestFullscreen) {
            video.webkitRequestFullscreen();
          } else if (video.mozRequestFullScreen) {
            video.mozRequestFullScreen();
          } else if (video.msRequestFullscreen) {
            video.msRequestFullscreen();
          }

          // Play the video
          video.play().catch(function(error) {
            console.log('Video play failed:', error);
          });
        });

        // Handle video load events
        video.addEventListener('loadedmetadata', function() {
          video.style.display = 'block';
          video.style.visibility = 'visible';
          video.style.opacity = '1';
        });

        // Handle video errors
        video.addEventListener('error', function() {
          console.log('Video failed to load');
          var errorDiv = document.createElement('div');
          errorDiv.style.cssText = 'background:#000;width:100%;height:200px;color:#fff;display:flex;align-items:center;justify-content:center;text-align:center;padding:20px;';
          errorDiv.innerHTML = '<p>Video could not be loaded.<br>Please check your connection.</p>';
          video.parentNode.replaceChild(errorDiv, video);
        });
      });
    }
  }

  // Initialize on load
  initializeMobileVideo();

  // Re-initialize on resize (in case orientation changes)
  var resizeTimer;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(function() {
      initializeMobileVideo();
    }, 250);
  });
});