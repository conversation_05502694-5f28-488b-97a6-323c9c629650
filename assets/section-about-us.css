/* Professional About Us Page - Clean & Modern */

.about-hero {
  background: #ffffff;
  padding: 8rem 0 6rem 0;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

.about-hero h1 {
  font-size: 3.5rem;
  font-weight: 300;
  color: #1a202c;
  margin-bottom: 1.5rem;
  letter-spacing: -0.025em;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  color: #64748b;
  margin: 0;
  text-align: center;
  font-style: normal;
  max-width: 600px;
  margin: 0 auto;
}

.professional-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 6rem 2rem;
}

.about-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-section {
  margin-bottom: 4rem;
}

.section-heading {
  font-size: 2.25rem;
  color: #1a202c;
  margin: 5rem 0 3rem 0;
  font-weight: 600;
  text-align: left;
  position: relative;
  padding-left: 0;
}

.section-heading::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 60px;
  height: 2px;
  background: #3182ce;
}

.service-heading {
  font-size: 1.5rem;
  color: #1a202c;
  margin-bottom: 1rem;
  font-weight: 600;
  display: block;
}

.service-icon {
  display: none;
}

.about-text {
  font-size: 1.125rem;
  line-height: 1.75;
  color: #4a5568;
  margin: 2rem 0;
  font-weight: 400;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.service-card {
  background: #f8fafc;
  padding: 2rem;
  margin: 1.5rem 0;
  border-radius: 8px;
  border-left: 4px solid #3182ce;
  transition: all 0.2s ease;
}

.service-card:hover {
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.service-card p {
  color: #4a5568;
  line-height: 1.6;
  font-size: 1rem;
  margin: 0.75rem 0 0 0;
}

.service-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.service-icon svg {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon svg {
  transform: scale(1.1);
}

.service-card h3 {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.service-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem;
  border-radius: 15px;
  text-align: center;
  margin: 4rem 0;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

/* Statistics Section */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin: 4rem 0;
  padding: 3rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  text-align: center;
  padding: 1.5rem 1rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3182ce;
  margin-bottom: 0.5rem;
  display: block;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}



.cta-section {
  background: #3182ce;
  color: white;
  padding: 3rem;
  border-radius: 8px;
  text-align: center;
  margin: 4rem 0;
}

.cta-section p {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.cta-button {
  display: inline-block;
  background: white;
  color: #3182ce;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.cta-button:hover {
  background: #f7fafc;
  transform: translateY(-1px);
}



/* Responsive Design */
@media screen and (max-width: 768px) {
  .about-hero {
    padding: 4rem 0;
  }

  .about-hero h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .professional-content {
    padding: 3rem 1.5rem;
  }

  .section-heading {
    font-size: 1.875rem;
    margin: 3rem 0 2rem 0;
  }

  .service-card {
    padding: 1.5rem;
    margin: 1rem 0;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 2rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .cta-section {
    padding: 2rem 1.5rem;
  }

  .cta-section p {
    font-size: 1.125rem;
  }
}

@media screen and (max-width: 480px) {
  .about-hero h1 {
    font-size: 2rem;
  }

  .professional-content {
    padding: 2rem 1rem;
  }

  .section-heading {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media screen and (max-width: 480px) {
  .about-content {
    padding: 0 1rem;
  }

  .about-hero {
    padding: 2rem 0;
  }

  .about-hero h1 {
    font-size: 2rem;
  }

  .about-section {
    margin-bottom: 2rem;
  }

  .about-text {
    font-size: 1rem;
    text-align: left;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    padding: 1.5rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .service-icon {
    width: 40px;
    height: 40px;
  }

  .service-icon svg {
    width: 20px;
    height: 20px;
  }
}

/* Animation for scroll reveal */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation delays */
.scroll-reveal:nth-child(1) { transition-delay: 0.1s; }
.scroll-reveal:nth-child(2) { transition-delay: 0.2s; }
.scroll-reveal:nth-child(3) { transition-delay: 0.3s; }
.scroll-reveal:nth-child(4) { transition-delay: 0.4s; }
.scroll-reveal:nth-child(5) { transition-delay: 0.5s; }

/* Hover effects for better interactivity */
.about-section h1:hover::after {
  width: 80px;
  transition: width 0.3s ease;
}

/* Enhanced service card interactions */
.service-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.service-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.service-card:hover::after {
  left: 100%;
}

/* Pulse animation for CTA section */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.cta-section:hover {
  animation: pulse 2s infinite;
}

/* Professional typography improvements */
.about-content h1,
.about-content h2,
.about-content h3 {
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.about-content p {
  font-family: Georgia, 'Times New Roman', serif;
}

/* Enhanced focus states for accessibility */
.service-card:focus-within {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .cta-section,
  .contact-form-section {
    display: none;
  }
  
  .service-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
