/* Product Tabs Styles */
.product-tabs-wrapper {
  margin-top: 3rem;
  padding: 0;
  max-width: 100%;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #f1f5f9;
  padding-top: 2rem;
}

.product-tabs {
  background: #ffffff;
  border-radius: 0;
  box-shadow: none;
  overflow: visible;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.product-tabs__nav {
  display: flex;
  background: transparent;
  border-bottom: 2px solid #e2e8f0;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 0;
  padding: 0;
}

.product-tabs__tab {
  background: none;
  border: none;
  padding: 1.5rem 0;
  margin-right: 3rem;
  font-size: 1.6rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
  border-bottom: 3px solid transparent;
  position: relative;
  font-family: inherit;
}

.product-tabs__tab:hover {
  color: #1e293b;
}

.product-tabs__tab.active {
  color: #1e293b;
  background: transparent;
  border-bottom-color: #3b82f6;
  font-weight: 600;
}

.product-tabs__content {
  padding: 3rem 0 2rem 0;
}

.product-tabs__panel {
  display: none;
}

.product-tabs__panel.active {
  display: block;
}

/* Overview Content Styles */
.overview-content {
  max-width: 100%;
}

.overview-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  margin-top: 3rem;
}

.overview-content h3:first-child {
  margin-top: 0;
}

/* Highlights Section */
.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: transparent;
  border-radius: 6px;
  border: none;
}

.highlight-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  color: #64748b;
  border-radius: 50%;
  flex-shrink: 0;
}

.highlight-content h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* Amenities Section */
.amenities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.amenity-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  font-size: 1.4rem;
  color: #475569;
}

.amenity-item svg {
  color: #64748b;
  flex-shrink: 0;
}

.amenities-show-all {
  background: none;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1.4rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.amenities-show-all:hover {
  background: #3b82f6;
  color: white;
}

/* Description Section */
.description-content {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #475569;
}

.description-text {
  max-height: 4.2rem; /* Show approximately 3 lines */
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.description-content.expanded .description-text {
  max-height: none;
}

.description-show-more {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 1.4rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  margin-top: 1rem;
  padding: 0;
  font-family: inherit;
}

.description-show-more:hover {
  color: #1d4ed8;
}

/* Surroundings Section */
.surroundings-content {
  font-size: 1.4rem;
}

.surroundings-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: #475569;
}

.surroundings-item strong {
  color: #1e293b;
  min-width: 80px;
}

.surroundings-view-map {
  background: none;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1.4rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.surroundings-view-map:hover {
  background: #3b82f6;
  color: white;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .product-tabs-wrapper {
    margin-top: 2rem;
  }

  .product-tabs {
    padding: 0 1rem;
  }

  .product-tabs__content {
    padding: 2rem 0 1rem 0;
  }

  .product-tabs__tab {
    padding: 1rem 0;
    margin-right: 2rem;
    font-size: 1.4rem;
  }
  
  .highlights-grid,
  .amenities-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-content h3 {
    font-size: 1.8rem;
  }

  .overview-rating {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    padding: 1.5rem;
  }

  .rating-score {
    font-size: 3rem;
    min-width: auto;
    width: 80px;
    margin: 0 auto;
  }
}

@media screen and (max-width: 480px) {
  .product-tabs__tab {
    padding: 0.75rem 0;
    margin-right: 1.5rem;
    font-size: 1.3rem;
  }

  .product-tabs__content {
    padding: 1.5rem 0 1rem 0;
  }
  
  .highlight-item,
  .amenity-item {
    padding: 0.5rem;
  }

  .rating-score {
    font-size: 2.4rem;
    width: 60px;
    padding: 0.75rem 0.25rem;
  }

  .rating-text {
    font-size: 1.6rem;
  }

  .rating-subtitle {
    font-size: 1.3rem;
  }
}

/* Rating Section */
.overview-rating {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.rating-score {
  font-size: 3.6rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  min-width: 80px;
  text-align: center;
  background: #3b82f6;
  color: white;
  border-radius: 8px;
  padding: 1rem 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rating-info {
  flex: 1;
}

.rating-text {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.rating-subtitle {
  font-size: 1.4rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

.rating-reviews {
  font-size: 1.4rem;
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  font-family: inherit;
}

.rating-reviews:hover {
  color: #1d4ed8;
}
