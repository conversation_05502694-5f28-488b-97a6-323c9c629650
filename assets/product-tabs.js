class ProductTabs {
  constructor() {
    this.init();
  }

  init() {
    this.tabsContainer = document.querySelector('.product-tabs');
    if (!this.tabsContainer) return;

    this.tabs = this.tabsContainer.querySelectorAll('.product-tabs__tab');
    this.panels = this.tabsContainer.querySelectorAll('.product-tabs__panel');

    this.bindEvents();
    this.setInitialState();
  }

  bindEvents() {
    this.tabs.forEach((tab, index) => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        this.switchTab(index);
      });

      tab.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.switchTab(index);
        }
        
        // Arrow key navigation
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
          e.preventDefault();
          const direction = e.key === 'ArrowLeft' ? -1 : 1;
          const visibleTabs = Array.from(this.tabs).filter(tab => 
            tab.style.display !== 'none'
          );
          const currentIndex = visibleTabs.indexOf(tab);
          const nextIndex = (currentIndex + direction + visibleTabs.length) % visibleTabs.length;
          const nextTab = visibleTabs[nextIndex];
          const nextTabIndex = Array.from(this.tabs).indexOf(nextTab);
          this.switchTab(nextTabIndex);
        }
      });
    });

    // Bind show more/less functionality
    this.bindShowMoreButtons();
  }

  switchTab(activeIndex) {
    // Only switch if the tab is visible
    const activeTab = this.tabs[activeIndex];
    if (!activeTab || activeTab.style.display === 'none') return;

    this.tabs.forEach((tab, index) => {
      const isActive = index === activeIndex;
      tab.classList.toggle('active', isActive);
      tab.setAttribute('aria-selected', isActive);
      
      if (isActive) {
        tab.setAttribute('tabindex', '0');
      } else {
        tab.setAttribute('tabindex', '-1');
      }
    });

    this.panels.forEach((panel, index) => {
      const isActive = index === activeIndex;
      panel.classList.toggle('active', isActive);
      panel.setAttribute('aria-hidden', !isActive);
      
      if (isActive) {
        panel.style.display = 'block';
      } else {
        panel.style.display = 'none';
      }
    });

    // Focus the active tab for accessibility
    activeTab.focus();
  }

  setInitialState() {
    // Set ARIA attributes
    this.tabs.forEach((tab, index) => {
      tab.setAttribute('role', 'tab');
      tab.setAttribute('aria-controls', this.panels[index].id);
      tab.setAttribute('tabindex', index === 0 ? '0' : '-1');
    });

    this.panels.forEach((panel, index) => {
      panel.setAttribute('role', 'tabpanel');
      panel.setAttribute('aria-labelledby', this.tabs[index].id || `tab-${index}`);
    });

    // Ensure only Overview tab is visible and active
    this.tabs.forEach((tab, index) => {
      if (index === 0) {
        tab.classList.add('active');
        tab.setAttribute('aria-selected', 'true');
        tab.style.display = 'block';
      } else {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
        tab.style.display = 'none';
      }
    });

    this.panels.forEach((panel, index) => {
      if (index === 0) {
        panel.classList.add('active');
        panel.setAttribute('aria-hidden', 'false');
        panel.style.display = 'block';
      } else {
        panel.classList.remove('active');
        panel.setAttribute('aria-hidden', 'true');
        panel.style.display = 'none';
      }
    });
  }

  bindShowMoreButtons() {
    // Show more functionality for amenities
    const showAllBtn = document.querySelector('.amenities-show-all');
    if (showAllBtn) {
      showAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        // Add functionality to show all amenities
        console.log('Show all amenities clicked');
      });
    }

    // Show more functionality for description
    const showMoreBtn = document.querySelector('.description-show-more');
    if (showMoreBtn) {
      showMoreBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const content = e.target.closest('.description-content');
        if (content) {
          content.classList.toggle('expanded');
          e.target.textContent = content.classList.contains('expanded') 
            ? 'Show less' 
            : 'Show more';
        }
      });
    }

    // View on map functionality
    const viewMapBtn = document.querySelector('.surroundings-view-map');
    if (viewMapBtn) {
      viewMapBtn.addEventListener('click', (e) => {
        e.preventDefault();
        // Add functionality to show map
        console.log('View on map clicked');
      });
    }
  }

  // Method to show specific tabs (can be called externally)
  showTabs(tabNames) {
    this.tabs.forEach((tab, index) => {
      const tabName = tab.getAttribute('data-tab');
      if (tabNames.includes(tabName)) {
        tab.style.display = 'block';
      } else {
        tab.style.display = 'none';
      }
    });
  }

  // Method to show all tabs
  showAllTabs() {
    this.tabs.forEach(tab => {
      tab.style.display = 'block';
    });
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ProductTabs();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProductTabs;
}
