/* Custom styles for featured collection cards */
.card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: 0.2s ease;
  background: #fff;
  margin: 16px 0;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.card .card__media,
.card .media {
  aspect-ratio: 1 !important;
  width: 100%;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.card .card__content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

.card .card__information {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  padding: 16px;
}

.card .card__heading {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.3;
}

.card .rating {
  font-size: 14px;
  margin: 8px 0;
  color: #666;
}

.card .price {
  font-weight: bold;
  margin-top: auto;
  font-size: 16px;
}

.collection__view-all {
  margin-top: 20px;
}

.grid.product-grid.contains-card {
  display: grid !important;
  gap: 2rem;
  align-items: stretch;
}

.grid.product-grid.contains-card .grid__item {
  display: flex !important;
  height: 100% !important;
  min-height: 450px !important;
  align-self: stretch !important;
}

.grid.product-grid.contains-card .card-wrapper {
  height: 100% !important;
  width: 100% !important;
  display: flex !important;
}

.grid.product-grid.contains-card.grid--4-col-desktop {
  grid-template-columns: repeat(4, 1fr);
}

.grid.product-grid.contains-card.grid--3-col-desktop {
  grid-template-columns: repeat(3, 1fr);
}

.grid.product-grid.contains-card.grid--2-col-desktop {
  grid-template-columns: repeat(2, 1fr);
}

@media (max-width: 749px) {
  .grid.product-grid.contains-card.grid--2-col-tablet-down {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card .card__information {
    padding: 12px;
  }
  
  .card .card__heading {
    font-size: 16px;
  }
}

@media (min-width: 750px) and (max-width: 989px) {
  .grid.product-grid.contains-card.grid--4-col-desktop {
    grid-template-columns: repeat(3, 1fr);
  }
}
