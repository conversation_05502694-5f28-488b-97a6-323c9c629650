/* Tour Information Section Styles */
.tour-info-section {
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.tour-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
}

.tour-section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

/* Tour Highlights */
.tour-highlights {
  margin-bottom: 3rem;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.highlight-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.highlight-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.highlight-content p {
  font-size: 0.9rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* What's Included Section */
.tour-included {
  margin-bottom: 3rem;
}

.included-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.included-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f0f9ff;
  border-radius: 6px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.2s ease;
}

.included-item:hover {
  background: #e0f2fe;
  border-color: rgba(59, 130, 246, 0.2);
}

.included-icon {
  flex-shrink: 0;
  color: #059669;
  background: #d1fae5;
  padding: 4px;
  border-radius: 50%;
}

.included-item span {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

/* Tour Itinerary */
.tour-itinerary {
  margin-bottom: 2rem;
}

.itinerary-timeline {
  margin-top: 2rem;
  position: relative;
}

.itinerary-timeline::before {
  content: '';
  position: absolute;
  left: 24px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
  border-radius: 1px;
}

.itinerary-day {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
}

.day-number {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  position: relative;
  z-index: 1;
}

.day-content {
  flex: 1;
  padding: 1rem 1.5rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.day-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.day-content p {
  font-size: 0.9rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tour-info-section {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .included-list {
    grid-template-columns: 1fr;
  }
  
  .highlight-item {
    padding: 1rem;
  }
  
  .itinerary-timeline::before {
    left: 20px;
  }
  
  .day-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .day-content {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .tour-info-section {
    margin: 0.5rem;
    padding: 1rem;
  }
  
  .tour-section-title {
    font-size: 1.3rem;
  }
  
  .highlight-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .highlight-icon {
    width: 40px;
    height: 40px;
  }
}
