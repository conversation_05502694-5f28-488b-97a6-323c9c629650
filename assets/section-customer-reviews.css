.customer-reviews {
  margin: 0 auto;
}

.customer-reviews .title {
  text-align: center;
  margin-bottom: 4rem;
}

.reviews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media screen and (max-width: 749px) {
  .reviews-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.review-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.review-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.review-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.review-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.review-avatar--placeholder {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.5rem;
}

.review-info {
  flex: 1;
}

.review-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.review-rating {
  display: flex;
  gap: 0.2rem;
}

.star {
  font-size: 1.2rem;
  color: #ddd;
}

.star--filled {
  color: #ffd700;
}

.review-content {
  line-height: 1.6;
}

.review-content p {
  margin: 0;
  color: #666;
  font-size: 0.95rem;
}

/* Color scheme variations */
.color-scheme-2 .review-card {
  background: rgba(255, 255, 255, 0.95);
}

.color-scheme-3 .review-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation support */
@media (prefers-reduced-motion: no-preference) {
  .scroll-trigger.animate--slide-in {
    animation: slide-in 0.6s ease forwards;
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .review-card {
    padding: 1.5rem;
  }
  
  .customer-reviews .title {
    margin-bottom: 2rem;
  }
  
  .review-header {
    margin-bottom: 1rem;
  }
  
  .review-avatar {
    width: 50px;
    height: 50px;
  }
  
  .review-name {
    font-size: 1rem;
  }
  
  .star {
    font-size: 1rem;
  }
}
